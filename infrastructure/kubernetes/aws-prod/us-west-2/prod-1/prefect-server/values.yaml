# Docs: https://artifacthub.io/packages/helm/prefect/prefect-server
namespaceOverride: prefect

prefect-server:
  namespaceOverride: prefect
  ingress:
    enabled: true
    # Left for future reference. We want to consolidate all internal endpoints into a single ALB
    #  extraHosts:
    #    - name: "prefect.prod.getunblocked.com"
    #      path: /prefect
    #    - name: "prefect.prod-1.us-west-2.prod.getunblocked.com"
    #      path: /prefect
    host:
      hostname: "prefect.prod.getunblocked.com"
      pathType: "Prefix"
    annotations:
      # Cert ARN is manually added once the AcmStack has been deployed
      alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-west-2:029574882031:certificate/6126acb3-5ab9-4826-8ee4-217552d8081c
      alb.ingress.kubernetes.io/group.name: prefect-internal
      alb.ingress.kubernetes.io/group.order: "20"
      alb.ingress.kubernetes.io/healthcheck-path: /api/health
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
      alb.ingress.kubernetes.io/load-balancer-attributes: access_logs.s3.enabled=false
      alb.ingress.kubernetes.io/scheme: internal
      alb.ingress.kubernetes.io/target-type: ip
      external-dns.alpha.kubernetes.io/hostname: prefect.prod-1.us-west-2.prod.getunblocked.com
      kubernetes.io/ingress.class: alb
  server:
    priorityClassName: high-priority
    # Left for future reference. We want to consolidate all internal endpoints into a single ALB
    #apiBasePath: "/prefect/api"
    resources:
      limits:
        cpu: "2"
        memory: "2Gi"
      requests:
        cpu: "2"
        memory: "2Gi"
    uiConfig:
      prefectUiApiUrl: "https://prefect.prod.getunblocked.com/api"
  backgroundServices:
    priorityClassName: high-priority
    resources:
      limits:
        cpu: "1"
        memory: "1Gi"
      requests:
        cpu: "1"
        memory: "1Gi"

# PostgreSQL subchart - default overrides
postgresql:
  primary:
    priorityClassName: high-priority
    # https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
    resources:
      requests:
        cpu: "1.0"
        memory: "8192Mi"
        ephemeral-storage: "50Mi"
      limits:
        cpu: "3.0"
        memory: "8192Mi"
        ephemeral-storage: "2Gi"

    ## persistence enables a PVC that stores the database between deployments. If making changes to the database deployment, this
    ## PVC will need to be deleted for database changes to take effect. This is especially notable when the authentication password
    ## changes on redeploys. This is disabled by default because we do not recommend using the subchart deployment for production deployments.
    persistence:
      # -- enable PostgreSQL Primary data persistence using PVC
      enabled: true
      size: 10000Gi
