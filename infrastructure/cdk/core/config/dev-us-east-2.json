{"awsEnvAccount": {"awsAccountID": "************", "rootRegion": "us-west-2", "targetRegion": "us-east-2", "awsOrgRootAccountID": "************", "notificationEmail": "<EMAIL>", "coldSite": true}, "cdkAppInfo": {"app": "core-aws-stack", "environment": "dev", "version": "0.0.0", "build": "0"}, "eksVPC": {"networkCIDR": "**********/16", "maxAZs": 3, "numNATGateways": 0, "allowICMPinDefaultSG": true, "enableVpcFlowLogs": false, "peeringTarget": []}, "eks": {"eksClusters": [{"enabled": false, "name": "dev-2", "version": "V1_31", "kubeProxyAddOnVersion": "v1.31.2-eksbuild.3", "enableAlbController": true, "createDefaultDenyPolicy": true, "hostedZoneId": "Z0423486DXLLTQB3SPFH", "enable_addon_falco": false, "enable_addon_grafana": false, "enable_addon_refinery": true, "enable_addon_priority_class": true, "enable_addon_cert_manager_csi": true, "enable_addon_kms_issuer": true, "enable_addon_external_rbac": true, "enable_addon_prefect_server": true, "enable_addon_prefect_workers": true, "enable_addon_prefect_external_rbac": true, "managedNodeGroup": [{"id": "dev-2-group2-c5a-xlarge", "minSize": 1, "maxSize": 2, "maxUnavailablePercentage": 30, "instanceType": "c5a.2xlarge", "diskSize": 250}], "additionalAllowedCIDRs": ["*********/8"]}], "serviceAccounts": [{"name": "adminwebservice", "customPolicies": ["base_service_permissions", "bedrock_list_invoke_rag", "dynamodb_read_rawIngestionData", "dynamodb_read_sensitiveLogCache", "lambda_invoke_functions", "s3_adminwebservice_read", "s3_jiraservice_read", "sagemaker_list_invoke_describe", "state_machine_start_stop_list_describe"]}, {"name": "apiservice", "customPolicies": ["base_service_permissions", "dynamodb_read_rawIngestionData"]}, {"name": "assetservice", "customPolicies": ["base_service_permissions", "s3_assetservice_read_write"]}, {"name": "authservice", "customPolicies": ["base_service_permissions"]}, {"name": "billingservice", "customPolicies": ["base_service_permissions", "s3_billingservice_read_write"]}, {"name": "ciservice", "customPolicies": ["base_service_permissions", "bedrock_list_invoke_rag", "dynamodb_read_rawIngestionData"]}, {"name": "dataservice", "customPolicies": ["base_service_permissions", "s3_dataservice_read_write"]}, {"name": "embeddingservice", "customPolicies": ["rds_read_write", "activemq_secret_read", "redis_secret_read", "dynamodb_read_write_rawIngestionData"]}, {"name": "encryptionservice", "customPolicies": ["rds_read_write"]}, {"name": "indexservice", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-asana", "customPolicies": ["base_service_permissions", "dynamodb_read_write_rawIngestionData", "s3_confluenceservice_read_write"]}, {"name": "ingest-coda", "customPolicies": ["base_service_permissions", "dynamodb_read_write_rawIngestionData", "s3_codaservice_read_write"]}, {"name": "ingest-confluence", "customPolicies": ["base_service_permissions", "dynamodb_read_write_rawIngestionData", "s3_confluenceservice_read_write"]}, {"name": "ingest-google", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-jira", "customPolicies": ["base_service_permissions", "dynamodb_read_write_rawIngestionData", "s3_jiraservice_read_write"]}, {"name": "ingest-linear", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-microsoft-teams", "customPolicies": ["base_service_permissions", "dynamodb_read_write_rawIngestionData", "s3_confluenceservice_read_write"]}, {"name": "ingest-notion", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-slack", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-stackoverflow", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-web", "customPolicies": ["base_service_permissions"]}, {"name": "jiraservice", "customPolicies": ["base_service_permissions"]}, {"name": "maintenanceservice", "customPolicies": ["base_service_permissions"]}, {"name": "mermaidservice", "customPolicies": ["redis_secret_read"]}, {"name": "mlrouterservice", "customPolicies": ["base_service_permissions", "dynamodb_read_write_mlRouterWebhookEventStore"]}, {"name": "notificationservice", "customPolicies": ["base_service_permissions"]}, {"name": "prefect-job", "namespace": "prefect", "createNamespace": true, "customPolicies": ["s3_prefect_job_read_write"]}, {"name": "proxy-provider", "customPolicies": ["base_service_permissions", "proxy_provider_aws_iam_sts_assume_external", "s3_proxy_provider_read"]}, {"name": "publicapiservice", "customPolicies": ["base_service_permissions"]}, {"name": "pusherservice", "customPolicies": ["base_service_permissions"]}, {"name": "queueservice", "customPolicies": ["activemq_secret_read"]}, {"name": "scmservice", "customPolicies": ["base_service_permissions", "s3_scmservice_read_write", "state_machine_start_stop_list_describe"]}, {"name": "searchservice", "customPolicies": ["base_service_permissions", "bedrock_list_invoke_rag", "dynamodb_read_rawIngestionData", "lambda_invoke_functions", "s3_searchservice_read"]}, {"name": "secretservice", "customPolicies": ["rds_read_write", "kms_secretservice_decrypt_encrypt_genkey"]}, {"name": "slackservice", "customPolicies": ["base_service_permissions", "bedrock_list_invoke_rag", "dynamodb_read_rawIngestionData", "lambda_invoke_functions", "s3_slackservice_read"]}, {"name": "sourcecodeservice", "customPolicies": ["base_service_permissions", "s3_sourcecodeservice_read_write", "state_machine_start_stop_list_describe"]}, {"name": "summarizationservice", "customPolicies": ["base_service_permissions"]}, {"name": "telemetryservice", "customPolicies": []}, {"name": "topicservice", "customPolicies": ["base_service_permissions", "s3_topicservice_read_write", "sagemaker_list_invoke_describe", "state_machine_start_stop_list_describe"]}, {"name": "webhookservice", "customPolicies": ["activemq_secret_read", "redis_secret_read"]}]}, "coreVPC": {"networkCIDR": "**********/16", "maxAZs": 3, "numNATGateways": 0, "allowICMPinDefaultSG": true, "peeringTarget": []}, "dns": {"route53HostedZoneID": "Z05916033T74B60YGQ2P2", "route53HostedZoneName": "dev.getunblocked.com", "aliasRecords": [], "cnameRecords": []}, "certRequests": [{"name": "alb.dev.getunblocked.com", "domainName": "alb.dev.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": true}, {"name": "admin.dev.getunblocked.com", "domainName": "admin.dev.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": true}, {"name": "prefect.dev.getunblocked.com", "domainName": "prefect.dev.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": true}, {"name": "ml.api.dev.getunblocked.com", "domainName": "ml.api.dev.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": true}, {"name": "ml.alb.dev.getunblocked.com", "domainName": "ml.alb.dev.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": true}], "rdsPostgres": {"databases": [{"cdkResourceName": "main-app", "dbUsername": "postgres", "isPrimary": true, "allowMajorVersionUpgrade": true, "autoMinorVersionUpgrade": true, "enablePerformanceInsights": true, "auroraPostgresFullVersion": "16.6", "auroraPostgresMajorVersion": "16", "ioOptimized": true, "writerInstanceClass": "t4g", "writerInstanceSize": "medium", "readerInstanceClass": "t4g", "readerInstanceSize": "medium", "readerInstanceCount": 1, "transactionTimeoutSeconds": 60, "statementTimeoutSeconds": 180, "workMemMB": 4, "snapshotIdentifier": "arn:aws:rds:us-east-2:************:cluster-snapshot:rds-backup-dr-25-05-2025", "disableS3Backup": true, "UseExistingCreateS3CrossRegionBackupBucket": true}], "disableS3Backup": true, "UseExistingCreateS3CrossRegionBackupBucket": true, "endpointCNAMEPrefix": "e2dd6b30-8d65-4ea0-93e6-82b6690378af", "backupRetentionDays": 7, "additionalAllowedCIDRs": ["**********/16", "***********/16"]}, "dynamodb": {"replicaRegions": [], "enableEncryption": false, "tables": [{"name": "rawIngestionData", "partitionKey": {"name": "id", "type": "BINARY"}, "globalIndexes": [{"indexName": "tenantIndex", "partitionKey": {"name": "tenant", "type": "BINARY"}, "projectionType": "KEYS_ONLY"}, {"indexName": "collectionIndex", "partitionKey": {"name": "collection", "type": "BINARY"}, "projectionType": "KEYS_ONLY"}, {"indexName": "groupIndex", "partitionKey": {"name": "group", "type": "BINARY"}, "projectionType": "KEYS_ONLY"}], "removalPolicy": "RETAIN_ON_UPDATE_OR_DELETE", "enableEncryption": true}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "partitionKey": {"name": "id", "type": "BINARY"}, "timeToLiveAttribute": "ttl", "removalPolicy": "RETAIN_ON_UPDATE_OR_DELETE", "enableEncryption": true}, {"name": "mlRouterWebhookEventStore", "partitionKey": {"name": "routerId", "type": "BINARY"}, "sortKey": {"name": "id", "type": "BINARY"}, "timeToLiveAttribute": "ttl", "removalPolicy": "RETAIN_ON_UPDATE_OR_DELETE", "enableEncryption": true}]}, "redis": {"cacheNodeType": "cache.t4g.micro", "clusterName": "primary-dev-redis", "autoMinorVersionUpgrade": true, "automaticFailoverEnabled": true, "clusterEnabled": true, "numNodeGroups": 1, "replicasPerNodeGroup": 1, "cacheParameterGroupName": "default.redis7.cluster.on", "multiAzEnabled": true, "users": [{"userName": "default", "accessString": "off", "isDefaultUser": true, "secrets": [{"secretName": "redis-default-password-1", "excludeCharacters": "/@\"'", "excludePunctuation": true, "passwordLength": 20}, {"secretName": "redis-default-password-2", "excludeCharacters": "/@\"'", "excludePunctuation": true, "passwordLength": 20}]}, {"userName": "unblocked", "accessString": "on ~* +@all", "secrets": [{"secretName": "redis-unblocked-password-1", "excludeCharacters": "/@\"'", "excludePunctuation": true, "passwordLength": 20}, {"secretName": "redis-unblocked-password-2", "excludeCharacters": "/@\"'", "excludePunctuation": true, "passwordLength": 20}]}], "additionalAllowedCIDRs": ["**********/16", "***********/16"]}, "activeMQMesh": {"meshCount": 2, "configurationFilePath": "assets/activemq/activemq-mesh.template.xml", "baseBrokerName": "primary-dev-activemq-mesh", "users": [{"userName": "unblocked", "consoleAccess": true, "secret": {"secretName": "activemq-unblocked-password-2", "excludeCharacters": " ;+%{}@'\"`/\\#", "excludePunctuation": true, "passwordLength": 20}}], "activeMQMeshNode": {"hostInstanceType": "mq.m5.large", "autoMinorVersionUpgrade": true, "deploymentMode": "ACTIVE_STANDBY_MULTI_AZ", "engineVersion": "5.18"}, "additionalAllowedCIDRs": ["**********/16", "***********/16"]}, "ecr": {"sourceAccount": "************", "replicaRepoMaxImages": 20}, "iam": {"createK8sDeployerRole": false, "createK8sReaderRole": false, "createCloudWatchReadOnlyRole": false, "createCrossAccountAdminReadOnlyRole": false, "createKinesisRole": false, "createCrossAccountS3ReadOnlyRole": false}, "httpProxy": {"enable": false, "autoScalingGroup": {"name": "HttpProxyASG", "maxCapacity": 2, "minCapacity": 1, "desiredCapacity": 1, "instanceType": "t4g.large", "machineAmiType": "ARM64", "launchTemplate": {"name": "HttpProxyLaunchTemplate", "instanceType": "t4g.large", "associatePublicIpAddress": true, "keyPairName": "HttpProxyKeypair", "machineAmiType": "ARM64", "blockDevices": [{"deviceName": "/dev/xvda", "volume": {"deleteOnTermination": true, "sizeInGB": 200, "volumeType": "gp2"}}], "userData": {"type": "linux", "userDataParts": [{"userDataFilePath": "assets/userdata/http-proxy-node.sh"}]}, "securityGroup": {"name": "HttpProxyLaunchTemplateSG", "description": "Security group for HTTP Proxy Cache Auto Scaling Group", "allowAllOutbound": true, "ingressRules": [{"peerRule": {"peerType": "ipv4Cidr", "ipv4Cidr": "***********/16"}, "portRule": {"port": 22, "portType": "tcp"}}, {"peerRule": {"peerType": "ipv4Cidr", "ipv4Cidr": "**********/16"}, "portRule": {"port": 3128, "portType": "tcp"}}]}}}}, "s3Buckets": [{"name": "external-alb-access-logs-dev", "addAccessLogPolicy": true, "enableVersioning": true, "enableLogRetentionRule": true}, {"name": "integration-data", "bucketTier": {"name": "IntelligentTieringL1"}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}, {"name": "pipeline-sandbox", "bucketTier": {"name": "IntelligentTieringL1"}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}], "secrets": [{"secretName": "openai-unblocked-token", "excludeCharacters": "/@\"'", "excludePunctuation": true, "passwordLength": 20}], "customerAssets": {"standardS3Bucket": {"name": "user-assets", "bucketTier": {"name": "IntelligentTieringL1", "archiveAccessTierTimeDays": 90, "deepArchiveAccessTierTimeDays": 180}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "transferAcceleration": true, "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}}, "efsFileSystems": [], "sagemakerMachineLearningModels": {"disable": true, "apiGateway": {"name": "ml-api_gw", "apiCertName": "ml.api.dev.getunblocked.com", "restApiName": "ml-api", "enableLogs": true}, "models": [{"modelName": "teknium/OpenHermes-2.5-Mistral-7B", "modelTask": "text-generation", "instanceType": "ml.g5.2xlarge", "modelTriggerLambda": {"lambdaPath": "assets/lambda/sagemaker-endpoint-trigger", "functionName": "MLMistral7BChatSagemakerTrigger", "handler": "app.handler"}, "modelDeepLearningContainer": {"repositoryName": "huggingface-pytorch-tgi-inference", "tag": "2.0.1-tgi1.1.0-gpu-py39-cu118-ubuntu20.04"}, "modelToken": {"name": "HUGGING_FACE_HUB_TOKEN", "secretName": "hf-hub-token"}, "modelEnvironment": {"SM_NUM_GPUS": "1", "MAX_INPUT_LENGTH": "2048", "MAX_TOTAL_TOKENS": "4096", "MAX_BATCH_TOTAL_TOKENS": "4096"}, "modelAutoScale": {"maxRequestsPerSecond": 4, "maxCapacity": 10, "scaleInCoolDownSeconds": 30, "scaleOutCoolDownSeconds": 600, "safetyFactor": 0.05}, "apiGatewayEndpoint": {"name": "ml-mistral-7b-chat-sagemaker-endpoint", "path": "transformers/mistral-7b-chat"}}, {"modelName": "hkunlp/instructor-large", "modelTask": "feature-extraction", "modelDockerImage": {"name": "instructor-large-embedding-model", "directory": "assets/image/machine-learning/instructor-embedding-model-image", "secretBuildArgs": [{"name": "JFROG_PASSWORD", "secretName": "JFROG_PASSWORD"}]}, "instanceType": "ml.g4dn.xlarge", "modelTriggerLambda": {"lambdaPath": "assets/lambda/sagemaker-endpoint-trigger", "functionName": "MLInstructorEmbeddingSagemakerTrigger", "handler": "app.handler"}, "modelAutoScale": {"maxRequestsPerSecond": 5, "maxCapacity": 5, "scaleInCoolDownSeconds": 10, "scaleOutCoolDownSeconds": 10, "safetyFactor": 0.25}, "apiGatewayEndpoint": {"name": "ml-instructor-sagemaker-endpoint", "path": "embeddings/instructor"}}, {"modelName": "hkunlp/instructor-xl", "modelTask": "feature-extraction", "modelDockerImage": {"name": "instructor-xl-embedding-model", "directory": "assets/image/machine-learning/instructor-embedding-model-image", "secretBuildArgs": [{"name": "JFROG_PASSWORD", "secretName": "JFROG_PASSWORD"}]}, "instanceType": "ml.g4dn.xlarge", "modelTriggerLambda": {"lambdaPath": "assets/lambda/sagemaker-endpoint-trigger", "functionName": "MLInstructorXLEmbeddingSagemakerTrigger", "handler": "app.handler"}, "modelAutoScale": {"maxRequestsPerSecond": 5, "maxCapacity": 5, "scaleInCoolDownSeconds": 10, "scaleOutCoolDownSeconds": 10, "safetyFactor": 0.25}, "modelEnvironment": {"INSTRUCTOR_MODEL": "hkunlp/instructor-xl"}, "apiGatewayEndpoint": {"name": "ml-instructor-xl-sagemaker-endpoint", "path": "embeddings/instructor-xl"}}, {"modelName": "intfloat/e5-mistral-7b-instruct", "modelTask": "feature-extraction", "modelDockerImage": {"name": "e5-mistral-embedding-model", "directory": "assets/image/machine-learning/e5-mistral-embedding-model-image", "secretBuildArgs": [{"name": "JFROG_PASSWORD", "secretName": "JFROG_PASSWORD"}]}, "instanceType": "ml.g5.2xlarge", "modelTriggerLambda": {"lambdaPath": "assets/lambda/sagemaker-endpoint-trigger", "functionName": "MLE5MistralEmbeddingSagemakerTrigger", "handler": "app.handler"}, "modelAutoScale": {"maxRequestsPerSecond": 2, "minCapacity": 2, "maxCapacity": 20, "scaleInCoolDownSeconds": 10, "scaleOutCoolDownSeconds": 360, "safetyFactor": 0.15}, "apiGatewayEndpoint": {"name": "ml-e5-mistral-sagemaker-endpoint", "path": "embeddings/e5-mistral"}}, {"modelName": "unblocked/topic-mapping", "modelTask": "topic-mapping", "modelDockerImage": {"name": "unblocked-topic-mapping-model", "directory": "assets/image/machine-learning/topic-mapping-model-image", "secretBuildArgs": [{"name": "JFROG_PASSWORD", "secretName": "JFROG_PASSWORD"}]}, "modelTriggerLambda": {"lambdaPath": "assets/lambda/sagemaker-endpoint-trigger", "functionName": "MLTopicMappingSagemakerTrigger", "handler": "app.handler"}, "instanceType": "ml.r5.2xlarge", "modelAutoScale": {"maxRequestsPerSecond": 5, "maxCapacity": 10, "scaleInCoolDownSeconds": 10, "scaleOutCoolDownSeconds": 120, "safetyFactor": 0.05}, "apiGatewayEndpoint": {"name": "ml-topic-mapping-sagemaker-endpoint", "path": "models/topic-mapping"}}]}}