{"awsEnvAccount": {"awsAccountID": "************", "rootRegion": "us-west-2", "targetRegion": "us-west-2", "awsOrgRootAccountID": "************", "notificationEmail": "<EMAIL>"}, "cdkAppInfo": {"app": "core-aws-stack", "environment": "prod", "version": "0.0.0", "build": "0"}, "eksVPC": {"networkCIDR": "********/16", "maxAZs": 3, "numNATGateways": 3, "allowICMPinDefaultSG": true, "peeringTarget": [{"id": "vpc-052a14fb3aa70e776", "name": "VPN", "networkCIDR": "***********/16", "accountID": "************", "region": "us-west-2", "allowICMPinDefaultSG": true, "targetIsPeeringConn": false, "peeringRole": "arn:aws:iam::************:role/CrossAccountPeeringAccepterRole-************"}], "transitGatewayId": "tgw-08ce9ea2424be06e2", "transitGatewayTargetCIDRs": ["*********/16"]}, "eks": {"eksClusters": [{"enabled": true, "name": "prod-1", "version": "V1_33", "kubeProxyAddOnVersion": "v1.33.0-eksbuild.2", "hostedZoneId": "Z046326532BV8FIH2UJE2", "enableAlbController": true, "enable_addon_priority_class": true, "createDefaultDenyPolicy": true, "createExternalSecretsClusterSecretStore": true, "enable_addon_cert_manager_csi": true, "enable_addon_kms_issuer": true, "enable_addon_external_rbac": true, "enable_addon_prefect_external_rbac": true, "enable_addon_falco": true, "enable_addon_grafana": true, "enable_addon_refinery": true, "managedNodeGroup": [{"id": "prod-1-group1-c5a-4xlarge", "minSize": 10, "maxSize": 22, "maxUnavailablePercentage": 30, "instanceType": "c5a.4xlarge", "amiType": "AL2023_X86_64_STANDARD"}, {"id": "prod-1-workers-2-c5a-4xlarge-2", "minSize": 5, "maxSize": 8, "maxUnavailablePercentage": 30, "instanceType": "c5a.4xlarge", "diskSize": 1000, "amiType": "AL2023_X86_64_STANDARD", "taints": [{"effect": "NO_SCHEDULE", "key": "node-class", "value": "worker"}]}]}], "serviceAccounts": [{"name": "adminwebservice", "customPolicies": ["base_service_permissions", "bedrock_list_invoke_rag", "dynamodb_read_rawIngestionData", "dynamodb_read_sensitiveLogCache", "lambda_invoke_functions", "s3_adminwebservice_read", "s3_jiraservice_read", "sagemaker_list_invoke_describe", "state_machine_start_stop_list_describe", "dynamodb_read_write_ciTriageCache"]}, {"name": "apiservice", "customPolicies": ["base_service_permissions", "dynamodb_read_rawIngestionData"]}, {"name": "assetservice", "customPolicies": ["base_service_permissions", "s3_assetservice_read_write"]}, {"name": "authservice", "customPolicies": ["base_service_permissions"]}, {"name": "billingservice", "customPolicies": ["base_service_permissions", "s3_billingservice_read_write"]}, {"name": "ciservice", "customPolicies": ["base_service_permissions", "bedrock_list_invoke_rag", "dynamodb_read_rawIngestionData", "dynamodb_read_write_ciTriageCache"]}, {"name": "dataservice", "customPolicies": ["base_service_permissions", "s3_dataservice_read_write"]}, {"name": "embeddingservice", "customPolicies": ["rds_read_write", "activemq_secret_read", "redis_secret_read", "dynamodb_read_write_rawIngestionData"]}, {"name": "encryptionservice", "customPolicies": ["rds_read_write"]}, {"name": "indexservice", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-asana", "customPolicies": ["base_service_permissions", "dynamodb_read_write_rawIngestionData", "s3_confluenceservice_read_write"]}, {"name": "ingest-coda", "customPolicies": ["base_service_permissions", "dynamodb_read_write_rawIngestionData", "s3_codaservice_read_write"]}, {"name": "ingest-confluence", "customPolicies": ["base_service_permissions", "dynamodb_read_write_rawIngestionData", "s3_confluenceservice_read_write"]}, {"name": "ingest-google", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-jira", "customPolicies": ["base_service_permissions", "dynamodb_read_write_rawIngestionData", "s3_jiraservice_read_write"]}, {"name": "ingest-linear", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-microsoft-teams", "customPolicies": ["base_service_permissions", "dynamodb_read_write_rawIngestionData", "s3_confluenceservice_read_write"]}, {"name": "ingest-notion", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-slack", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-stackoverflow", "customPolicies": ["base_service_permissions"]}, {"name": "ingest-web", "customPolicies": ["base_service_permissions"]}, {"name": "jiraservice", "customPolicies": ["base_service_permissions"]}, {"name": "maintenanceservice", "customPolicies": ["base_service_permissions"]}, {"name": "mcpser<PERSON>", "customPolicies": ["base_service_permissions", "dynamodb_read_rawIngestionData"]}, {"name": "mermaidservice", "customPolicies": ["redis_secret_read"]}, {"name": "mlrouterservice", "customPolicies": ["base_service_permissions", "dynamodb_read_write_mlRouterWebhookEventStore"]}, {"name": "notificationservice", "customPolicies": ["base_service_permissions"]}, {"name": "prefect-job", "namespace": "prefect", "createNamespace": true, "customPolicies": ["s3_prefect_job_read_write"]}, {"name": "proxy-provider", "customPolicies": ["base_service_permissions", "proxy_provider_aws_iam_sts_assume_external", "s3_proxy_provider_read"]}, {"name": "publicapiservice", "customPolicies": ["base_service_permissions"]}, {"name": "pusherservice", "customPolicies": ["base_service_permissions"]}, {"name": "queueservice", "customPolicies": ["activemq_secret_read"]}, {"name": "review", "customPolicies": ["base_service_permissions", "bedrock_list_invoke_rag"]}, {"name": "scmservice", "customPolicies": ["base_service_permissions", "s3_scmservice_read_write", "state_machine_start_stop_list_describe"]}, {"name": "searchservice", "customPolicies": ["base_service_permissions", "bedrock_list_invoke_rag", "dynamodb_read_rawIngestionData", "lambda_invoke_functions", "s3_searchservice_read"]}, {"name": "secretservice", "customPolicies": ["rds_read_write", "kms_secretservice_decrypt_encrypt_genkey"]}, {"name": "slackservice", "customPolicies": ["base_service_permissions", "bedrock_list_invoke_rag", "dynamodb_read_rawIngestionData", "lambda_invoke_functions", "s3_slackservice_read"]}, {"name": "sourcecodeservice", "customPolicies": ["base_service_permissions", "s3_sourcecodeservice_read_write", "state_machine_start_stop_list_describe"]}, {"name": "summarizationservice", "customPolicies": ["base_service_permissions"]}, {"name": "telemetryservice", "customPolicies": []}, {"name": "topicservice", "customPolicies": ["base_service_permissions", "s3_topicservice_read_write", "sagemaker_list_invoke_describe", "state_machine_start_stop_list_describe"]}, {"name": "webhookservice", "customPolicies": ["activemq_secret_read", "redis_secret_read"]}]}, "coreVPC": {"networkCIDR": "********/16", "maxAZs": 3, "numNATGateways": 1, "allowICMPinDefaultSG": true, "peeringTarget": [{"id": "vpc-052a14fb3aa70e776", "name": "VPN", "networkCIDR": "***********/16", "accountID": "************", "region": "us-west-2", "allowICMPinDefaultSG": true, "targetIsPeeringConn": false, "peeringRole": "arn:aws:iam::************:role/CrossAccountPeeringAccepterRole-************"}], "transitGatewayId": "tgw-08ce9ea2424be06e2", "transitGatewayTargetCIDRs": ["*********/16"], "defaultSGMongoAtlasAllowedCIDRs": ["********/16", "***********/16"]}, "dns": {"route53HostedZoneID": "Z06222132KYCTR0G03TIK", "route53HostedZoneName": "prod.getunblocked.com", "aliasRecords": [], "cnameRecords": []}, "certRequests": [{"name": "alb.prod.getunblocked.com", "domainName": "alb.prod.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": false}, {"name": "admin.prod.getunblocked.com", "domainName": "admin.prod.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": false}, {"name": "prefect.prod.getunblocked.com", "domainName": "prefect.prod.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": false}, {"name": "temporal.prod.getunblocked.com", "domainName": "temporal.prod.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": false}, {"name": "ml.api.prod.getunblocked.com", "domainName": "ml.api.prod.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": false}, {"name": "ml.alb.prod.getunblocked.com", "domainName": "ml.alb.prod.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": false}, {"name": "ml.pipeline.alb.prod.getunblocked.com", "domainName": "ml.pipeline.alb.prod.getunblocked.com", "addRegionWildcardAsSan": false, "useEmailValidation": false}], "rdsPostgres": {"databases": [{"cdkResourceName": "main-app", "dbUsername": "postgres", "isPrimary": true, "allowMajorVersionUpgrade": true, "autoMinorVersionUpgrade": true, "enhancedMonitoringInterval": 60, "enablePerformanceInsights": true, "performanceInsightRetention": 31, "auroraPostgresFullVersion": "16.4", "auroraPostgresMajorVersion": "16", "ioOptimized": true, "writerInstanceClass": "r6g", "writerInstanceSize": "4xlarge", "readerInstanceClass": "r6g", "readerInstanceSize": "2xlarge", "readerInstanceCount": 1, "transactionTimeoutSeconds": 45, "statementTimeoutSeconds": 120, "workMemMB": 16}], "endpointCNAMEPrefix": "30512161-3f48-498b-8583-bc02648bd079", "s3BackupReplicationTargetRegion": "us-east-2", "s3BackupReplicationTargetKMSKeyArn": "arn:aws:kms:us-east-2:************:key/8fddbf7e-697b-4429-ae14-df6c7a1480af", "additionalAllowedCIDRs": ["*********/16", "********/16", "***********/16"]}, "auroraServerless": {"databases": [{"cdkResourceName": "temporal-aurora-serverless", "dbUsername": "postgres", "isPrimary": true, "allowMajorVersionUpgrade": false, "autoMinorVersionUpgrade": true, "enablePerformanceInsights": true, "performanceInsightRetention": 31, "enhancedMonitoringInterval": 0, "auroraPostgresFullVersion": "16.4", "auroraPostgresMajorVersion": "16", "minCapacity": 2, "maxCapacity": 24.0, "deletionProtection": true, "enableBackup": true}], "useExistingS3BackupBucket": false, "additionalAllowedCIDRs": ["********/16", "***********/16"], "disableS3Backup": true, "backupRetentionDays": 1, "endpointCNAMEPrefix": "minimal-aurora-prod", "enableCloudWatchAlarms": true, "enableSNSNotifications": true}, "dynamodb": {"replicaRegions": [], "deletionProtection": true, "tables": [{"name": "rawIngestionData", "partitionKey": {"name": "id", "type": "BINARY"}, "globalIndexes": [{"indexName": "tenantIndex", "partitionKey": {"name": "tenant", "type": "BINARY"}, "projectionType": "KEYS_ONLY"}, {"indexName": "collectionIndex", "partitionKey": {"name": "collection", "type": "BINARY"}, "projectionType": "KEYS_ONLY"}, {"indexName": "groupIndex", "partitionKey": {"name": "group", "type": "BINARY"}, "projectionType": "KEYS_ONLY"}], "removalPolicy": "RETAIN_ON_UPDATE_OR_DELETE", "enableEncryption": true}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "partitionKey": {"name": "id", "type": "BINARY"}, "timeToLiveAttribute": "ttl", "removalPolicy": "RETAIN_ON_UPDATE_OR_DELETE", "enableEncryption": true}, {"name": "ciTriageCache", "partitionKey": {"name": "id", "type": "BINARY"}, "timeToLiveAttribute": "ttl", "removalPolicy": "RETAIN_ON_UPDATE_OR_DELETE", "enableEncryption": true}, {"name": "mlRouterWebhookEventStore", "partitionKey": {"name": "routerId", "type": "BINARY"}, "sortKey": {"name": "id", "type": "BINARY"}, "timeToLiveAttribute": "ttl", "removalPolicy": "RETAIN_ON_UPDATE_OR_DELETE", "enableEncryption": true}]}, "redis": {"cacheNodeType": "cache.t4g.small", "clusterName": "primary-prod-redis", "autoMinorVersionUpgrade": true, "automaticFailoverEnabled": true, "clusterEnabled": true, "numNodeGroups": 1, "replicasPerNodeGroup": 1, "cacheParameterGroupName": "default.redis7.cluster.on", "multiAzEnabled": true, "users": [{"userName": "default", "accessString": "off", "isDefaultUser": true, "secrets": [{"secretName": "redis-default-password-1", "excludeCharacters": "/@\"'", "excludePunctuation": true, "passwordLength": 20}, {"secretName": "redis-default-password-2", "excludeCharacters": "/@\"'", "excludePunctuation": true, "passwordLength": 20}]}, {"userName": "unblocked", "accessString": "on ~* +@all", "secrets": [{"secretName": "redis-unblocked-password-1", "excludeCharacters": "/@\"'", "excludePunctuation": true, "passwordLength": 20}, {"secretName": "redis-unblocked-password-2", "excludeCharacters": "/@\"'", "excludePunctuation": true, "passwordLength": 20}]}], "additionalAllowedCIDRs": ["*********/16", "********/16", "***********/16"]}, "activeMQMesh": {"meshCount": 2, "configurationFilePath": "assets/activemq/activemq-mesh.template.xml", "baseBrokerName": "primary-prod-activemq-mesh", "users": [{"userName": "unblocked", "consoleAccess": true, "secret": {"secretName": "activemq-unblocked-password-2", "excludeCharacters": " ;+%{}@'\"`/\\#", "excludePunctuation": true, "passwordLength": 20}}], "activeMQMeshNode": {"hostInstanceType": "mq.m5.xlarge", "autoMinorVersionUpgrade": true, "deploymentMode": "ACTIVE_STANDBY_MULTI_AZ", "engineVersion": "5.18"}, "additionalAllowedCIDRs": ["*********/16", "********/16", "***********/16"]}, "ecr": {"sourceAccount": "************", "cleanupLambda": {"lambdaPath": "assets/lambda/ecr-cleanup-lambda", "functionName": "ECRCleanupLambda", "handler": "main.handler", "runtime": "PYTHON_3_9", "environment": {"REGION": "us-west-2", "DRYRUN": "false", "IMAGES_TO_KEEP": "100"}, "timeoutInMin": 2, "region": "us-west-2"}}, "ses": {"configurationSet": {"name": "Unblocked-SES-ConfigurationSet", "s3BucketName": "unblocked-ses-kinesis", "iamRoleName": "Unblocked-SES-ConfigurationSet-Role", "kinesisName": "Unblocked-SES-Kinesis-DataStream", "kinesisIamRoleName": "Unblocked-SES-Kinesis-Iam-Role"}}, "iam": {"createK8sDeployerRole": true, "createK8sReaderRole": true, "createCloudWatchReadOnlyRole": true, "createCrossAccountAdminReadOnlyRole": true, "createCrossAccountS3ReadOnlyRole": true}, "httpProxy": {"autoScalingGroup": {"name": "HttpProxyASG", "maxCapacity": 3, "minCapacity": 1, "desiredCapacity": 2, "instanceType": "m5a.large", "machineAmiType": "ARM64", "launchTemplate": {"name": "HttpProxyLaunchTemplate", "instanceType": "m5a.large", "associatePublicIpAddress": true, "keyPairName": "HttpProxyKeypair", "machineAmiType": "Standard", "userData": {"type": "linux", "userDataParts": [{"userDataFilePath": "assets/userdata/http-proxy-node.sh"}]}, "securityGroup": {"name": "HttpProxyLaunchTemplateSG", "description": "Security group for HTTP Proxy Cache Auto Scaling Group", "allowAllOutbound": true, "ingressRules": [{"peerRule": {"peerType": "ipv4Cidr", "ipv4Cidr": "********/16"}, "portRule": {"port": 3128, "portType": "tcp"}}, {"peerRule": {"peerType": "ipv4Cidr", "ipv4Cidr": "********/16"}, "portRule": {"port": 3128, "portType": "tcp"}}]}}}}, "customerHTTPProxy": [{"name": "workday", "enable": true, "elasticIps": [{"name": "primary", "ip": "************", "id": "eipalloc-0b6fdb4b05ed3f05a"}, {"name": "secondary", "ip": "************", "id": "eipalloc-02a1c919825b18232"}], "allowedIngressCIDRs": ["********/16", "***********/16"], "instanceType": "c5a.large", "userDataScriptPath": "assets/userdata/http-proxy-node.sh", "blackoutWindow": ["FRI:4:00-6:30", "SUN:02:00-07:30"]}], "sqsDeadLetterQueues": [], "sqsQueues": [], "s3Buckets": [{"name": "external-alb-access-logs-prod", "addAccessLogPolicy": true, "excludeEnvSuffix": true, "enableVersioning": true, "enableLogRetentionRule": true}, {"name": "internal-alb-access-logs-prod", "addAccessLogPolicy": true, "excludeEnvSuffix": true, "enableVersioning": true, "enableLogRetentionRule": true}, {"name": "integration-data", "bucketTier": {"name": "IntelligentTieringL1"}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}, {"name": "pipeline-sandbox", "bucketTier": {"name": "IntelligentTieringL1"}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": true}], "customerAssets": {"standardS3Bucket": {"name": "user-assets", "bucketTier": {"name": "IntelligentTieringL1", "archiveAccessTierTimeDays": 90, "deepArchiveAccessTierTimeDays": 180}, "bucketKeyEnabled": true, "bucketEncryptionType": "KMS", "transferAcceleration": true, "autoDeleteObjects": false, "enableVersioning": true, "enableUserdataRetentionRule": false}}, "pullRequestDataPipeline": {"name": "PullRequestDataPipeline", "stateMachine": {"name": "PullRequestDataPipelineStateMachine", "endpointName": "pull-request-data-pipeline", "assetBucket": {"name": "pull-request-data-pipeline-asset", "enableVersioning": true, "enableUserdataRetentionRule": true}, "glueStage": {"name": "PreProcessPullRequestGlueStage", "stage": "Glue", "retry": {"maxAttempts": 3, "intervalInSeconds": 10, "backOffRate": 2, "errors": ["Glue.ConcurrentRunsExceededException", "States.Timeout"]}, "timeoutInMin": 30, "glueJobFilePath": "assets/glue/pull-request-data-pipeline/preprocess/src/glue_etl.py", "glueJobNumberOfWorkers": 16, "glueJobMaxConcurrentRuns": 30, "glueArgumentsJsonPath": "$.PreProcessGlue", "glueResultJsonPath": "$.Result"}, "preProcessStages": [{"name": "PullRequestSummariesStage", "stage": "PreProcess", "enableVPCNetworking": true, "timeoutInMin": 720, "retry": {"maxAttempts": 3, "intervalInSeconds": 10, "backOffRate": 2, "errors": ["InternalServerError", "ThrottlingException", "SageMaker.AmazonSageMakerException"]}, "processDockerImage": {"name": "pull-request-data-pipeline-summary", "directory": "assets/image/pull-request-data-pipeline/summaries-processor-image", "buildArgs": {"ENVIRONMENT": "prod"}, "secretBuildArgs": [{"name": "JFROG_PASSWORD", "secretName": "JFROG_PASSWORD"}]}, "processInstanceType": "m5.4xlarge", "processJobNameJsonPath": "$.PullRequestSummaries.ProcessingJobName", "processS3Inputs": [{"inputName": "input", "inputJsonPath": "$.PullRequestSummaries.ProcessInput", "contentType": "application/jsonlines"}], "processS3Outputs": [{"outputName": "output", "outputJsonPath": "$.PullRequestSummaries.ProcessOutput"}], "processResultJsonPath": "$.Result.PullRequestSummaries", "processEnvironmentJsonPath": "$.PullRequestSummaries.ProcessEnvironment", "processCompletionLambdaStage": {"name": "PullRequestSummariesCompletionLambdaStage", "stage": "ProcessCompletionStage", "enableVPCNetworking": true, "retry": {"maxAttempts": 5, "intervalInSeconds": 120, "backOffRate": 5, "errors": ["Lambda.ResourceNotReadyException", "States.TaskFailed"]}, "timeoutInMin": 5, "lambdaResultJsonPath": "$.Result.PullRequestSummariesCompletionLambdaStage", "lambda": {"containerPath": "assets/lambda/data-pipeline/trigger-process-completion", "functionName": "PullRequestSummariesTriggerCompletion", "handler": "handler.handle", "environment": {"PROCESS_OUTPUT_JSON_PATH": "$.PullRequestSummaries.ProcessOutput", "AMQ_HOST": "b-3dc4e244-255f-4760-8f52-e9f5665be9a2-1.mq.us-west-2.amazonaws.com,b-3dc4e244-255f-4760-8f52-e9f5665be9a2-2.mq.us-west-2.amazonaws.com,b-c88f2792-dcdb-4845-a5e4-f293ed5f5e5e-1.mq.us-west-2.amazonaws.com,b-c88f2792-dcdb-4845-a5e4-f293ed5f5e5e-2.mq.us-west-2.amazonaws.com", "AMQ_USER": "unblocked", "AMQ_PASSWORD_SECRET_NAME": "activemq-unblocked-password-2", "AMQ_QUEUE": "/queue/pr_summaries_ingestion_completion", "AMQ_EVENT_TYPE": "PullRequestSummariesCompletionEvent"}}}}]}}, "codeIngestionDataPipeline": {"name": "CodeIngestionDataPipeline", "stateMachine": {"name": "CodeIngestionDataPipelineStateMachine", "endpointName": "code-ingestion-data-pipeline", "subPipelineStages": [{"name": "CodeIngestionStandardRunnerSubPipelineStage", "stage": "SubPipeline", "choice": {"name": "CodeIngestionStandardRunnerChoice", "operation": "Or", "conditions": [{"condition": {"type": "IsNotPresent", "IsNotPresent": true, "variable": "$.CodeIngestion.UseLargeRunner"}}, {"condition": {"type": "BooleanEquals", "BooleanEquals": false, "variable": "$.CodeIngestion.UseLargeRunner"}}]}, "preProcessStage": {"name": "CodeIngestionStage", "stage": "PreProcess", "enableVPCNetworking": true, "timeoutInMin": 1440, "retry": {"maxAttempts": 3, "intervalInSeconds": 10, "backOffRate": 2, "errors": ["InternalServerError", "ThrottlingException", "SageMaker.AmazonSageMakerException"]}, "catch": {"errors": ["States.TaskFailed"], "errorResultJsonPath": "$.Result.CodeIngestion.StandardRunner.Error", "catchStageName": "CodeIngestionStandardRunnerCompletionLambdaStage"}, "processDockerImage": {"name": "code-ingestion-data-pipeline-embeddings", "directory": "assets/image/code-ingestion-data-pipeline/embeddings-processor-image", "buildArgs": {"ENVIRONMENT": "prod"}, "secretBuildArgs": [{"name": "JFROG_PASSWORD", "secretName": "JFROG_PASSWORD"}]}, "processInstanceType": "g4dn.xlarge", "processJobNameJsonPath": "$.CodeIngestion.ProcessingJobName", "processS3Inputs": [], "processS3Outputs": [{"outputName": "output", "outputJsonPath": "$.CodeIngestion.ProcessOutput"}], "processResultJsonPath": "$.Result.CodeIngestion", "processEnvironmentJsonPath": "$.CodeIngestion.ProcessEnvironment", "processCompletionLambdaStage": {"name": "CodeIngestionStandardRunnerCompletionLambdaStage", "stage": "ProcessCompletionStage", "enableVPCNetworking": true, "retry": {"maxAttempts": 5, "intervalInSeconds": 120, "backOffRate": 5, "errors": ["Lambda.ResourceNotReadyException", "States.TaskFailed"]}, "timeoutInMin": 5, "lambdaResultJsonPath": "$.Result.CodeIngestionCompletionLambdaStage", "lambda": {"containerPath": "assets/lambda/data-pipeline/trigger-process-completion", "functionName": "CodeIngestionTriggerCompletion", "handler": "handler.handle", "environment": {"PROCESS_OUTPUT_JSON_PATH": "$.CodeIngestion.ProcessOutput", "PROCESS_ERROR_JSON_PATH": "$.Result.CodeIngestion.StandardRunner.Error", "AMQ_HOST": "b-3dc4e244-255f-4760-8f52-e9f5665be9a2-1.mq.us-west-2.amazonaws.com,b-3dc4e244-255f-4760-8f52-e9f5665be9a2-2.mq.us-west-2.amazonaws.com,b-c88f2792-dcdb-4845-a5e4-f293ed5f5e5e-1.mq.us-west-2.amazonaws.com,b-c88f2792-dcdb-4845-a5e4-f293ed5f5e5e-2.mq.us-west-2.amazonaws.com", "AMQ_USER": "unblocked", "AMQ_PASSWORD_SECRET_NAME": "activemq-unblocked-password-2", "AMQ_QUEUE": "/queue/source_code_events", "AMQ_EVENT_TYPE": "CodeIngestionCompletionEvent"}}}, "processCompletionFailStage": {"choice": {"name": "CodeIngestionStandardRunnerFailChoice", "conditions": [{"condition": {"type": "IsPresent", "IsPresent": true, "variable": "$.Result.CodeIngestion.StandardRunner.Error"}}]}, "name": "CodeIngestionStandardRunnerFail", "errorPathJsonPath": "$.Result.CodeIngestion.StandardRunner.Error.Error", "causePathJsonPath": "$.Result.CodeIngestion.StandardRunner.Error.Cause"}}}, {"name": "CodeIngestionLargeRunnerSubPipelineStage", "stage": "SubPipeline", "choice": {"name": "CodeIngestionLargeRunnerChoice", "operation": "And", "conditions": [{"condition": {"type": "IsPresent", "IsPresent": true, "variable": "$.CodeIngestion.UseLargeRunner"}}, {"condition": {"type": "BooleanEquals", "BooleanEquals": true, "variable": "$.CodeIngestion.UseLargeRunner"}}]}, "batchSubmitStage": {"name": "CodeIngestionLargeRunnerStage", "stage": "BatchSubmitStage", "timeoutInMin": 1440, "retry": {"maxAttempts": 3, "intervalInSeconds": 10, "backOffRate": 2, "errors": ["InternalServerError", "ThrottlingException", "SageMaker.AmazonSageMakerException"]}, "catch": {"errors": ["States.TaskFailed"], "errorResultJsonPath": "$.Result.CodeIngestion.LargeRunner.Error", "catchStageName": "CodeIngestionLargeRunnerCompletionLambdaStage"}, "batchSubmitJobDefinitionArn": "arn:aws:batch:us-west-2:************:job-definition/CodeIngestionBatchCodeI-48ad47478da625b", "batchSubmitJobQueueArn": "arn:aws:batch:us-west-2:************:job-queue/CodeIngestionBatchCodeIn-vvQ8wO0P01xeVEkx", "batchSubmitJobNameJsonPath": "$.CodeIngestion.ProcessingJobName", "batchSubmitResultJsonPath": "$.Result.CodeIngestion.LargeRunner.BatchSubmit", "batchSubmitEnvironmentJsonPath": "$.CodeIngestion.ProcessEnvironment", "batchCompletionLambdaStage": {"name": "CodeIngestionLargeRunnerCompletionLambdaStage", "stage": "BatchCompletionStage", "enableVPCNetworking": true, "retry": {"maxAttempts": 5, "intervalInSeconds": 120, "backOffRate": 5, "errors": ["Lambda.ResourceNotReadyException", "States.TaskFailed"]}, "timeoutInMin": 5, "lambdaResultJsonPath": "$.Result.CodeIngestion.LargeRunner.CompletionLambda", "lambda": {"containerPath": "assets/lambda/data-pipeline/trigger-process-completion", "functionName": "CodeIngestionLargeTriggerCompletion", "handler": "handler.handle", "environment": {"PROCESS_OUTPUT_JSON_PATH": "$.CodeIngestion.ProcessOutput", "PROCESS_ERROR_JSON_PATH": "$.Result.CodeIngestion.LargeRunner.Error", "AMQ_HOST": "b-3dc4e244-255f-4760-8f52-e9f5665be9a2-1.mq.us-west-2.amazonaws.com,b-3dc4e244-255f-4760-8f52-e9f5665be9a2-2.mq.us-west-2.amazonaws.com,b-c88f2792-dcdb-4845-a5e4-f293ed5f5e5e-1.mq.us-west-2.amazonaws.com,b-c88f2792-dcdb-4845-a5e4-f293ed5f5e5e-2.mq.us-west-2.amazonaws.com", "AMQ_USER": "unblocked", "AMQ_PASSWORD_SECRET_NAME": "activemq-unblocked-password-2", "AMQ_QUEUE": "/queue/source_code_events", "AMQ_EVENT_TYPE": "CodeIngestionCompletionEvent"}}}, "batchCompletionFailStage": {"choice": {"name": "CodeIngestionLargeRunnerFailChoice", "conditions": [{"condition": {"type": "IsPresent", "IsPresent": true, "variable": "$.Result.CodeIngestion.LargeRunner.Error"}}]}, "name": "CodeIngestionLargeRunnerFail", "errorPathJsonPath": "$.Result.CodeIngestion.LargeRunner.Error.Error", "causePathJsonPath": "$.Result.CodeIngestion.LargeRunner.Error.Cause"}}}]}}, "topicIngestionDataPipeline": {"name": "TopicIngestionDataPipeline", "stateMachine": {"name": "TopicIngestionDataPipelineStateMachine", "endpointName": "topic-ingestion-data-pipeline", "preProcessStages": [{"name": "TopicIngestionStage", "stage": "PreProcess", "enableVPCNetworking": true, "timeoutInMin": 480, "retry": {"maxAttempts": 3, "intervalInSeconds": 10, "backOffRate": 2, "errors": ["InternalServerError", "ThrottlingException", "SageMaker.AmazonSageMakerException"]}, "processDockerImage": {"name": "topic-ingestion-data-pipeline", "directory": "assets/image/topic-ingestion-data-pipeline/cluster-processor-image", "buildArgs": {"ENVIRONMENT": "prod"}, "secretBuildArgs": [{"name": "JFROG_PASSWORD", "secretName": "JFROG_PASSWORD"}]}, "processInstanceType": "c5.4xlarge", "processJobNameJsonPath": "$.TopicIngestion.ProcessingJobName", "processS3Inputs": [], "processS3Outputs": [{"outputName": "output", "outputJsonPath": "$.TopicIngestion.ProcessOutput"}], "processResultJsonPath": "$.Result.TopicIngestion", "processEnvironmentJsonPath": "$.TopicIngestion.ProcessEnvironment", "processCompletionLambdaStage": {"name": "TopicIngestionCompletionLambdaStage", "stage": "ProcessCompletionStage", "enableVPCNetworking": true, "retry": {"maxAttempts": 5, "intervalInSeconds": 120, "backOffRate": 5, "errors": ["Lambda.ResourceNotReadyException", "States.TaskFailed"]}, "timeoutInMin": 5, "lambdaResultJsonPath": "$.Result.TopicIngestionCompletionLambdaStage", "lambda": {"containerPath": "assets/lambda/data-pipeline/trigger-process-completion", "functionName": "TopicIngestionTriggerCompletion", "handler": "handler.handle", "environment": {"PROCESS_OUTPUT_JSON_PATH": "$.TopicIngestion.ProcessOutput", "AMQ_HOST": "b-3dc4e244-255f-4760-8f52-e9f5665be9a2-1.mq.us-west-2.amazonaws.com,b-3dc4e244-255f-4760-8f52-e9f5665be9a2-2.mq.us-west-2.amazonaws.com,b-c88f2792-dcdb-4845-a5e4-f293ed5f5e5e-1.mq.us-west-2.amazonaws.com,b-c88f2792-dcdb-4845-a5e4-f293ed5f5e5e-2.mq.us-west-2.amazonaws.com", "AMQ_USER": "unblocked", "AMQ_PASSWORD_SECRET_NAME": "activemq-unblocked-password-2", "AMQ_QUEUE": "/queue/topic_priority_events", "AMQ_EVENT_TYPE": "TopicIngestionCompletionEvent"}}}}]}}, "efsFileSystems": [], "batches": [{"name": "CodeIngestionBatch", "ecsJobDefinition": {"name": "CodeIngestionBatchJobDefinition", "ecsEC2ContainerDefinition": {"name": "CodeIngestionBatchContainerDefinition", "cpus": 3, "gpus": 0, "memoryLimitGB": 7, "dockerImage": {"name": "code-ingestion-standard-data-pipeline-embeddings", "directory": "assets/image/code-ingestion-data-pipeline/embeddings-processor-image", "buildArgs": {"ENVIRONMENT": "prod"}, "secretBuildArgs": [{"name": "JFROG_PASSWORD", "secretName": "JFROG_PASSWORD"}]}, "environment": {"NVIDIA_DRIVER_CAPABILITIES": "compute,utility", "NVIDIA_REQUIRE_CUDA": "cuda>=8.0"}}}, "ec2ECSComputeEnvironment": {"name": "CodeIngestionBatchComputeEnvironment", "instanceType": "c5a.4xlarge", "securityGroup": {"name": "CodeIngestionBatchComputeEnvironmentSG", "description": "Security group for Code Ingestion Batch Compute Environment", "allowAllOutbound": true, "ingressRules": [{"peerRule": {"peerType": "ipv4Cidr", "ipv4Cidr": "***********/16"}, "portRule": {"port": 22, "portType": "tcp"}}]}, "launchTemplate": {"name": "CodeIngestionBatchLaunchTemplate", "keyPairName": "rashin-key-test", "blockDevices": [{"deviceName": "/dev/xvda", "volume": {"deleteOnTermination": true, "sizeInGB": 150, "volumeType": "gp2"}}]}}, "propagateTags": true, "tags": [{"name": "DrataExclude", "value": "TRUE", "properties": {"applyToLaunchedInstances": true}}]}]}