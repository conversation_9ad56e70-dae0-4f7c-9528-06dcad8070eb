import { Stack, StackProps } from 'aws-cdk-lib';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { Construct } from 'constructs';
import { AwsSecret } from '../common/asm/config';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';

interface SecretsConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
    secrets: Array<AwsSecret>;
}

export interface SecretsStackProps extends StackProps {
    buildConfig: SecretsConfig;
}

export class SecretsStack extends Stack {
    constructor(scope: Construct, id: string, props: SecretsStackProps) {
        super(scope, id, props);

        if (props.buildConfig.awsEnvAccount.coldSite) {
            console.log('Skipping SecretsStack');
            return;
        }

        for (const secretConfig of props.buildConfig.secrets) {
            new secretsmanager.Secret(this, secretConfig.secretName, {
                secretName: secretConfig.secretName,
                generateSecretString: {
                    excludeCharacters: secretConfig.excludeCharacters,
                    excludePunctuation: secretConfig.excludePunctuation,
                    passwordLength: secretConfig.passwordLength,
                },
            });
        }
    }
}
