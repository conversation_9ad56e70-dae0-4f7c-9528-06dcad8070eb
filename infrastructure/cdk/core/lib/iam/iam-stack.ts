import { Duration, Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount } from '../build-config';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Iam } from '../common/iam/config';

interface IamStackConfig {
    awsEnvAccount: AwsEnvAccount;
    iam: Iam;
}

export interface IamStackProps extends StackProps {
    buildConfig: IamStackConfig;
}

export class IamStack extends Stack {
    readonly users = new Map<string, iam.IUser>();
    constructor(scope: Construct, id: string, props: IamStackProps) {
        super(scope, id, props);

        const buildConfig = props.buildConfig;
        const iamConfig = props.buildConfig.iam;
        if (!iamConfig) return;

        // Create k8s deployer role
        // This role is assumed by AllK8sClustersDefaultNamespaceDeployAccess policy in
        // root management account. It provide cross account access to EKS clusters
        // for IAM users.
        if (iamConfig.createK8sDeployerRole) {
            new iam.Role(this, 'K8sDeployerRole', {
                roleName: 'K8sDeployerRole',
                assumedBy: new iam.AccountPrincipal(buildConfig.awsEnvAccount.awsOrgRootAccountID),
                description: 'Role used by CI/CD pipeline for deploying resources to k8s',
                maxSessionDuration: Duration.hours(2),
            });
        }

        // Create k8s reader role
        // This role is assumed by AllK8sClustersDefaultNamespaceReadAccess policy in
        // root management account. It provide cross account access to EKS clusters
        // for IAM users.
        if (iamConfig.createK8sReaderRole) {
            new iam.Role(this, 'K8sReaderRole', {
                roleName: 'K8sReaderRole',
                assumedBy: new iam.AccountPrincipal(buildConfig.awsEnvAccount.awsOrgRootAccountID),
                description: 'Role to allow read only access to k8s resources',
                maxSessionDuration: Duration.hours(2),
            });
        }

        // Create CloudWatch metrics reader role
        // Create role with full read-only access to Cloudwatch to be used by Grafana data source
        if (iamConfig.createCloudWatchReadOnlyRole) {
            new iam.Role(this, 'CloudWatchReadOnlyAccessForGrafana', {
                roleName: 'CloudWatchReadOnlyAccessForGrafana',
                assumedBy: new iam.AccountPrincipal(buildConfig.awsEnvAccount.awsOrgRootAccountID),
                description: 'Role used by Grafana.net to ingest CloudWatch metrics',
                managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('CloudWatchReadOnlyAccess')],
            });
        }

        if (iamConfig.createCrossAccountAdminReadOnlyRole) {
            new iam.Role(this, 'CrossAccountAdminReadOnlyRole', {
                roleName: 'CrossAccountAdminReadOnlyRole',
                assumedBy: new iam.AccountPrincipal(buildConfig.awsEnvAccount.awsOrgRootAccountID),
                description: 'Role used to grant cross account full read-only access',
                managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('ReadOnlyAccess')],
            });
        }

        if (iamConfig.createCrossAccountS3ReadOnlyRole) {
            new iam.Role(this, 'CrossAccountS3ReadOnlyRole', {
                roleName: 'CrossAccountS3ReadOnlyRole',
                assumedBy: new iam.AccountPrincipal(buildConfig.awsEnvAccount.awsOrgRootAccountID),
                description: 'Role used to grant cross account full read-only access to S3',
                managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('AmazonS3ReadOnlyAccess')],
            });
        }

        // Create a role to be used by VPC peering requesting accounts to automatically approve
        // their own requests. We only need this under sec-ops account for Dev and Prod account IDs.
        for (const accountId of iamConfig.peeringAccepterRoleTargetAccounts) {
            const peeringRole = new iam.Role(this, `CrossAccountPeeringAccepterRole-${accountId}`, {
                roleName: `CrossAccountPeeringAccepterRole-${accountId}`,
                assumedBy: new iam.AccountPrincipal(accountId),
                description: `Role to grant account ${accountId} access to a accept its own request`,
            });
            peeringRole.addToPolicy(
                new iam.PolicyStatement({
                    actions: ['ec2:AcceptVpcPeeringConnection'],
                    resources: ['*'],
                })
            );
        }

        // Create EC2 deploybot role
        // Role used to create EC2 instances for Github Actions
        if (iamConfig.createEc2DeploybotRole) {
            const ec2DeploybotRole = new iam.PolicyDocument({
                statements: [
                    new iam.PolicyStatement({
                        resources: ['*'],
                        actions: [
                            'ec2:StartInstances',
                            'ec2:RunInstances',
                            'ec2:DescribeInstances',
                            'ec2:DescribeInstanceStatus',
                            'ec2:DescribeInstanceTypes',
                            'ec2:DescribeSubnets',
                            'ec2:describeSpotPriceHistory',
                            'pricing:GetProducts',
                            'pricing:GetAttributeValues',
                        ],
                    }),
                    new iam.PolicyStatement({
                        resources: ['*'],
                        actions: ['ec2:StopInstances', 'ec2:RebootInstances', 'ec2:TerminateInstances'],
                        conditions: {
                            StringEquals: {
                                'ec2:ResourceTag/Owner': 'deploybot',
                            },
                        },
                    }),
                    new iam.PolicyStatement({
                        resources: ['*'],
                        actions: ['ec2:CreateTags'],
                        conditions: {
                            StringEquals: {
                                'ec2:CreateAction': 'RunInstances',
                            },
                        },
                    }),
                ],
            });

            new iam.Role(this, 'Ec2DeploybotRole', {
                roleName: 'Ec2DeploybotRole',
                assumedBy: new iam.AccountPrincipal(buildConfig.awsEnvAccount.awsOrgRootAccountID),
                description: 'Role used by deploybot account to create and delete ec2 instances for github actions',
                inlinePolicies: { Ec2DeploybotRole: ec2DeploybotRole },
            });

            // Ec2 Spot role required to launch EC2 instances
            new iam.CfnServiceLinkedRole(this, 'AWSServiceRoleForEC2Spot', {
                awsServiceName: 'spot.amazonaws.com',
            });
        }

        // Create each group, add users and attach all required policies
        for (const group of iamConfig.groups) {
            // AWS Managed policies
            const managedPoliciesList: iam.IManagedPolicy[] = [];
            group.managedPolicies.forEach((policy) =>
                managedPoliciesList.push(iam.ManagedPolicy.fromAwsManagedPolicyName(policy))
            );

            // Create group
            const g = new iam.Group(this, group.name, {
                groupName: group.name,
                managedPolicies: managedPoliciesList,
            });

            // Add policy statements to group's default policy
            group.policyStatements.forEach((statement, index) =>
                g.attachInlinePolicy(
                    new iam.Policy(this, `${group.name}-policy-${index}`, {
                        statements: [
                            new iam.PolicyStatement({
                                effect: statement.effect.toLowerCase() == 'allow' ? iam.Effect.ALLOW : iam.Effect.DENY,
                                actions: statement.actions,
                                resources: statement.resources,
                            }),
                        ],
                    })
                )
            );

            for (const user of group.members) {
                let u = this.users.get(user);
                // This is to avoid duplicate resources - cdk will complain without it
                if (u == undefined) {
                    u = iam.User.fromUserName(this, user, user);
                    this.users.set(user, u);
                }
                g.addUser(u);
            }
        }
    }
}
