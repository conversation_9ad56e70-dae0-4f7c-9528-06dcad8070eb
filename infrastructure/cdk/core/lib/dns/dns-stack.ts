import { Stack, StackProps, Duration } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import * as route53 from 'aws-cdk-lib/aws-route53';
import { Dns } from '../common/dns/config';

interface DnsStackConfig {
    awsEnvAccount: AwsEnvAccount;
    dns: Dns;
    cdkAppInfo: CdkAppInfo;
}

export interface DnsStackProps extends StackProps {
    buildConfig: DnsStackConfig;
}

export class DnsStack extends Stack {
    readonly myHostedZone: route53.IHostedZone;

    constructor(scope: Construct, id: string, props: DnsStackProps) {
        super(scope, id, props);

        const dnsConfig = props.buildConfig.dns;
        if (!dnsConfig) return;

        // Look up hosted zone for current environment
        this.myHostedZone = route53.HostedZone.fromHostedZoneAttributes(this, 'MyZone', {
            zoneName: dnsConfig.route53HostedZoneName,
            hostedZoneId: dnsConfig.route53HostedZoneID,
        });

        for (const record of dnsConfig.aRecords) {
            new route53.ARecord(this, `alias-${record.recordName}`, {
                recordName: record.recordName,
                target: route53.RecordTarget.fromValues(record.target),
                zone: this.myHostedZone,
                comment: record.comment ? record.comment : `alias record pointing to ${record.target}`,
                ttl: Duration.minutes(record.ttlMinutes),
            });
        }

        for (const record of dnsConfig.cnameRecords) {
            new route53.CnameRecord(this, `cname-${record.recordName}`, {
                domainName: record.domainName,
                zone: this.myHostedZone,
                comment: record.comment ? record.comment : `cname record pointing to ${record.domainName}`,
                recordName: record.recordName,
                ttl: Duration.minutes(record.ttlMinutes),
            });
        }
    }
}
