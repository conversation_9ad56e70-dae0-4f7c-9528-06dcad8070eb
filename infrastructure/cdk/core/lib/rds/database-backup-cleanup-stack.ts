import { Stack, StackProps, Duration } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { BuildConfig } from '../build-config';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import * as path from 'path';

export class DatabaseBackupCleanupStack extends Stack {
    readonly rdsS3BackupRole: iam.Role;

    constructor(scope: Construct, id: string, props: StackProps, buildConfig: BuildConfig) {
        super(scope, id, props);

        // Create S3 backup IAM role
        if (!buildConfig.rdsPostgres.disableS3Backup) {
            this.rdsS3BackupRole = this.createrdsS3BackupRole(buildConfig.awsEnvAccount.targetRegion);
        }

        this.setupSnapshotCleanupJobs(buildConfig);
    }

    /*
     * Setup a Lambda function to trigger a DB snapshot and export to S3 every night
     */
    private createrdsS3BackupRole(region: string): iam.Role {
        // Role giving access to Lambda job as well as RDS service to create and export backups
        const rdsS3BackupRole = new iam.Role(this, `PostgresBackupRole${region}-lambda`, {
            roleName: `PostgresBackupRole${region}-lambda`,
            assumedBy: new iam.CompositePrincipal(
                new iam.ServicePrincipal('lambda.amazonaws.com'),
                new iam.ServicePrincipal('export.rds.amazonaws.com')
            ),
            description: `Role assumed by lambda job for handling db backups`,
            managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole')],
            inlinePolicies: {
                RDSBackup: new iam.PolicyDocument({
                    statements: [
                        new iam.PolicyStatement({
                            resources: ['*'],
                            actions: [
                                'rds:DescribeDBSnapshots',
                                'rds:DescribeDBClusterSnapshots',
                                'rds:DescribeDBClusterSnapshotAttributes',
                                'rds:DescribeExportTasks',
                                'rds:CopyDBClusterSnapshot',
                                'rds:DeleteDBClusterSnapshot',
                                'rds:StartExportTask',
                            ],
                        }),
                        new iam.PolicyStatement({
                            resources: ['arn:aws:kms:*:*:alias/aws/rds'],
                            actions: [
                                'kms:Encrypt',
                                'kms:Decrypt',
                                'kms:GenerateDataKey',
                                'kms:GenerateDataKeyWithoutPlaintext',
                                'kms:ReEncryptFrom',
                                'kms:ReEncryptTo',
                                'kms:CreateGrant',
                                'kms:DescribeKey',
                                'kms:RetireGrant',
                            ],
                        }),
                        new iam.PolicyStatement({
                            resources: [
                                'arn:aws:s3:::unblocked-postgres-backup*',
                                `arn:aws:s3:::unblocked-postgres-backup*/*`,
                            ],
                            actions: [
                                's3:HeadObject',
                                's3:PutObject',
                                's3:GetObject',
                                's3:ListBucket',
                                's3:DeleteObject',
                                's3:PutObjectAcl',
                                's3:PutObjectTagging',
                                's3:DeleteObjectTagging',
                                's3:ListMultipartUploadParts',
                                's3:AbortMultipartUpload',
                                's3:CreateBucket',
                                's3:GetBucketLocation',
                            ],
                        }),
                    ],
                }),
            },
        });

        rdsS3BackupRole.addToPolicy(
            new iam.PolicyStatement({
                resources: [rdsS3BackupRole.roleArn],
                actions: ['iam:PassRole'],
            })
        );

        return rdsS3BackupRole;
    }

    /*
     * Setup a Lambda function to cleaup old backups
     */
    private setupSnapshotCleanupJobs(buildConfig: BuildConfig) {
        // Lambda job to clean up manual snapshots
        // Any snapshot copied to secondary region(s) show up as manual snapshot
        // To exclude a snapshot, add a `retain` tag with value of `true` to it
        const rdsSnapshotCleanupLambdaFunction = new lambda.Function(
            this,
            `RDSPostgresSnapshotCleaupLambda${buildConfig.awsEnvAccount.targetRegion}`,
            {
                functionName: `RDSPostgresSnapshotCleaupLambda${buildConfig.awsEnvAccount.targetRegion}`,
                runtime: lambda.Runtime.PYTHON_3_9,
                memorySize: 128,
                timeout: Duration.minutes(15),
                handler: 'main.lambda_handler',
                code: lambda.Code.fromAsset(
                    path.join(__dirname, '../../assets/lambda/rds-aurora-postgres-snapshot-cleanup-lambda')
                ),
                environment: {
                    RETENTION_DAYS: `${buildConfig.rdsPostgres.backupRetentionDays}`,
                },
                role: this.rdsS3BackupRole,
            }
        );

        // Run once an hour
        const eventRule = new events.Rule(this, 'scheduleRule', {
            schedule: events.Schedule.cron({ minute: '1' }),
        });

        eventRule.addTarget(new targets.LambdaFunction(rdsSnapshotCleanupLambdaFunction));
    }
}
