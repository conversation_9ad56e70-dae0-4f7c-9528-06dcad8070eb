import { Stack, StackProps, Duration } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { Dns } from '../common/dns/config';
import { NetworkStack } from '../vpc/network-stack';
import { BlockDeviceVolume } from 'aws-cdk-lib/aws-ec2';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as subscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as cw_actions from 'aws-cdk-lib/aws-cloudwatch-actions';
import { BastionHost } from './config';

interface BastionHostStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
    bastionHost: BastionHost;
    dns: Dns;
}

export interface BastionHostStackProps extends StackProps {
    buildConfig: BastionHostStackConfig;
    networkStack: NetworkStack;
}

export class BastionHostStack extends Stack {
    readonly host: ec2.BastionHostLinux;
    readonly securityGroup: ec2.SecurityGroup;
    readonly snsTopic: sns.Topic;
    constructor(scope: Construct, id: string, props: BastionHostStackProps) {
        super(scope, id, props);

        this.securityGroup = new ec2.SecurityGroup(this, 'BastionHostSG', {
            vpc: props.networkStack.coreVpc,
            allowAllOutbound: true,
            securityGroupName: 'BastionHostSG',
        });

        // Create inbound rules to allow traffic to SSH port
        for (const ip of props.buildConfig.bastionHost.allowedCIDRs) {
            this.securityGroup.addIngressRule(
                ec2.Peer.ipv4(ip),
                ec2.Port.tcp(22),
                'Allows ssh access from external IP address'
            );
        }

        this.host = new ec2.BastionHostLinux(this, 'LinuxBastionHost', {
            vpc: props.networkStack.coreVpc,
            securityGroup: this.securityGroup,
            instanceName: 'LinuxBastionHost',
            instanceType: ec2.InstanceType.of(ec2.InstanceClass.T2, ec2.InstanceSize.MICRO),
            subnetSelection: {
                subnetType: ec2.SubnetType.PUBLIC,
            },
            blockDevices: [
                {
                    deviceName: '/dev/xvda',
                    volume: BlockDeviceVolume.ebs(10, {
                        encrypted: true,
                    }),
                },
            ],

            init: ec2.CloudFormationInit.fromConfigSets({
                configSets: {
                    // Applies the configs below in this order
                    default: ['retool_user', 'retool_pubkey'],
                },
                configs: {},
            }),
            initOptions: {
                configSets: ['default'],
                timeout: Duration.minutes(10),
            },
        });

        this.host.instance.instance.addPropertyOverride('KeyName', props.buildConfig.bastionHost.sshKeyName);

        const elasticIp = new ec2.CfnEIP(this, 'BastionHostEIP', {
            instanceId: this.host.instanceId,
            tags: [{ key: 'Name', value: 'BastionHostEIP' }],
        });

        // Look up hosted zone for current environment
        const secopsHostedZone = route53.HostedZone.fromHostedZoneAttributes(this, 'MyZone', {
            zoneName: props.buildConfig.dns.route53HostedZoneName,
            hostedZoneId: props.buildConfig.dns.route53HostedZoneID,
        });

        new route53.ARecord(this, 'ARecord', {
            recordName: 'bhost',
            zone: secopsHostedZone,
            target: route53.RecordTarget.fromIpAddresses(elasticIp.ref),
        });

        // Create SNS topic
        this.snsTopic = this.createSNSTopic(props.buildConfig);

        // Create alarms
        this.createAlarms(props.buildConfig);
    }

    /*
     * SNS topic to notify AWS admins of various alarm events
     */
    private createSNSTopic(buildConfig: BastionHostStackConfig): sns.Topic {
        const topic = new sns.Topic(this, 'BastionHost-Alarms', {
            displayName: 'Topic to deliver alarms related to Bastion host',
        });

        // Send emails to admins
        topic.addSubscription(new subscriptions.EmailSubscription(buildConfig.awsEnvAccount.notificationEmail));
        return topic;
    }

    private createAlarms(buildConfig: BastionHostStackConfig) {
        const alarm = new cloudwatch.Alarm(this, 'BastionHostCPUUtilization', {
            alarmName: 'BastionHostCPUUtilization',
            alarmDescription: 'Bastion host server high CPU',
            comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
            threshold: 80,
            evaluationPeriods: 2,
            metric: new cloudwatch.Metric({
                account: buildConfig.awsEnvAccount.awsAccountID,
                metricName: 'CPUUtilization',
                namespace: 'AWS/EC2',
                dimensionsMap: {
                    InstanceId: this.host.instanceId,
                },
            }),
            actionsEnabled: true,
        });
        alarm.addAlarmAction(new cw_actions.SnsAction(this.snsTopic));
    }
}
