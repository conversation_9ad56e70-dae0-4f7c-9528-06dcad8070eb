import { Stack, StackProps } from 'aws-cdk-lib';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as cw_actions from 'aws-cdk-lib/aws-cloudwatch-actions';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as elbv2 from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import { Protocol } from 'aws-cdk-lib/aws-elasticloadbalancingv2';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as route53Targets from 'aws-cdk-lib/aws-route53-targets';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as subscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { AutoScalingConstruct } from '../common/autoscaling/autoscaling-construct';
import { Dns } from '../common/dns/config';
import { HttpProxy } from './config';

interface HttpProxyStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
    httpProxy: HttpProxy;
    dns: Dns;
}

export interface HttpProxyStackProps extends StackProps {
    buildConfig: HttpProxyStackConfig;
}

export class HttpProxyStack extends Stack {
    readonly host: ec2.Instance;
    readonly elbSecurityGroup: ec2.SecurityGroup;
    readonly snsTopic: sns.Topic;

    constructor(scope: Construct, id: string, props: HttpProxyStackProps) {
        super(scope, id, props);

        if (props.buildConfig.awsEnvAccount.coldSite) {
            console.log('Skipping HttpProxyStack');
            return;
        }

        // Lookup core VPC
        const vpcId = ssm.StringParameter.valueFromLookup(this, '/network-stack/core-vpc-id');
        const vpc = ec2.Vpc.fromLookup(this, `ImportCoreVPC`, {
            isDefault: false,
            vpcId: vpcId,
        });

        // Create Keypair
        const cfnKeyPair = new ec2.CfnKeyPair(this, 'HttpProxyKeypair', {
            keyName: 'HttpProxyKeypair',
        });
        const keypair = ec2.KeyPair.fromKeyPairName(this, 'HttpProxyKeypairObject', cfnKeyPair.keyName);
        keypair.node.addDependency(cfnKeyPair);

        const asg = new AutoScalingConstruct(this, `HttpProxyASG`, {
            autoScalingGroup: props.buildConfig.httpProxy.autoScalingGroup,
            vpc: vpc,
            vpcSubnets: {
                subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
            },
        });

        // Elb Security Group
        this.elbSecurityGroup = new ec2.SecurityGroup(this, 'HttpProxyElbSG', {
            vpc: vpc,
            allowAllOutbound: true,
            securityGroupName: 'HttpProxyElbSG',
        });

        // Allow traffic from Dev private address range
        this.elbSecurityGroup.addIngressRule(ec2.Peer.ipv4('*********/8'), ec2.Port.tcp(80), 'Traffic from Dev');

        // Allow traffic from Prod private address range
        this.elbSecurityGroup.addIngressRule(ec2.Peer.ipv4('10.0.0.0/8'), ec2.Port.tcp(80), 'Traffic from Prod');

        // Allow traffic from SecOps (VPN) private address range
        this.elbSecurityGroup.addIngressRule(
            ec2.Peer.ipv4('***********/16'),
            ec2.Port.tcp(80),
            'Traffic from SecOps (VPN'
        );

        // Ugly hack to get around this bug
        // https://github.com/aws/aws-cdk/issues/24427
        let elbSubnetFilter: any = { subnetType: ec2.SubnetType.PRIVATE_ISOLATED };
        if (props.buildConfig.httpProxy.elbCidrRanges.length > 0) {
            const subnets = [];
            for (const subnet of vpc.isolatedSubnets) {
                if (props.buildConfig.httpProxy.elbCidrRanges.includes(subnet.ipv4CidrBlock)) {
                    subnets.push(subnet);
                }
            }
            elbSubnetFilter = {
                subnets: subnets,
            };
        }

        // Create and configure loadbalancer
        const proxylb = new elbv2.NetworkLoadBalancer(this, 'NLB', {
            vpc,
            internetFacing: false,
            securityGroups: [this.elbSecurityGroup],
            vpcSubnets: elbSubnetFilter,
        });

        const listener = proxylb.addListener('Listener', {
            port: 80,
            protocol: Protocol.TCP,
        });

        listener.addTargets('HttpProxyFleet', {
            port: 3128,
            targets: [asg.autoScalingGroup],
        });

        // Look up hosted zone for current environment
        const hostedZone = route53.HostedZone.fromHostedZoneAttributes(this, 'Route53Zone', {
            zoneName: props.buildConfig.dns.route53HostedZoneName,
            hostedZoneId: props.buildConfig.dns.route53HostedZoneID,
        });

        // Create DNS record
        new route53.ARecord(this, 'HttpProxyARecord', {
            recordName: `proxy.${props.buildConfig.dns.route53HostedZoneName}`,
            zone: hostedZone,
            target: route53.RecordTarget.fromAlias(new route53Targets.LoadBalancerTarget(proxylb)),
        });

        // CPU monitoring for Drata
        // Create SNS topic
        this.snsTopic = this.createSNSTopic(props.buildConfig);

        // Create alarms
        this.createAlarms(props.buildConfig, asg.autoScalingGroup.autoScalingGroupName);
    }

    /*
     * SNS topic to notify AWS admins of various alarm events
     */
    private createSNSTopic(buildConfig: HttpProxyStackConfig): sns.Topic {
        const topic = new sns.Topic(this, 'HttpProxy-Alarms', {
            displayName: 'Topic to deliver alarms related to http proxy system',
        });

        // Send emails to admins
        topic.addSubscription(new subscriptions.EmailSubscription(buildConfig.awsEnvAccount.notificationEmail));
        return topic;
    }

    private createAlarms(buildConfig: HttpProxyStackConfig, asgName: string) {
        const alarm = new cloudwatch.Alarm(this, 'HttpProxyCPUUtilization', {
            alarmName: 'HttpProxyCPUUtilization',
            alarmDescription: 'Bastion host server high CPU',
            comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
            threshold: 80,
            evaluationPeriods: 2,
            metric: new cloudwatch.Metric({
                account: buildConfig.awsEnvAccount.awsAccountID,
                metricName: 'CPUUtilization',
                namespace: 'AWS/EC2',
                dimensionsMap: {
                    AutoScalingGroupName: asgName,
                },
            }),
            actionsEnabled: true,
        });
        alarm.addAlarmAction(new cw_actions.SnsAction(this.snsTopic));
    }
}
