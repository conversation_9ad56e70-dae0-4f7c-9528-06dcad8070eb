import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { SecOpsBuildConfig } from '../build-config';
import * as route53 from 'aws-cdk-lib/aws-route53';
import { NetworkStack } from '../vpc/network-stack';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as cw_actions from 'aws-cdk-lib/aws-cloudwatch-actions';
import * as subscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';

export class OpenvpnStack extends Stack {
    readonly ec2Instance: ec2.Instance;
    readonly openvpnSg: ec2.SecurityGroup;
    readonly eip: ec2.CfnEIP;
    readonly route53ARecord: route53.ARecord;
    readonly snsTopic: sns.Topic;

    constructor(
        scope: Construct,
        id: string,
        props: StackProps,
        secOpsbuildConfig: SecOpsBuildConfig,
        private networkStack: NetworkStack
    ) {
        super(scope, id, props);

        // OpenVPN Stack requires manually created VPC peering connections.
        // Until we can figure out a programmatic way of creating cross-account peering
        // this stack can only be deployed to us-west-2a under sec-ops account .
        if (secOpsbuildConfig.awsEnvAccount.targetRegion !== 'us-west-2') {
            throw new Error('OpenVPN stack can only be deployed to us-west-2 under sec-ops account');
        }

        // Prepare security group
        this.openvpnSg = new ec2.SecurityGroup(this, 'OpenVPNSg', {
            vpc: this.networkStack.coreVpc,
            allowAllOutbound: true,
            description: 'security group for OpenVPN server',
        });

        //sg.addIngressRule(ec2.Peer.anyIpv4(), ec2.Port.tcp(22));
        this.openvpnSg.addIngressRule(ec2.Peer.anyIpv4(), ec2.Port.tcp(943));
        this.openvpnSg.addIngressRule(ec2.Peer.anyIpv4(), ec2.Port.tcp(443));
        this.openvpnSg.addIngressRule(ec2.Peer.anyIpv4(), ec2.Port.udp(1194));

        // Define EC2 instance
        this.ec2Instance = new ec2.Instance(this, 'openvpn-server', {
            vpc: this.networkStack.coreVpc,
            vpcSubnets: {
                subnetType: ec2.SubnetType.PUBLIC,
            },
            securityGroup: this.openvpnSg,
            instanceType: ec2.InstanceType.of(ec2.InstanceClass.T2, ec2.InstanceSize.SMALL),
            machineImage: new ec2.GenericLinuxImage({
                'us-west-2': 'ami-0d10bccf2f1a6d60b',
            }),
            keyName: 'openvpn', // Can be found in 1password infra vault
        });

        // Allocate and attach elastic IP address
        this.eip = new ec2.CfnEIP(this, 'OpenVPN Server Public Elastic IP', {
            tags: [{ key: 'Name', value: 'OpenVPNEIP' }],
        });

        new ec2.CfnEIPAssociation(this, 'OpenvpnEIPAssociation', {
            eip: this.eip.ref,
            instanceId: this.ec2Instance.instanceId,
        });

        // Look up hosted zone for current environment
        const secopsHostedZone = route53.HostedZone.fromHostedZoneAttributes(this, 'MyZone', {
            zoneName: secOpsbuildConfig.dns.route53HostedZoneName,
            hostedZoneId: secOpsbuildConfig.dns.route53HostedZoneID,
        });

        this.route53ARecord = new route53.ARecord(this, 'AliasRecord', {
            zone: secopsHostedZone,
            target: route53.RecordTarget.fromIpAddresses(this.eip.ref),
            recordName: 'gateway',
        });

        // Create SNS topic
        this.snsTopic = this.createSNSTopic(secOpsbuildConfig);

        // Create alarms
        this.createAlarms(secOpsbuildConfig);
    }

    /*
     * SNS topic to notify AWS admins of various alarm events
     */
    private createSNSTopic(buildConfig: SecOpsBuildConfig): sns.Topic {
        const topic = new sns.Topic(this, 'OpenVPN-Alarms', {
            displayName: 'Topic to deliver alarms related to OpenVPN Access Server',
        });

        // Send emails to admins
        topic.addSubscription(new subscriptions.EmailSubscription(buildConfig.awsEnvAccount.notificationEmail));
        return topic;
    }

    private createAlarms(buildConfig: SecOpsBuildConfig) {
        const alarm = new cloudwatch.Alarm(this, 'OpenVPNCPUUtilization', {
            alarmName: 'OpenVPNCPUUtilization',
            alarmDescription: 'OpenVPN server high CPU',
            comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
            threshold: 80,
            evaluationPeriods: 2,
            metric: new cloudwatch.Metric({
                account: buildConfig.awsEnvAccount.awsAccountID,
                metricName: 'CPUUtilization',
                namespace: 'AWS/EC2',
                dimensionsMap: {
                    InstanceId: this.ec2Instance.instanceId,
                },
            }),
            actionsEnabled: true,
        });
        alarm.addAlarmAction(new cw_actions.SnsAction(this.snsTopic));
    }
}
