import * as cdk from 'aws-cdk-lib';
import { Stack, StackProps, Duration } from 'aws-cdk-lib';
import * as cloudwatch from 'aws-cdk-lib/aws-cloudwatch';
import * as cw_actions from 'aws-cdk-lib/aws-cloudwatch-actions';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as sns from 'aws-cdk-lib/aws-sns';
import * as subscriptions from 'aws-cdk-lib/aws-sns-subscriptions';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { AutoScalingConstruct } from '../common/autoscaling/autoscaling-construct';
import { AutoScalingGroup } from '../common/autoscaling/config';
import { CustomerHTTPProxyConfig, LaunchTemplate } from '../common/ec2/config';
import { Dns } from '../common/dns/config';
import { LambdaConstruct } from '../common/lambda/lambda-construct';

interface CustomerHTTPProxyStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
    dns: Dns;
    customerHTTPProxy: CustomerHTTPProxyConfig[];
}

export interface CustomerHTTPProxyStackProps extends StackProps {
    buildConfig: CustomerHTTPProxyStackConfig;
}

export class CustomerHTTPProxyStack extends Stack {
    private vpc: ec2.IVpc;
    private snsTopic: sns.Topic;
    private hostedZone: route53.IHostedZone;

    constructor(scope: Construct, id: string, props: CustomerHTTPProxyStackProps) {
        super(scope, id, props);

        if (props.buildConfig.awsEnvAccount.coldSite) {
            console.log('Skipping CustomerHTTPProxyStack - cold site');
            return;
        }

        // Initialize core components
        this.initializeCore(props.buildConfig);

        // Create proxy deployments for each enabled customer
        props.buildConfig.customerHTTPProxy
            .filter((customer) => customer.enable)
            .forEach((customer) => {
                this.createCustomerProxy(customer, props.buildConfig);
            });
    }

    private initializeCore(buildConfig: CustomerHTTPProxyStackConfig): void {
        // Lookup core VPC
        const vpcId = ssm.StringParameter.valueFromLookup(this, '/network-stack/core-vpc-id');
        this.vpc = ec2.Vpc.fromLookup(this, 'ImportCoreVPC', {
            isDefault: false,
            vpcId: vpcId,
        });

        // Look up hosted zone for current environment
        this.hostedZone = route53.HostedZone.fromHostedZoneAttributes(this, 'Route53Zone', {
            zoneName: buildConfig.dns.route53HostedZoneName,
            hostedZoneId: buildConfig.dns.route53HostedZoneID,
        });

        // Create SNS topic for notifications
        this.snsTopic = new sns.Topic(this, 'CustomerHTTPProxy-Alarms', {
            displayName: 'Customer HTTP Proxy system alarms and notifications',
        });

        this.snsTopic.addSubscription(new subscriptions.EmailSubscription(buildConfig.awsEnvAccount.notificationEmail));
    }

    private createCustomerProxy(customer: CustomerHTTPProxyConfig, buildConfig: CustomerHTTPProxyStackConfig): void {
        const instanceCount = customer.elasticIps.length;
        const instanceType = customer.instanceType || 't3.small';
        const userDataPath = customer.userDataScriptPath || 'assets/userdata/http-proxy-node.sh';

        // Create Keypair
        const cfnKeyPair = new ec2.CfnKeyPair(this, `${customer.name}HttpProxyKeypair`, {
            keyName: `${customer.name}HttpProxyKeypair`,
        });
        const keypair = ec2.KeyPair.fromKeyPairName(this, `${customer.name}HttpProxyKeypairObject`, cfnKeyPair.keyName);
        keypair.node.addDependency(cfnKeyPair);

        // Create LaunchTemplate instance
        const launchTemplate = new LaunchTemplate({
            name: `${customer.name}HttpProxyLT`,
            instanceType: instanceType,
            keyPairName: `${customer.name}HttpProxyKeypair`,
            associatePublicIpAddress: false,
            machineAmiType: 'Standard',
            userData: {
                type: 'linux',
                userDataParts: [
                    {
                        userDataFilePath: userDataPath,
                    },
                ],
            },
            securityGroup: {
                name: `${customer.name}HttpProxySG`,
                description: `Security group for ${customer.name} HTTP proxy`,
                allowAllOutbound: true,
                ingressRules: customer.allowedIngressCIDRs.map((cidr) => ({
                    peerRule: {
                        peerType: 'ipv4Cidr',
                        ipv4Cidr: cidr,
                    },
                    portRule: {
                        port: 3128,
                        portType: 'tcp',
                    },
                })),
            },
        });

        // Create AutoScalingGroup instance
        const asgConfig = new AutoScalingGroup({
            name: `${customer.name}HttpProxyASG`,
            machineAmiType: 'Standard',
            instanceType: instanceType,
            minCapacity: instanceCount,
            maxCapacity: instanceCount,
            desiredCapacity: instanceCount,
            launchTemplate: launchTemplate,
        });

        // Create Auto Scaling Group
        const asg = new AutoScalingConstruct(this, `${customer.name}HttpProxyASG`, {
            autoScalingGroup: asgConfig,
            vpc: this.vpc,
            vpcSubnets: {
                subnetType: ec2.SubnetType.PUBLIC,
            },
            instanceMaintenancePolicyOverride: {
                minHealthyPercentage: 50,
                maxHealthyPercentage: 100,
            },
        });

        // Create customer-specific maintenance Lambda
        this.createMaintenanceLambda(customer, buildConfig);

        // Create customer-specific alarms
        this.createCustomerAlarms(customer, asg.autoScalingGroup.autoScalingGroupName);

        // Create blackout lambda function if blackout windows have been provided. All times are UTC
        if (customer.blackoutWindow && customer.blackoutWindow.length > 0) {
            this.createMaintenanceBlackoutLambda(customer, buildConfig);
        }
    }
    private createMaintenanceBlackoutLambda(
        customer: CustomerHTTPProxyConfig,
        buildConfig: CustomerHTTPProxyStackConfig
    ): void {
        // Validate required environment variables
        const slackWebhookUrl = process.env.SLACK_WEBHOOK_URL_INFRA_ALARMS;
        const grafanaWebhookUrl = process.env.GRAFANA_WEBHOOK_URL_INFRA_ALARMS;

        if (!slackWebhookUrl) {
            throw new Error('SLACK_WEBHOOK_URL_INFRA_ALARMS environment variable is required for deployment');
        }

        if (!grafanaWebhookUrl) {
            throw new Error('GRAFANA_WEBHOOK_URL_INFRA_ALARMS environment variable is required for deployment');
        }

        // Create IAM role for Lambda
        const lambdaRole = new iam.Role(this, `${customer.name}HTTPProxyBlackoutRole`, {
            assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
            managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole')],
            inlinePolicies: {
                SecurityGroupManagement: new iam.PolicyDocument({
                    statements: [
                        new iam.PolicyStatement({
                            effect: iam.Effect.ALLOW,
                            actions: [
                                'ec2:DescribeSecurityGroups',
                                'ec2:AuthorizeSecurityGroupIngress',
                                'ec2:RevokeSecurityGroupIngress',
                            ],
                            resources: ['*'],
                        }),
                    ],
                }),
            },
        });

        const maintenanceBlackoutLambda = new LambdaConstruct(this, `${customer.name}HTTPProxyBlackout`, {
            name: `${customer.name}HTTPProxyBlackout`,
            runtime: lambda.Runtime.PYTHON_3_12,
            lambdaPath: 'assets/lambda/customer-http-proxy-blackout',
            handler: 'main.lambda_handler',
            environment: {
                MAINTENANCE_WINDOWS: customer.blackoutWindow.join(','),
                SECURITY_GROUP_NAME: `${customer.name}HttpProxySG`,
                ALLOWED_INBOUND_CIDR_LIST: customer.allowedIngressCIDRs?.join(','),
                ALLOWED_INBOUND_PORT: '3128',
                SLACK_WEBHOOK_URL: slackWebhookUrl,
                GRAFANA_WEBHOOK_URL: grafanaWebhookUrl,
            },
            region: this.region,
            lambdaRole: lambdaRole,
            timeout: cdk.Duration.minutes(2),
        });

        // Schedule to run every 15 minutes to check maintenance windows
        new events.Rule(this, `${customer.name}HTTPProxyBlackoutSchedule`, {
            schedule: events.Schedule.rate(cdk.Duration.minutes(15)),
            targets: [new targets.LambdaFunction(maintenanceBlackoutLambda.lambdaFunction)],
        });
    }

    private createMaintenanceLambda(
        customer: CustomerHTTPProxyConfig,
        buildConfig: CustomerHTTPProxyStackConfig
    ): void {
        // Create IAM role for Lambda
        const lambdaRole = new iam.Role(this, `${customer.name}HTTPProxyMaintenanceRole`, {
            assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
            managedPolicies: [iam.ManagedPolicy.fromAwsManagedPolicyName('service-role/AWSLambdaBasicExecutionRole')],
            inlinePolicies: {
                AsgEC2AndRoute53: new iam.PolicyDocument({
                    statements: [
                        new iam.PolicyStatement({
                            effect: iam.Effect.ALLOW,
                            actions: [
                                'autoscaling:DescribeAutoScalingGroups',
                                'autoscaling:DescribeAutoScalingInstances',
                                'ec2:DescribeInstances',
                                'ec2:DescribeInstanceStatus',
                                'ec2:DescribeAddresses',
                                'ec2:AssociateAddress',
                                'ec2:DisassociateAddress',
                                'route53:ChangeResourceRecordSets',
                                'route53:ListResourceRecordSets',
                                'sns:Publish',
                            ],
                            resources: ['*'],
                        }),
                    ],
                }),
            },
        });

        const k8sEc2DisableSrcDstCheck = new LambdaConstruct(this, `${customer.name}HTTPProxyMaintenance`, {
            name: `${customer.name}HTTPProxyMaintenance`,
            runtime: lambda.Runtime.PYTHON_3_12,
            lambdaPath: 'assets/lambda/customer-http-proxy-maintenance',
            handler: 'main.lambda_handler',
            environment: {
                TARGET_ASG_NAME: `${customer.name}HttpProxyASG`,
                SNS_TOPIC_ARN: this.snsTopic.topicArn,
                HOSTED_ZONE_ID: this.hostedZone.hostedZoneId,
                HOSTED_ZONE_NAME: buildConfig.dns.route53HostedZoneName,
                CUSTOMER_NAME: customer.name,
                ELASTIC_IPS: customer.elasticIps.map((eip) => eip.ip).join(','),
                ELASTIC_IP_IDS: customer.elasticIps.map((eip) => eip.id).join(','),
            },
            region: this.region,
            lambdaRole: lambdaRole,
            timeout: cdk.Duration.minutes(5),
        });

        // Setup AutoScaling Events for this customer's ASG
        this.setupAutoScalingEvents(customer, k8sEc2DisableSrcDstCheck.lambdaFunction);

        // Schedule periodic maintenance every 5 minutes
        this.schedulePeriodicMaintenance(customer, k8sEc2DisableSrcDstCheck.lambdaFunction);
    }

    private setupAutoScalingEvents(customer: CustomerHTTPProxyConfig, maintenanceLambda: lambda.Function): void {
        // Create EventBridge rule for ASG events specific to this customer
        const asgEventRule = new events.Rule(this, `${customer.name}ASGEventRule`, {
            eventPattern: {
                source: ['aws.autoscaling'],
                detailType: [
                    'EC2 Instance Launch Successful',
                    'EC2 Instance Launch Unsuccessful',
                    'EC2 Instance Terminate Successful',
                    'EC2 Instance Terminate Unsuccessful',
                ],
                detail: {
                    AutoScalingGroupName: [`${customer.name}HttpProxyASG`],
                },
            },
        });

        asgEventRule.addTarget(new targets.LambdaFunction(maintenanceLambda));
    }

    private schedulePeriodicMaintenance(customer: CustomerHTTPProxyConfig, maintenanceLambda: lambda.Function): void {
        // Schedule Lambda to run every 5 minutes
        const periodicRule = new events.Rule(this, `${customer.name}PeriodicMaintenanceRule`, {
            schedule: events.Schedule.rate(Duration.minutes(5)),
        });

        periodicRule.addTarget(new targets.LambdaFunction(maintenanceLambda));
    }

    private createCustomerAlarms(customer: CustomerHTTPProxyConfig, asgName: string): void {
        // CPU utilization alarm
        const cpuAlarm = new cloudwatch.Alarm(this, `${customer.name}HTTPProxyCPUUtilization`, {
            alarmName: `${customer.name}HTTPProxyCPUUtilization`,
            alarmDescription: `${customer.name} HTTP proxy high CPU utilization`,
            comparisonOperator: cloudwatch.ComparisonOperator.GREATER_THAN_THRESHOLD,
            threshold: 80,
            evaluationPeriods: 2,
            metric: new cloudwatch.Metric({
                metricName: 'CPUUtilization',
                namespace: 'AWS/EC2',
                dimensionsMap: {
                    AutoScalingGroupName: asgName,
                },
            }),
            actionsEnabled: true,
        });

        cpuAlarm.addAlarmAction(new cw_actions.SnsAction(this.snsTopic));

        // Instance health alarm
        const healthAlarm = new cloudwatch.Alarm(this, `${customer.name}HTTPProxyInstanceHealth`, {
            alarmName: `${customer.name}HTTPProxyInstanceHealth`,
            alarmDescription: `${customer.name} HTTP proxy unhealthy instances`,
            comparisonOperator: cloudwatch.ComparisonOperator.LESS_THAN_THRESHOLD,
            threshold: customer.elasticIps.length,
            evaluationPeriods: 2,
            metric: new cloudwatch.Metric({
                metricName: 'GroupInServiceInstances',
                namespace: 'AWS/AutoScaling',
                dimensionsMap: {
                    AutoScalingGroupName: asgName,
                },
            }),
            actionsEnabled: true,
        });

        healthAlarm.addAlarmAction(new cw_actions.SnsAction(this.snsTopic));
    }
}
