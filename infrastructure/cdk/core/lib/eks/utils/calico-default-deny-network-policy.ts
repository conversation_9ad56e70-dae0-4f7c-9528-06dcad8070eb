import { KubernetesManifest, Cluster } from 'aws-cdk-lib/aws-eks';
import * as blueprints from '@aws-quickstart/eks-blueprints';

export function createCalicoDefaultDenyNetworkPolicy(
    clusterInfo: blueprints.ClusterInfo,
    namespace: string,
    eksVpcCidr: string,
    tgwCidr: string[] = [],
    vpnCird: string = ''
) {
    // @ts-ignore
    const cluster: Cluster = clusterInfo.cluster;

    const cidrs = [
        eksVpcCidr,

        // https://docs.aws.amazon.com/eks/latest/APIReference/API_KubernetesNetworkConfigRequest.html#AmazonEKS-Type-KubernetesNetworkConfigRequest-serviceIpv4Cidr
        // If you don't specify a block, Kubernetes assigns addresses from either the **********/16 or **********/16 CIDR blocks
        eksVpcCidr.startsWith('10.') ? '**********/16' : '**********/16',

        // EC2 METADATA ENDPOINT
        '***************/32',
        ...tgwCidr,
    ];

    const manifest = getPolicyManifest(namespace, cidrs, vpnCird);

    new KubernetesManifest(cluster.stack, `DefaultDenyNetPolicyAddon-${namespace}`, {
        cluster,
        manifest,
        overwrite: true,
    });
}

function getPolicyManifest(namespace: string, cidrs: string[], vpnCidr: string): any {
    // Safety check
    // Deploying a default deny to any of the following namespaces can potentially break the cluster.
    // If KubeAPI or Calico API gets blocked the only way to fix it would be creating a new cluster!
    if (['calico-apiserver', 'calico-operator', 'calico-system', 'kube-system'].indexOf(namespace) > -1) {
        throw new Error(`Danger! Default deny policy for namespace ${namespace} can permanently break this cluster!`);
    }

    return [
        {
            apiVersion: 'projectcalico.org/v3',
            kind: 'NetworkPolicy',
            metadata: {
                name: 'default-deny',
                namespace: namespace,
            },
            spec: {
                //namespaceSelector : "has(kubernetes.io/metadata.name) && kubernetes.io/metadata.name not in {\"kube-system\", \"calico-system\", \"calico-apiserver\", \"cert-manager\", \"kube-node-lease\", \"tigera-operator\", \"falco\", \"keda\", \"refinery\"}",
                types: ['Ingress', 'Egress'],
                egress: [
                    {
                        action: 'Allow',
                        destination: {
                            notNets: cidrs,
                        },
                    },
                    {
                        action: 'Allow',
                        destination: {
                            nets: [
                                // Private subnets in SecOps (internal load balancers run on these)
                                '192.168.48.0/20',
                                '192.168.64.0/20',
                                '192.168.80.0/20',
                            ],
                        },
                    },
                    {
                        action: 'Allow',
                        destination: {
                            services: {
                                name: 'kube-dns',
                                namespace: 'kube-system',
                            },
                        },
                    },
                    {
                        action: 'Allow',
                        destination: {
                            services: {
                                name: 'prefect-server',
                                namespace: 'prefect',
                            },
                        },
                    },
                    {
                        action: 'Allow',
                        destination: {
                            services: {
                                name: 'refinery',
                                namespace: 'refinery',
                            },
                        },
                    },
                    {
                        action: 'Allow',
                        destination: {
                            services: {
                                name: 'temporal-frontend',
                                namespace: 'temporal',
                            },
                        },
                    },
                    {
                        action: 'Deny',
                        destination: {
                            services: {
                                name: 'kubernetes',
                                namespace: 'default',
                            },
                        },
                    },
                ],
                ingress: vpnCidr
                    ? [
                          {
                              action: 'Allow',
                              destination: {
                                  nets: [vpnCidr], // VPN subnet
                              },
                          },
                      ]
                    : [],
            },
        },
    ];
}
