import { KubernetesManifest } from 'aws-cdk-lib/aws-eks';
import * as blueprints from '@aws-quickstart/eks-blueprints';

export function createExternalSecretsClusterSecretStore(
    clusterInfo: blueprints.ClusterInfo,
    region: string,
    serviceAccountName: string = 'external-secrets-sa'
) {
    const namespace = 'external-secrets'; // Hard-coded to external-secrets

    // Assume service account already exists - no need to create it

    // Create ClusterSecretStore
    const clusterSecretStore = new KubernetesManifest(clusterInfo.cluster.stack, 'ClusterSecretStore', {
        cluster: clusterInfo.cluster,
        manifest: [
            {
                apiVersion: 'external-secrets.io/v1beta1',
                kind: 'ClusterSecretStore',
                metadata: {
                    name: 'default',
                },
                spec: {
                    provider: {
                        aws: {
                            service: 'SecretsManager',
                            region: region,
                            auth: {
                                jwt: {
                                    serviceAccountRef: {
                                        name: serviceAccountName,
                                        namespace: namespace,
                                    },
                                },
                            },
                        },
                    },
                },
            },
        ],
    });

    // Ensure the ClusterSecretStore references the existing service account
    return clusterSecretStore;
}
