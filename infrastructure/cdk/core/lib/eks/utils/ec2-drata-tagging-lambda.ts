import * as cdk from 'aws-cdk-lib';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';
import { LambdaConstruct } from '../../common/lambda/lambda-construct';
import { IamHelpers } from '../../common/utils/iam-helpers';
import { LambdaHelpers } from '../../common/utils/lambda-helpers';

export function createDrataExcludeTaggingLambda(scope: Construct, region: string) {
    const functionName = 'eks-tag-ec2-instances-for-drata-exclude';
    const roleName = IamHelpers.getRoleName(region, functionName);

    // ECR role allowing cleanup Lambda function to list and delete images in tagged repos
    const ecrCleanupLambdaRole = new iam.Role(scope, roleName, {
        assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
        description: 'Role assumed by lambda job for tagging ec2 instances',
        inlinePolicies: {
            DescribeACMCerts: new iam.PolicyDocument({
                statements: [
                    new iam.PolicyStatement({
                        resources: ['*'],
                        actions: ['ec2:DescribeInstances', 'ec2:CreateTags'],
                    }),
                ],
            }),
        },
    });

    const ecrCleanupLambda = new LambdaConstruct(scope, functionName, {
        name: functionName,
        runtime: LambdaHelpers.findLambdaRuntime('PYTHON_3_9'),
        lambdaPath: 'assets/lambda/eks-tag-ec2-instances-for-drata-exclude',
        handler: 'main.handler',
        region: region,
        timeout: cdk.Duration.minutes(2),
        lambdaRole: ecrCleanupLambdaRole,
        environment: {
            REGION: region,
            DRYRUN: 'false',
        },
    });

    // Run once a day
    const eventRule = new events.Rule(scope, 'scheduleRule', {
        schedule: events.Schedule.cron({ hour: '*' }),
    });
    eventRule.addTarget(new targets.LambdaFunction(ecrCleanupLambda.lambdaFunction));
}
