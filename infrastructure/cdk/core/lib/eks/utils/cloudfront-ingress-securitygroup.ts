import { Tags } from 'aws-cdk-lib';
import { SecurityGroup, Peer, Port, IVpc } from 'aws-cdk-lib/aws-ec2';
import { Construct } from 'constructs';

export function createCFIngressOnlySecurityGroup(scope: Construct, vpc: IVpc, region: string): SecurityGroup {
    // Static map of IDs for `com.amazonaws.global.cloudfront.origin-facing`
    const cloudFrontPrefixId = new Map<string, string>([
        ['us-east-1', 'pl-3b927c52'],
        ['us-east-2', 'pl-b6a144df'],
        ['us-west-1', 'pl-4ea04527'],
        ['us-west-2', 'pl-82a045eb'],
    ]).get(region);
    if (!cloudFrontPrefixId) {
        throw new Error(`Did not find CloudFront prefix id for current region`);
    }

    const cfIngressSG = new SecurityGroup(scope, 'CloudFrontIngressOnlyForEKS', {
        vpc: vpc,
        securityGroupName: 'CloudFrontIngressOnly',
        allowAllOutbound: true,
        description: 'security group inbound traffic from CloudFront to ',
    });

    // Why? From docs `Both name or ID of securityGroups are supported. Name matches a Name tag, not the groupName attribute.`
    Tags.of(cfIngressSG).add('Name', 'CloudFrontIngressOnly');
    Tags.of(cfIngressSG).add('name', 'CloudFrontIngressOnly');

    // IMPORTANT NOTE: the following counts as 55 (out of 60) security group
    // rule entry allowance.
    cfIngressSG.addIngressRule(
        Peer.prefixList(cloudFrontPrefixId),
        Port.tcp(443),
        'Allow HTTPs from CloudFront origin facing IPs '
    );
    return cfIngressSG;
}
