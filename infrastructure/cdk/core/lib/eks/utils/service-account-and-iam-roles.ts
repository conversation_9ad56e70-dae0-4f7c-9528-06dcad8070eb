import { KubernetesManifest } from 'aws-cdk-lib/aws-eks';
import { PolicyStatementProps, PolicyStatement, ManagedPolicy } from 'aws-cdk-lib/aws-iam';
import * as policies from '../../../assets/eks/service-accounts/policies.json';
import { IamMappedServiceAccount } from '../config';
import * as blueprints from '@aws-quickstart/eks-blueprints';

export function createServiceAccountAndIamRoles(
    clusterInfo: blueprints.ClusterInfo,
    serviceAccounts: IamMappedServiceAccount[]
) {
    for (const account of serviceAccounts) {
        if (account.createNamespace) {
            new KubernetesManifest(clusterInfo.cluster.stack, `${account.namespace}-namespace`, {
                cluster: clusterInfo.cluster,
                manifest: [
                    {
                        apiVersion: 'v1',
                        kind: 'Namespace',
                        metadata: {
                            name: account.namespace,
                        },
                    },
                ],
                overwrite: true,
            });
        }

        const sa = clusterInfo.cluster.addServiceAccount(`${account.name}-sa`, {
            name: account.name,
            namespace: account.namespace,
        });

        // This is to allow for string indexes
        interface obj {
            [key: string]: object[];
        }

        // Custom policies
        account.customPolicies.forEach((policyName) => {
            const p: obj = policies;
            p[policyName].forEach((statement: PolicyStatementProps) => {
                sa.role.addToPrincipalPolicy(new PolicyStatement(statement));
            });
        });

        // AWS managed policies
        account.managedPolicies.forEach((policyName) => {
            const cloudWatchAgentPolicy = ManagedPolicy.fromAwsManagedPolicyName(policyName);
            sa.role.addManagedPolicy(cloudWatchAgentPolicy);
        });
    }
}
