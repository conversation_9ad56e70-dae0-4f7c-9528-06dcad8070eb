import { KubernetesManifest, Cluster } from 'aws-cdk-lib/aws-eks';
import * as blueprints from '@aws-quickstart/eks-blueprints';

export interface EfsStorageClassAddonProps {
    efsId: string;
}

export class EfsStorageClassAddon implements blueprints.ClusterAddOn {
    readonly options: EfsStorageClassAddonProps;

    constructor(props: EfsStorageClassAddonProps) {
        this.options = props;
    }

    deploy(clusterInfo: blueprints.ClusterInfo): void {
        // @ts-ignore
        const cluster: Cluster = clusterInfo.cluster;

        const storageClassManifest = {
            apiVersion: 'storage.k8s.io/v1',
            kind: 'StorageClass',
            metadata: {
                name: 'efs-sc',
            },
            provisioner: 'efs.csi.aws.com',
            parameters: {
                provisioningMode: 'efs-ap',
                fileSystemId: this.options.efsId,
                directoryPerms: '700',
                basePath: '/dynamic_provisioning',
            },
            reclaimPolicy: 'Retain',
            mountOptions: ['tls'],
            volumeBindingMode: 'Immediate',
        };

        new KubernetesManifest(cluster.stack, 'EfsStorageClassManifest', {
            cluster,
            manifest: [storageClassManifest],
            overwrite: true,
        });
    }
}
