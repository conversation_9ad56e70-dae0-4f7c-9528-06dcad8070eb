import {
    HelmAddOn,
    ClusterInfo,
    HelmAddOnUserProps,
    HelmAddOnProps,
    CertManagerAddOn,
    Values,
} from '@aws-quickstart/eks-blueprints';
import { dependable, supportsALL } from '@aws-quickstart/eks-blueprints/dist/utils';
import { Duration } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { merge } from 'ts-deepmerge';

/**
 * User provided options for the Helm Chart
 */
export interface CertManagerCsiDriverAddOnProps extends HelmAddOnUserProps {
    version: string;
}

/**
 * Default props to be used when creating the Helm chart
 */
const defaultProps: HelmAddOnProps & CertManagerCsiDriverAddOnProps = {
    name: 'blueprints-cert-manager-csi-driver-addon',
    namespace: 'cert-manager',
    chart: 'cert-manager-csi-driver',
    version: 'v0.7.0',
    release: 'cert-manager-csi-driver',
    repository: 'https://charts.jetstack.io',
    values: {},
};

@supportsALL
export class CertManagerCsiDriver extends HelmAddOn {
    readonly options: CertManagerCsiDriverAddOnProps;

    constructor(props?: CertManagerCsiDriverAddOnProps) {
        super({ ...defaultProps, ...props });
        this.options = this.props as CertManagerCsiDriverAddOnProps;
    }

    @dependable(CertManagerAddOn.name)
    deploy(clusterInfo: ClusterInfo): Promise<Construct> {
        // Merge and render values
        let values: Values = populateValues(this.options);
        values = merge(values, this.props.values ?? {});

        // Deploy chart
        const chart = this.addHelmChart(clusterInfo, values, undefined, true, Duration.minutes(5));
        return Promise.resolve(chart);
    }
}

/**
 * populateValues populates the appropriate values used to customize the Helm chart
 * @param helmOptions User provided values to customize the chart
 */
function populateValues(helmOptions: CertManagerCsiDriverAddOnProps): Values {
    const values = helmOptions.values ?? {};
    return values;
}
