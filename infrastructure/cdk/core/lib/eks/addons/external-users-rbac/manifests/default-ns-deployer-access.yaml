---
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
    name: deployer-role
    namespace: default
rules:
    - apiGroups:
          - ''
          - 'apps'
          - 'batch'
          - 'extensions'
          - 'networking.k8s.io'
          - 'secrets-store.csi.x-k8s.io'
          - 'crd.projectcalico.org'
          - 'keda.sh'
          - 'external-secrets.io'
      resources:
          - 'configmaps'
          - 'cronjobs'
          - 'deployments'
          - 'events'
          - 'ingresses'
          - 'jobs'
          - 'pods'
          - 'pods/attach'
          - 'pods/exec'
          - 'pods/log'
          - 'pods/portforward'
          - 'secrets'
          - 'services'
          - 'ingresses'
          - 'replicasets'
          - 'networkpolicies'
          - 'secretproviderclasses'
          - 'scaledobjects'
          - 'persistentvolumes'
          - 'persistentvolumeclaims'
          - 'externalsecrets'
      verbs:
          - 'create'
          - 'delete'
          - 'describe'
          - 'get'
          - 'list'
          - 'patch'
          - 'update'
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
    name: deployer-role-binding
    namespace: default
subjects:
    - kind: User
      name: deployer-user
roleRef:
    kind: Role
    name: deployer-role
    apiGroup: rbac.authorization.k8s.io
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
    name: deployer-cluster-role
rules:
    - apiGroups:
        - ''
      resources:
        - 'namespaces'
      verbs:
        - 'get'
        - 'list'
        - 'watch'
        - 'create'
        - 'update'
        - 'patch'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
    name: deployer-cluster-role-binding
subjects:
    - kind: User
      name: deployer-user
roleRef:
    kind: ClusterRole
    name: deployer-cluster-role
    apiGroup: rbac.authorization.k8s.io