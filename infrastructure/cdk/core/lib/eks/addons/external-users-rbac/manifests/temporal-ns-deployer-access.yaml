---
apiVersion: v1
kind: Namespace
metadata:
  name: temporal
---
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
    name: deployer-role
    namespace: temporal
rules:
    - apiGroups:
          - ''
          - 'apps'
          - 'batch'
          - 'extensions'
          - 'networking.k8s.io'
          - 'secrets-store.csi.x-k8s.io'
          - 'crd.projectcalico.org'
          - 'keda.sh'
          - 'autoscaling'
          - 'external-secrets.io'
      resources:
          - 'configmaps'
          - 'cronjobs'
          - 'deployments'
          - 'events'
          - 'ingresses'
          - 'jobs'
          - 'pods'
          - 'pods/attach'
          - 'pods/exec'
          - 'pods/log'
          - 'pods/portforward'
          - 'secrets'
          - 'services'
          - 'ingresses'
          - 'replicasets'
          - 'networkpolicies'
          - 'secretproviderclasses'
          - 'scaledobjects'
          - 'horizontalpodautoscalers'
          - 'externalsecrets'
      verbs:
          - 'create'
          - 'delete'
          - 'describe'
          - 'get'
          - 'list'
          - 'patch'
          - 'update'
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
    name: deployer-role-binding
    namespace: temporal
subjects:
    - kind: User
      name: deployer-user
roleRef:
    kind: Role
    name: deployer-role
    apiGroup: rbac.authorization.k8s.io
