---
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
    name: reader-role
    namespace: default
rules:
    - apiGroups:
          - ''
          - 'apps'
          - 'batch'
          - 'extensions'
      resources:
          - 'configmaps'
          - 'cronjobs'
          - 'deployments'
          - 'events'
          - 'ingresses'
          - 'jobs'
          - 'pods'
          - 'pods/log'
          - 'services'
      verbs:
          - 'describe'
          - 'get'
          - 'list'
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
    name: reader-role-binding
    namespace: default
subjects:
    - kind: User
      name: reader-user
roleRef:
    kind: Role
    name: reader-role
    apiGroup: rbac.authorization.k8s.io
