---
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
    name: deployer-role
    namespace: prefect
rules:
    - apiGroups:
          - ''
          - 'apps'
          - 'batch'
          - 'extensions'
          - 'networking.k8s.io'
          - 'secrets-store.csi.x-k8s.io'
          - 'crd.projectcalico.org'
          - 'keda.sh'
          - 'policy'
          - 'rbac.authorization.k8s.io'
          - 'autoscaling'
      resources:
          - 'configmaps'
          - 'cronjobs'
          - 'deployments'
          - 'events'
          - 'ingresses'
          - 'jobs'
          - 'pods'
          - 'pods/attach'
          - 'pods/exec'
          - 'pods/log'
          - 'pods/portforward'
          - 'pods/status'
          - 'secrets'
          - 'services'
          - 'ingresses'
          - 'replicasets'
          - 'networkpolicies'
          - 'secretproviderclasses'
          - 'scaledobjects'
          - 'poddisruptionbudgets'
          - 'serviceaccounts'
          - 'statefulsets'
          - 'roles'
          - 'rolebindings'
          - 'horizontalpodautoscalers'
      verbs:
          - 'create'
          - 'delete'
          - 'describe'
          - 'get'
          - 'list'
          - 'patch'
          - 'update'
          - 'watch'
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
    name: deployer-role-binding
    namespace: prefect
subjects:
    - kind: User
      name: deployer-user
roleRef:
    kind: Role
    name: deployer-role
    apiGroup: rbac.authorization.k8s.io
