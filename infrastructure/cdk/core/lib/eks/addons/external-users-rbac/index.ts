import { KubernetesManifest, Cluster } from 'aws-cdk-lib/aws-eks';
import { Role, AccountPrincipal } from 'aws-cdk-lib/aws-iam';
import * as blueprints from '@aws-quickstart/eks-blueprints';
import * as path from 'path';

export interface ExternalUsersRBACAddonProps {
    awsOrgRootAccountID: string;
    clusterName: string;
    region: string;
    enable_addon_prefect_external_rbac: boolean;
}

export class ExternalUsersRBACAddon implements blueprints.ClusterAddOn {
    readonly options: ExternalUsersRBACAddonProps;

    constructor(props: ExternalUsersRBACAddonProps) {
        this.options = props as ExternalUsersRBACAddonProps;
    }

    deploy(clusterInfo: blueprints.ClusterInfo): void {
        // @ts-ignore
        const cluster: Cluster = clusterInfo.cluster;

        // Create IAM roles
        const { eksDeployerRole, eksReaderRole } = this.createIamRoles(clusterInfo);

        const manifests = [
            {
                fileName: 'default-ns-deployer-access.yaml',
                roles: [eksDeployerRole],
                username: 'deployer-user', // Comes from RoleBinding definition
            },
            {
                fileName: 'default-ns-reader-access.yaml',
                roles: [eksReaderRole],
                username: 'reader-user', // Comes from RoleBinding definition
            },
        ];

        if (this.options.enable_addon_prefect_external_rbac) {
            manifests.push({
                fileName: 'prefect-ns-deployer-access.yaml',
                roles: [eksDeployerRole],
                username: 'deployer-user', // Comes from RoleBinding definition
            });
        }

        if (this.options.enable_addon_prefect_external_rbac) {
            manifests.push({
                fileName: 'temporal-ns-deployer-access.yaml',
                roles: [eksDeployerRole],
                username: 'deployer-user', // Comes from RoleBinding definition
            });
        }

        const manifestDir = path.join(__dirname, 'manifests/');

        for (const item of manifests) {
            // Apply manifest
            const doc = blueprints.utils.readYamlDocument(path.join(manifestDir, item.fileName));
            // ... apply any substitutions for dynamic values
            const manifest = doc.split('---').map((e) => blueprints.utils.loadYaml(e));
            new KubernetesManifest(cluster.stack, `ExternalUsersRBACAddon-${item.fileName}`, {
                cluster,
                manifest,
                overwrite: true,
            });

            // Create IAM role mapping for each role-user
            item.roles.forEach((role: Role) => {
                cluster.awsAuth.addRoleMapping(role, {
                    username: item.username,
                    groups: [''],
                });
            });
        }
    }

    private createIamRoles(clusterInfo: blueprints.ClusterInfo): { eksDeployerRole: Role; eksReaderRole: Role } {
        const region = this.options.region;
        const clusterName = this.options.clusterName;
        const awsOrgRootAccountID = this.options.awsOrgRootAccountID;

        // Create k8s deployer role
        // This role is assumed by AllK8sClustersDefaultNamespaceDeployAccess policy in
        // root management account. It provide cross account access to EKS clusters
        // for IAM users.
        const eksDeployerRole = new Role(clusterInfo.cluster.stack, `EksBpDeployerRole-${clusterName}`, {
            roleName: `EksBpDeployerRole-${clusterName}-${region}`,
            assumedBy: new AccountPrincipal(awsOrgRootAccountID),
            description: 'Role used by CI/CD pipeline for deploying resources to k8s',
        });

        // Create k8s reader role
        // This role is assumed by AllK8sClustersDefaultNamespaceReadAccess policy in
        // root management account. It provides cross-account access to EKS clusters
        // for IAM users.
        const eksReaderRole = new Role(clusterInfo.cluster.stack, `EksBpReaderRole-${clusterName}`, {
            roleName: `EksBpReaderRole-${clusterName}-${region}`,
            assumedBy: new AccountPrincipal(awsOrgRootAccountID),
            description: 'Role to allow read only access to k8s resources',
        });

        return { eksDeployerRole, eksReaderRole };
    }
}
