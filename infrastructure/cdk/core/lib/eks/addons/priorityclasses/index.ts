import { KubernetesManifest, Cluster } from 'aws-cdk-lib/aws-eks';
import * as blueprints from '@aws-quickstart/eks-blueprints';
import * as path from 'path';

export interface PriorityClassAddonProps {}

export class PriorityClassAddon implements blueprints.ClusterAddOn {
    readonly options: PriorityClassAddonProps;

    constructor(props?: PriorityClassAddonProps) {
        this.options = props as PriorityClassAddonProps;
    }

    deploy(clusterInfo: blueprints.ClusterInfo): void {
        // @ts-ignore
        const cluster: Cluster = clusterInfo.cluster;

        const manifests = ['high-priority.yaml', 'medium-priority.yaml', 'low-priority.yaml'];

        const manifestDir = path.join(__dirname, 'manifests/');

        for (const fileName of manifests) {
            // Apply manifest
            const doc = blueprints.utils.readYamlDocument(path.join(manifestDir, fileName));
            // ... apply any substitutions for dynamic values
            const manifest = doc.split('---').map((e) => blueprints.utils.loadYaml(e));
            new KubernetesManifest(cluster.stack, `PriorityClassAddon-${fileName}`, {
                cluster,
                manifest,
                overwrite: true,
            });
        }
    }
}
