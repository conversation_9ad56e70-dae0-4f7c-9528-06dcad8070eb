# Refinery Proxy Deployment

## Updating rules

Simply update the information under the `rules:` key in yaml file for your target environment. Once changes have been merged into main, CI/CD will take care of deploying the new rules.

## Accessing Refinery service from other Kubernetes pods

Refinery service can be reached via `refinery.refinery.svc.cluster.local` from within Kubernetes clusters.
It uses a ClusterIP service with the following ports exposed:
`80/TCP,4317/TCP,9090/TCP`

## Testing Refinery Service Endpoint

Go to any of the instances in the cluster environment and execute the below:

```bash
export HONEYCOMB_API_KEY=<LOCAL_ENVIRONMENT_HONEYCOMB_API_KEY>
curl -i http://refinery.refinery.svc.cluster.local/v1/traces -X POST -H "Content-Type: application/json" -H "x-honeycomb-team: $HONEYCOMB_API_KEY" -d '{ "resourceSpans": [ { "resource": { "attributes": [ { "key": "service.name", "value": { "stringValue": "test-with-curl" } } ] }, "scopeSpans": [ { "scope": { "name": "manual-test" }, "spans": [ { "traceId": "71699b6fe85982c7c8995ea3d9c95df2", "spanId": "3c191d03fa8be065", "name": "spanitron", "kind": 2, "droppedAttributesCount": 0, "events": [], "droppedEventsCount": 0, "status": { "code": 1 } } ] } ] } ] }'
```

The above should create a new span for the`test-with-curl` service in the [local environment](https://ui.honeycomb.io/unblocked/environments/local/datasets/test-with-curl)
