import * as blueprints from '@aws-quickstart/eks-blueprints';
import { HelmAddOn, ClusterInfo, HelmAddOnUserProps, HelmAddOnProps, Values } from '@aws-quickstart/eks-blueprints';
import { createNamespace, readYamlDocument, setPath, supportsALL } from '@aws-quickstart/eks-blueprints/dist/utils';
import { KubernetesManifest } from 'aws-cdk-lib/aws-eks';
import { Construct } from 'constructs';
import { Duration } from 'aws-cdk-lib';
import * as path from 'path';
import { merge } from 'ts-deepmerge';

/**
 * User provided options for the Helm Chart
 */
export interface RefineryAddOnProps extends HelmAddOnUserProps {
    environment: string;
}

/**
 * Default props to be used when creating the Helm chart
 */
const defaultProps: HelmAddOnProps & RefineryAddOnProps = {
    name: 'blueprints-refinery-addon',
    namespace: 'refinery',
    chart: 'refinery',
    // Helm chart version (https://github.com/honeycombio/helm-charts/releases)
    version: '2.18.2',
    release: 'refinery',
    repository: 'https://honeycombio.github.io/helm-charts',
    values: {},
    environment: 'dev',
};

@supportsALL
export class RefineryAddon extends HelmAddOn {
    readonly options: RefineryAddOnProps;
    readonly apiKey: string;

    constructor(props?: RefineryAddOnProps) {
        super({ ...defaultProps, ...props });
        this.options = this.props as RefineryAddOnProps;

        // Load values file for target environment
        const valuesDir = path.join(__dirname, 'values/');
        const valuesFile = readYamlDocument(path.join(valuesDir, `${this.options.environment}.yaml`));
        this.options.values = blueprints.utils.loadYaml(valuesFile);

        if (!process.env.HONEYCOMB_API_KEY) {
            throw Error('HONEYCOMB_API_KEY environment variable is not defined');
        }
        this.apiKey = process.env.HONEYCOMB_API_KEY;
    }

    deploy(clusterInfo: ClusterInfo): Promise<Construct> {
        // Merge and render values
        let values: Values = populateValues(this.options);
        values = merge(values, this.props.values ?? {});
        const cluster = clusterInfo.cluster;

        // Create refinery namespace
        const namespace = createNamespace(this.options.namespace!, cluster, true, true);

        // Create api key kube secret
        const manifestDir = path.join(__dirname, 'manifests/');
        const keyDoc = blueprints.utils.readYamlDocument(path.join(manifestDir, 'secret.yaml'));
        const secretManifest = keyDoc.split('---').map((e) => blueprints.utils.loadYaml(e));
        setPath(secretManifest[0], 'stringData.api-key', this.apiKey);
        new KubernetesManifest(cluster.stack, 'refinery-secrets-env', {
            cluster: cluster,
            manifest: secretManifest,
            overwrite: true,
        }).node.addDependency(namespace);

        // Deploy chart
        const chart = this.addHelmChart(clusterInfo, values, undefined, true, Duration.minutes(5));
        chart.node.addDependency(namespace);
        return Promise.resolve(chart);
    }
}

/**
 * populateValues populates the appropriate values used to customize the Helm chart
 * @param helmOptions User provided values to customize the chart
 */
function populateValues(helmOptions: RefineryAddOnProps): Values {
    const values = helmOptions.values ?? {};
    return values;
}
