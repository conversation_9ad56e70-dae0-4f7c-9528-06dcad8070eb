REFINERY_CHART ?= 2.18.2

deploy-dev:
	AWS_PROFILE=dev helm upgrade --timeout 15m --install refinery honeycomb/refinery \
		--version $(REFINERY_CHART) \
		--values ./values/dev.yaml --create-namespace -n refinery
.PHONY: deploy-dev

deploy-prod:
	AWS_PROFILE=prod helm upgrade --timeout 15m --install refinery honeycomb/refinery \
		--version $(REFINERY_CHART) \
		--values ./values/prod.yaml --create-namespace -n refinery
.PHONY: deploy-prod

uninstall-dev:
	AWS_PROFILE=dev helm uninstall --timeout 15m -n refinery refinery
.PHONY: uninstall-dev

uninstall-prod:
	AWS_PROFILE=prod helm uninstall --timeout 15m -n refinery refinery
.PHONY: uninstall-prod
