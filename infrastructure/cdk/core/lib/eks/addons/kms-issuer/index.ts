import * as blueprints from '@aws-quickstart/eks-blueprints';
import {
    HelmAddOn,
    ClusterInfo,
    HelmAddOnUserProps,
    HelmAddOnProps,
    CertManagerAddOn,
    Values,
} from '@aws-quickstart/eks-blueprints';
import { KubernetesManifest } from 'aws-cdk-lib/aws-eks';
import { dependable, setPath, supportsALL } from '@aws-quickstart/eks-blueprints/dist/utils';
import { Construct } from 'constructs';
import { Duration } from 'aws-cdk-lib';
import * as path from 'path';
import { merge } from 'ts-deepmerge';
import * as iam from 'aws-cdk-lib/aws-iam';

/**
 * User provided options for the Helm Chart
 */
export interface KmsIssuerAddOnProps extends HelmAddOnUserProps {
    clusterName: string;
    commonName: string;
    awsRegion: string;
}

/**
 * Default props to be used when creating the Helm chart
 */
const defaultProps: HelmAddOnProps & KmsIssuerAddOnProps = {
    clusterName: '',
    commonName: '',
    awsRegion: '',
    name: 'blueprints-kms-issuer-addon',
    namespace: 'cert-manager',
    chart: 'kms-issuer',
    version: '1.0.2',
    release: 'kms-issuer',
    repository: 'https://skyscanner.github.io/kms-issuer',
    values: {
        serviceAccount: {
            create: false,
            name: 'kms-issuer',
        },
    },
};

@supportsALL
export class KmsIssuer extends HelmAddOn {
    readonly options: KmsIssuerAddOnProps;

    constructor(props?: KmsIssuerAddOnProps) {
        super({ ...defaultProps, ...props });
        this.options = this.props as KmsIssuerAddOnProps;
    }

    @dependable(CertManagerAddOn.name)
    deploy(clusterInfo: ClusterInfo): Promise<Construct> {
        const cluster = clusterInfo.cluster;
        const namespace = this.options.namespace || 'cert-manager';

        // Create service account
        const serviceAccountName = 'kms-issuer';
        const sa = cluster.addServiceAccount(serviceAccountName, {
            name: serviceAccountName,
            namespace: namespace,
        });

        const keyName = `eks-mtls-kmsissuer-key-${this.options.clusterName}`;
        const keyAlias = `alias/kms-issuer-eks-mtls-key-${this.options.clusterName}`;

        // Apply additional IAM policies to the service account.
        const policies = this.getIAMPolicies(keyName, keyAlias);
        policies.forEach((policy: iam.PolicyStatement) => sa.addToPrincipalPolicy(policy));

        const manifestDir = path.join(__dirname, 'manifests/');
        const keyDoc = blueprints.utils.readYamlDocument(path.join(manifestDir, 'kms-key.yaml'));
        // ... apply any substitutions for dynamic values
        const keyManifest = keyDoc.split('---').map((e) => blueprints.utils.loadYaml(e));

        // Create helm chart
        let values: Values = populateValues(this.options);
        values = merge(values, this.props.values ?? {});
        const chart = this.addHelmChart(clusterInfo, values, undefined, true, Duration.minutes(5));
        chart.node.addDependency(sa);

        // Create KMS key resource
        setPath(keyManifest[1], 'metadata.name', keyName);
        setPath(keyManifest[1], 'spec.aliasName', keyAlias);

        new KubernetesManifest(cluster.stack, 'kms-issuer-kms-key', {
            cluster: cluster,
            manifest: keyManifest,
            overwrite: true,
        }).node.addDependency(chart);

        const rootCaDoc = blueprints.utils.readYamlDocument(path.join(manifestDir, 'root-ca.yaml'));
        // ... apply any substitutions for dynamic values
        const rootCaManifest = rootCaDoc.split('---').map((e) => blueprints.utils.loadYaml(e));
        setPath(rootCaManifest[1], 'spec.commonName', this.options.commonName);
        setPath(rootCaManifest[1], 'spec.keyId', keyAlias);

        // Create CA root cert
        new KubernetesManifest(cluster.stack, 'kms-issuer-root-ca-cert', {
            cluster: cluster,
            manifest: rootCaManifest,
            overwrite: true,
        }).node.addDependency(chart);

        return Promise.resolve(chart);
    }

    private getIAMPolicies(keyName: string, keyAlias: string): iam.PolicyStatement[] {
        const p: iam.PolicyStatement[] = [];
        // For future me https://dev.to/rebrowning/aws-iam-and-other-permission-gotchas-bme
        p.push(
            // Grant alias key access using alias
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'kms:CreateAlias',
                    'kms:Verify',
                    'kms:Sign',
                    'kms:GetPublicKey',
                    'kms:DescribeKey',
                    'kms:TagResource',
                ],
                resources: [`*`],
                conditions: {
                    StringLike: {
                        'kms:RequestAlias': keyAlias,
                    },
                },
            })
        );

        p.push(
            // Grant full control of the key using key arn
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['*'],
                resources: [`arn:aws:kms:*:*:key/${keyName}`],
            })
        );

        p.push(
            // Grant full control on alias
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['*'],
                resources: [`arn:aws:kms:*:*:${keyAlias}`],
            })
        );

        p.push(
            // Allow controller to create aliases for its key(s)
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['kms:CreateAlias', 'kms:GetPublicKey', 'kms:DescribeKey', 'kms:TagResource'],
                resources: [`*`],
                conditions: {
                    StringLike: {
                        'aws:ResourceTag/project': 'kms-issuer',
                    },
                },
            })
        );

        p.push(
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['kms:CreateKey', 'kms:TagResource', 'kms:DescribeKey'],
                resources: ['*'],
            })
        );

        return p;
    }
}

/**
 * populateValues populates the appropriate values used to customize the Helm chart
 * @param helmOptions User provided values to customize the chart
 */
function populateValues(helmOptions: KmsIssuerAddOnProps): Values {
    const values = helmOptions.values ?? {};
    setPath(values, 'serviceAccount.create', false);
    setPath(values, 'env', [{ name: 'AWS_REGION', value: helmOptions.awsRegion }]);
    return values;
}
