import * as blueprints from '@aws-quickstart/eks-blueprints';
import { EksCluster } from '../config';
import { CertManagerCsiDriver } from './cert-manager-csi-driver';
import { EfsStorageClassAddon } from './efs-storageclass';
import { ExternalUsersRBACAddon } from './external-users-rbac';
import { FalcoAddon } from './falco';
import { GrafanaAddon } from './grafana';
import { KmsIssuer } from './kms-issuer';
import { PriorityClassAddon } from './priorityclasses';
import { RefineryAddon } from './refinery';

export function generateListOfAddons(
    clusterConfig: EksCluster,
    region: string,
    env: string,
    awsOrgRootAccountID: string
): Array<blueprints.ClusterAddOn> {
    // Keda service auto-scaling params
    const kedaParams = {
        podSecurityContextFsGroup: 1001,
        securityContextRunAsGroup: 1001,
        securityContextRunAsUser: 1001,
        irsaRoles: ['CloudWatchFullAccess'],
    };

    const addons: Array<blueprints.ClusterAddOn> = [
        new blueprints.addons.CalicoOperatorAddOn({
            version: 'v3.29.3',
            values: {
                installation: {
                    registry: 'quay.io',
                },
                node: {
                    image: {
                        repository: 'calico/node',
                        tag: 'v3.29.3',
                    },
                },
                cni: {
                    image: {
                        repository: 'calico/cni',
                        tag: 'v3.29.3',
                    },
                },
                flexvol: {
                    image: {
                        repository: 'calico/pod2daemon-flexvol',
                        tag: 'v3.29.3',
                    },
                },
                typha: {
                    image: {
                        repository: 'calico/typha',
                        tag: 'v3.29.3',
                    },
                },
                apiserver: {
                    image: {
                        repository: 'calico/apiserver',
                        tag: 'v3.29.3',
                    },
                },
                kubeControllers: {
                    image: {
                        repository: 'calico/kube-controllers',
                        tag: 'v3.29.3',
                    },
                },
            },
        }),
        new blueprints.addons.MetricsServerAddOn(),
        new blueprints.addons.ClusterAutoScalerAddOn(),
        new blueprints.addons.AwsLoadBalancerControllerAddOn(),
        new blueprints.addons.VpcCniAddOn(),
        new blueprints.addons.CoreDnsAddOn(),
        new blueprints.addons.ExternalsSecretsAddOn({}),
        new blueprints.addons.ExternalDnsAddOn({
            hostedZoneResources: ['HostedZone'],
        }),
        //  Use this command to check for version compatibility and get correct KubeProxy version
        //         aws eks describe-addon-versions --addon-name kube-proxy  --kubernetes-version 1.29 \
        //         --query "addons[].addonVersions[].[addonVersion, compatibilities[].defaultVersion]" \
        //         --output text
        new blueprints.addons.KubeProxyAddOn(clusterConfig.kubeProxyAddOnVersion),
        // Post deployment/upgrade issues
        // SecretStoreAddon and csi-secret-store-driver has been a problematic component
        // Sometimes after a CDK blueprint version upgrade this addon is automatically upgrades
        // But the upgrade causes it fail to start. In this type of failure the only fix is to
        // restart the system post deployment using the following commands
        //     kubectl rollout restart daemonset/blueprints-addon-secret-store-csi-driver-secrets-store-csi-driv -n kube-system
        //     kubectl rollout restart daemonset/csi-secrets-store-provider-aws -n kube-system
        new blueprints.addons.SecretsStoreAddOn({
            values: {
                linux: {
                    priorityClassName: 'high-priority',
                    registrar: {
                        resources: {
                            requests: {
                                cpu: '100m',
                            },
                            limits: {
                                cpu: '300m',
                            },
                        },
                    },
                },
            },
        }),
        new blueprints.addons.KedaAddOn(kedaParams),
        new blueprints.addons.CertManagerAddOn(),
        new blueprints.addons.EbsCsiDriverAddOn(),
    ];

    // Conditionally add extra addons
    if (clusterConfig.enable_addon_falco) {
        addons.push(new FalcoAddon(clusterConfig.falcoAddOnVersion, { environment: env }));
    }

    if (clusterConfig.enable_addon_grafana) {
        addons.push(new GrafanaAddon({ clusterName: `${clusterConfig.name}-${region}` }));
    }

    if (clusterConfig.enable_addon_refinery) {
        addons.push(new RefineryAddon({ environment: env, namespace: 'refinery' }));
    }

    if (clusterConfig.enable_addon_gpu_operator) {
        addons.push(new blueprints.addons.GpuOperatorAddon());
    }

    if (clusterConfig.efs_addon_fs_id) {
        addons.push(new EfsStorageClassAddon({ efsId: clusterConfig.efs_addon_fs_id }));
        addons.push(new blueprints.addons.EfsCsiDriverAddOn());
    }

    if (clusterConfig.enable_addon_priority_class) {
        addons.push(new PriorityClassAddon());
    }

    if (clusterConfig.enable_addon_cert_manager_csi) {
        addons.push(new CertManagerCsiDriver());
    }

    if (clusterConfig.enable_addon_kms_issuer) {
        addons.push(
            new KmsIssuer({
                clusterName: clusterConfig.name,
                commonName: `${region}.${env}.getunblocked.com`,
                awsRegion: region,
            })
        );
    }

    if (clusterConfig.enable_addon_external_rbac) {
        addons.push(
            new ExternalUsersRBACAddon({
                awsOrgRootAccountID: awsOrgRootAccountID,
                clusterName: clusterConfig.name,
                region: region,
                enable_addon_prefect_external_rbac: clusterConfig.enable_addon_prefect_external_rbac,
            })
        );
    }

    return addons;
}
