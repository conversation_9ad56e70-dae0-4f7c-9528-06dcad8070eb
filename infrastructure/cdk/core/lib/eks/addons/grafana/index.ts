import * as blueprints from '@aws-quickstart/eks-blueprints';
import { HelmAddOn, ClusterInfo, HelmAddOnUserProps, HelmAddOnProps, Values } from '@aws-quickstart/eks-blueprints';
import { readYamlDocument, setPath, supportsALL } from '@aws-quickstart/eks-blueprints/dist/utils';
import { Construct } from 'constructs';
import { Duration } from 'aws-cdk-lib';
import * as path from 'path';
import { merge } from 'ts-deepmerge';

/**
 * User provided options for the Helm Chart
 */
export interface GrafanaAddOnProps extends HelmAddOnUserProps {
    clusterName: string;
}

/**
 * Default props to be used when creating the Helm chart
 */
const defaultProps: HelmAddOnProps & GrafanaAddOnProps = {
    name: 'blueprints-grafana-addon',
    namespace: 'grafana',
    chart: 'k8s-monitoring',
    version: '0.10.1',
    release: 'grafana-k8s-monitoring',
    repository: 'https://grafana.github.io/helm-charts',
    values: {},
    clusterName: 'dev-unassigned',
};

@supportsALL
export class GrafanaAddon extends HelmAddOn {
    readonly options: GrafanaAddOnProps;
    readonly grafanaK8sMetricsToken: string;

    constructor(props?: GrafanaAddOnProps) {
        super({ ...defaultProps, ...props });
        this.options = this.props as GrafanaAddOnProps;

        // Load values file for target environment
        const valuesFile = readYamlDocument(path.join(__dirname, 'values/values.yaml'));
        this.options.values = blueprints.utils.loadYaml(valuesFile);

        // Get Grafana k8s metrics token from env var (GH secrets)
        if (!process.env.GRAFANA_K8S_METRICS_TOKEN) {
            throw Error('GRAFANA_K8S_METRICS_TOKEN environment variable is not defined');
        }
        this.grafanaK8sMetricsToken = process.env.GRAFANA_K8S_METRICS_TOKEN;
    }

    deploy(clusterInfo: ClusterInfo): Promise<Construct> {
        // Merge and render values
        let values: Values = populateValues(this.options, this.options.clusterName, this.grafanaK8sMetricsToken);
        values = merge(values, this.props.values ?? {});

        // Deploy chart
        const chart = this.addHelmChart(clusterInfo, values, true, true, Duration.minutes(5));
        return Promise.resolve(chart);
    }
}

/**
 * populateValues populates the appropriate values used to customize the Helm chart
 * @param helmOptions User provided values to customize the chart
 */
function populateValues(helmOptions: GrafanaAddOnProps, clusterName: string, grafanaK8sMetricsToken: string): Values {
    const values = helmOptions.values ?? {};
    setPath(values, 'cluster.name', clusterName);
    setPath(values, 'externalServices.prometheus.basicAuth.password', grafanaK8sMetricsToken);
    setPath(values, 'externalServices.loki.basicAuth.password', grafanaK8sMetricsToken);
    setPath(values, 'externalServices.tempo.basicAuth.password', grafanaK8sMetricsToken);
    return values;
}
