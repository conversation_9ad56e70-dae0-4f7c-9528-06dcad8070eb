cluster:
    name: unassigned-fix-it
externalServices:
    prometheus:
        host: https://prometheus-prod-10-prod-us-central-0.grafana.net
        basicAuth:
            username: '321364'
            password: REPLACE_WITH_ACCESS_POLICY_TOKEN
    loki:
        host: https://logs-prod3.grafana.net
        basicAuth:
            username: '159652'
            password: REPLACE_WITH_ACCESS_POLICY_TOKEN
    tempo:
        host: https://tempo-us-central1.grafana.net:443
        basicAuth:
            username: '156162'
            password: REPLACE_WITH_ACCESS_POLICY_TOKEN
metrics:
    enabled: true
    cost:
        enabled: false
    node-exporter:
        enabled: true
logs:
    enabled: true
    pod_logs:
        enabled: false
    cluster_events:
        enabled: true
traces:
    enabled: false
opencost:
    enabled: false
kube-state-metrics:
    enabled: true
prometheus-node-exporter:
    enabled: true
prometheus-operator-crds:
    enabled: true
grafana-agent: {}
grafana-agent-logs: {}
