import { ManagedNodeGroup, MngClusterProviderProps } from '@aws-quickstart/eks-blueprints';
import { CfnOutput, Stack, StackProps } from 'aws-cdk-lib';
import { InstanceType, SecurityGroup, Peer, Port, Vpc, IVpc } from 'aws-cdk-lib/aws-ec2';
import { CapacityType, TaintEffect } from 'aws-cdk-lib/aws-eks';
import * as eks from 'aws-cdk-lib/aws-eks';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { VPCConfig } from '../common/vpc/config';
import { Eks, EksCluster } from './config';
import * as blueprints from '@aws-quickstart/eks-blueprints';
import { createCalicoDefaultDenyNetworkPolicy } from './utils/calico-default-deny-network-policy';
import { createCFIngressOnlySecurityGroup } from './utils/cloudfront-ingress-securitygroup';
import { createExternalSecretsClusterSecretStore } from './utils/cluster-secret-store';
import { createDrataExcludeTaggingLambda } from './utils/ec2-drata-tagging-lambda';
import { createServiceAccountAndIamRoles } from './utils/service-account-and-iam-roles';
import { generateListOfAddons } from './addons/addons';

interface EksClustersStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
    eks: Eks;
    eksVPC?: VPCConfig;
}

export interface EksClustersStackProps extends StackProps {
    buildConfig: EksClustersStackConfig;
}

export class EksClustersStack extends Stack {
    // I have left this as a reminder to test multi-cluster (blue/green) deployments
    //readonly clusters = new Map<string, eks.Cluster>();
    readonly cfIngressSG: SecurityGroup;
    readonly eksVpc: IVpc;
    readonly eksBlueprint: blueprints.EksBlueprint[];

    constructor(scope: Construct, id: string, props: EksClustersStackProps) {
        super(scope, id, props);

        if (props.buildConfig.awsEnvAccount.coldSite) {
            console.log('Skipping EksClustersStack.');
            return;
        }

        if (!props.buildConfig.eks.eksClusters) {
            console.log('Skipping EksClustersStack');
            return;
        }

        this.eksBlueprint = [];

        const buildConfig = props.buildConfig;
        const account = props.buildConfig.awsEnvAccount.awsAccountID;
        const region = props.buildConfig.awsEnvAccount.targetRegion;
        const env = props.buildConfig.cdkAppInfo.environment;
        const vpcIdSSMKey = buildConfig.eks.useCoreVPC ? '/network-stack/core-vpc-id' : '/eks-vpc/vpc-id';
        const vpcId = ssm.StringParameter.valueFromLookup(this, vpcIdSSMKey);

        // Lookup VPC
        this.eksVpc = Vpc.fromLookup(this, `ImportEKSClustersVPC`, {
            isDefault: false,
            vpcId: vpcId,
        });

        // Create shared ALB security group. Limits traffic to CloudFront only!
        this.cfIngressSG = createCFIngressOnlySecurityGroup(this, this.eksVpc, region);

        // Create a cluster for each cluster config
        for (const eksClusterConfig of buildConfig.eks.eksClusters) {
            if (!eksClusterConfig.enabled) {
                continue;
            }

            // Generate list of cluster addons
            const addOns: Array<blueprints.ClusterAddOn> = generateListOfAddons(
                eksClusterConfig,
                region,
                env,
                buildConfig.awsEnvAccount.awsOrgRootAccountID
            );

            const clusterProvider: blueprints.ClusterProvider = this.genericClusterProvider(eksClusterConfig);

            const eksBlueprint = blueprints.EksBlueprint.builder()
                .clusterProvider(clusterProvider)
                .account(account)
                .name(eksClusterConfig.name)
                .region(region)
                .enableControlPlaneLogTypes(
                    blueprints.ControlPlaneLogType.API,
                    blueprints.ControlPlaneLogType.AUTHENTICATOR
                )
                .resourceProvider(blueprints.GlobalResources.Vpc, new blueprints.VpcProvider(vpcId))
                .resourceProvider('HostedZone', new blueprints.ImportHostedZoneProvider(eksClusterConfig.hostedZoneId))
                .version(eksClusterConfig.version)
                .addOns(...addOns)
                .useDefaultSecretEncryption(true) // set to false to turn secret encryption off (non-production/demo cases)
                .build(scope, eksClusterConfig.name);

            // Ensure the cluster is created first
            const clusterInfo = eksBlueprint.getClusterInfo().cluster.clusterName;
            console.log(`Cluster ${clusterInfo} was created.`);
            this.eksBlueprint.push(eksBlueprint);

            // Allow access from VPN subnet
            eksBlueprint
                .getClusterInfo()
                .cluster.clusterSecurityGroup.addIngressRule(
                    Peer.ipv4('***********/16'),
                    Port.tcp(443),
                    'Allow HTTPs from VPN CIDR'
                );

            for (const cidr of eksClusterConfig.additionalAllowedCIDRs) {
                eksBlueprint
                    .getClusterInfo()
                    .cluster.clusterSecurityGroup.addIngressRule(
                        Peer.ipv4(cidr),
                        Port.tcp(443),
                        'Allow HTTPs from from additional cidr'
                    );
            }

            // Create service accounts
            createServiceAccountAndIamRoles(eksBlueprint.getClusterInfo(), buildConfig.eks.serviceAccounts);

            // Create Calico default deny policy for 'default' namespace
            if (buildConfig.eksVPC && eksClusterConfig.createDefaultDenyPolicy) {
                createCalicoDefaultDenyNetworkPolicy(
                    eksBlueprint.getClusterInfo(),
                    'default',
                    this.eksVpc.vpcCidrBlock,
                    buildConfig.eksVPC.transitGatewayTargetCIDRs,
                    '***********/32'
                );
            }

            if (buildConfig.eksVPC && eksClusterConfig.createExternalSecretsClusterSecretStore) {
                createExternalSecretsClusterSecretStore(
                    eksBlueprint.getClusterInfo(),
                    buildConfig.awsEnvAccount.targetRegion
                );
            }

            new CfnOutput(this, eksClusterConfig.name, { value: eksBlueprint.getClusterInfo().cluster.clusterName });
            new CfnOutput(this, `Cluster${eksClusterConfig.name}`, {
                value: eksBlueprint.getClusterInfo().cluster.clusterArn,
            });
        }

        new CfnOutput(this, 'CloudFrontIngressSecurityGroupID', { value: this.cfIngressSG.securityGroupId });

        // Create Lambda function to run every hour and add DrataExclude tag to ESK nodes
        createDrataExcludeTaggingLambda(this, region);
    }

    private genericClusterProvider(eksClusterConfig: EksCluster): blueprints.GenericClusterProvider {
        const nodegroups: ManagedNodeGroup[] = [];
        for (const nodegroup of eksClusterConfig.managedNodeGroup) {
            const eksTaints = nodegroup.taints?.map((taint) => ({
                effect: TaintEffect[taint.effect as keyof typeof TaintEffect],
                key: taint.key,
                value: taint.value,
            }));

            const eksTaintLabels: { [name: string]: string } | undefined = nodegroup.taints?.reduce(
                (acc, taint) => {
                    acc[taint.key] = taint.value;
                    return acc;
                },
                {} as { [name: string]: string }
            );

            nodegroups.push({
                id: nodegroup.id,
                nodegroupName: nodegroup.id,
                minSize: nodegroup.minSize,
                maxSize: nodegroup.maxSize,
                instanceTypes: [new InstanceType(nodegroup.instanceType)],
                maxUnavailablePercentage: nodegroup.maxUnavailablePercentage,
                nodeGroupCapacityType: nodegroup.spotInstance ? CapacityType.SPOT : CapacityType.ON_DEMAND,
                tags: {
                    DrataExclude: 'Monitored by Grafana',
                },
                amiType: this.getAmiType(nodegroup.amiType),
                labels:
                    nodegroup.labels && Object.keys(nodegroup.labels).length > 0 ? nodegroup.labels : eksTaintLabels,
                diskSize: nodegroup.diskSize,
                taints: eksTaints,
            });
        }

        return new blueprints.GenericClusterProvider({
            managedNodeGroups: nodegroups,
            privateCluster: true,
        });
    }

    private getAmiType(amiString?: string): eks.NodegroupAmiType | undefined {
        if (amiString && amiString in eks.NodegroupAmiType) {
            return eks.NodegroupAmiType[amiString as keyof typeof eks.NodegroupAmiType];
        }
        return undefined;
    }
}
