import { custom, JSONObject, optional, required, array } from 'ts-json-object';
import { KubernetesVersion } from 'aws-cdk-lib/aws-eks';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function cdkEnumValueLookup(obj: any, key: string, value: string, lookupType: any, lookupTypeName: string): any {
    const enumValue = value.toUpperCase();
    if (!Object.keys(lookupType).includes(enumValue)) {
        throw new TypeError(`Invalid value ${value} for ${lookupTypeName}`);
    }
    return lookupType[enumValue as keyof typeof lookupType];
}

export class IamMappedServiceAccount extends J<PERSON>NObject {
    @required
    name: string;

    @optional('default')
    namespace: string;

    @optional(false)
    createNamespace: boolean;

    // Policy definitions are located at ../../assets/eks/service-accounts/policies.json
    // Provide names (JSON keys) of policies to attach to service account
    @optional([])
    customPolicies: string[];

    // AWS managed policies only
    @optional([])
    managedPolicies: string[];
}
export class ManagedNodeGroupTaint extends JSONObject {
    @required
    effect: string;
    @required
    key: string;
    @required
    value: string;
}

export class ManagedNodeGroupSpec extends JSONObject {
    @optional(100)
    diskSize: number;
    @required
    id: string;
    @required
    instanceType: string;
    @optional(null)
    labels?: {
        [name: string]: string;
    };

    @required
    maxSize: number;
    @required
    minSize: number;
    @required
    maxUnavailablePercentage: number;

    @optional(false)
    spotInstance: boolean;

    @optional(null)
    tags?: {
        [name: string]: string;
    };

    @optional([])
    @array(ManagedNodeGroupTaint)
    taints: Array<ManagedNodeGroupTaint>;

    @optional(undefined)
    amiType: string;
}

export class EksCluster extends JSONObject {
    @optional(true)
    enabled: boolean;

    @required
    name: string;

    // For acceptable values lookup CDK docs
    @custom((d: EksCluster, k: string, v: string) => {
        return cdkEnumValueLookup(d, k, v, KubernetesVersion, 'KubernetesVersion');
    })
    @required
    version: KubernetesVersion;

    @required
    kubeProxyAddOnVersion: string;

    @optional('4.20.0')
    falcoAddOnVersion: string;

    @optional(true)
    createDefaultDenyPolicy: boolean;

    @required
    hostedZoneId: string;

    @optional(true)
    enableAlbController: boolean;

    @optional([])
    @array(ManagedNodeGroupSpec)
    managedNodeGroup: Array<ManagedNodeGroupSpec>;

    // InstanceType.InstanceSize e.g "c5a.2xlarge"
    @optional('c5a.2xlarge')
    instanceType: string;

    @optional(true)
    enable_addon_falco: boolean;
    @optional(true)
    enable_addon_grafana: boolean;
    @optional(true)
    enable_addon_refinery: boolean;
    @optional(true)
    enable_addon_priority_class: boolean;
    @optional(true)
    enable_addon_cert_manager_csi: boolean;
    @optional(false)
    enable_addon_gpu_operator: boolean;
    @optional(undefined)
    efs_addon_fs_id: string;
    @optional(true)
    enable_addon_kms_issuer: boolean;
    @optional(true)
    enable_addon_external_rbac: boolean;
    @optional(false)
    enable_addon_prefect_external_rbac: boolean;
    @optional([])
    additionalAllowedCIDRs: string[];
    @optional(true)
    createExternalSecretsClusterSecretStore: boolean;
}

export class Eks extends JSONObject {
    @required
    @array(EksCluster)
    eksClusters: Array<EksCluster>;

    @optional([])
    @array(IamMappedServiceAccount)
    serviceAccounts: Array<IamMappedServiceAccount>;

    @optional(false)
    useCoreVPC: boolean;
}
