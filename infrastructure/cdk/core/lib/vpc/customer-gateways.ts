import { CfnOutput, Stack, StackProps } from 'aws-cdk-lib';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { CfnCustomerGateway, CfnVPNConnection } from 'aws-cdk-lib/aws-ec2';
import { TransitGateway } from '../common/transit-gateway/config';

interface CustomerGatewaysStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
    transitGateway: TransitGateway;
}

export interface CustomerGatewaysProps extends StackProps {
    buildConfig: CustomerGatewaysStackConfig;
}

export class CustomerGatewaysStack extends Stack {
    constructor(scope: Construct, id: string, props: CustomerGatewaysProps) {
        super(scope, id, props);

        if (!props.buildConfig.transitGateway || props.buildConfig.transitGateway.customerGateways.length == 0) {
            console.log('Skipping CustomerGatewaysStack.');
            return;
        }

        // Lookup transit gateway
        const tgwId = ssm.StringParameter.valueFromLookup(this, 'tgw-id');

        // Generate a pre-shared key for VPN tunnels
        const vpnPreSharedKey = new secretsmanager.Secret(this, 'eks-vpc-vpn-pre-shared-secret', {
            secretName: 'eks-vpc-vpn-pre-shared-secret',
            generateSecretString: {
                excludePunctuation: true,
                passwordLength: 20,
            },
        });

        new CfnOutput(this, `secret-arn-for-eks-vpc-vpn-pre-shared-secret`, {
            value: `${vpnPreSharedKey.secretArn}`,
        });

        // Create customer gateways and VPN connections
        for (const customerGateway of props.buildConfig.transitGateway.customerGateways) {
            const cfnCustomerGateway = new CfnCustomerGateway(this, `cgw-${customerGateway.deviceName}`, {
                ipAddress: customerGateway.externalIpAddress,
                type: 'ipsec.1',
                bgpAsn: customerGateway.externalBgpAsn,
                deviceName: customerGateway.deviceName,
            });

            const vpnConnection = new CfnVPNConnection(this, `vpn-connection-${customerGateway.deviceName}`, {
                customerGatewayId: cfnCustomerGateway.attrCustomerGatewayId,
                type: 'ipsec.1',
                staticRoutesOnly: false,
                //vpnGatewayId: vpnGateway.gatewayId,
                transitGatewayId: tgwId,
                vpnTunnelOptionsSpecifications: [
                    {
                        preSharedKey: vpnPreSharedKey.secretValue.toString(),
                    },
                    {
                        preSharedKey: vpnPreSharedKey.secretValue.toString(),
                    },
                ],
            });
            vpnConnection.node.addDependency(cfnCustomerGateway);
        }
    }
}
