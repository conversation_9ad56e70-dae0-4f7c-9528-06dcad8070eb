import { Stack, StackProps, CfnOutput, Tags } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import {
    Vpc,
    SecurityGroup,
    CfnVPCPeeringConnection,
    Peer,
    Port,
    SubnetType,
    ISubnet,
    CfnRoute,
    ISecurityGroup,
    IpAddresses,
    IVpc,
    FlowLogDestination,
    FlowLogTrafficType,
    FlowLogMaxAggregationInterval,
} from 'aws-cdk-lib/aws-ec2';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { VPCConfig } from '../common/vpc/config';
import * as tg from '../common/transit-gateway';

interface EksNetworkStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
    eksVPC: VPCConfig;
}

export interface EksNetworkStackProps extends StackProps {
    buildConfig: EksNetworkStackConfig;
}

export class EksNetworkStack extends Stack {
    readonly vpc: Vpc;
    readonly defaultSecGroup: ISecurityGroup;

    constructor(scope: Construct, id: string, props: EksNetworkStackProps) {
        super(scope, id, props);

        if (props.buildConfig.awsEnvAccount.coldSite) {
            console.log('Skipping EksNetworkStack.');
            return;
        }

        if (!props.buildConfig.eksVPC) {
            console.log('Skipping EksNetworkStack');
            return;
        }

        const buildConfig = props.buildConfig;

        this.vpc = new Vpc(this, 'EKS-VPC', {
            vpcName: 'eks-vpc',
            maxAzs: buildConfig.eksVPC.maxAZs,
            natGateways: buildConfig.eksVPC.numNATGateways,
            ipAddresses: IpAddresses.cidr(buildConfig.eksVPC.networkCIDR),
            subnetConfiguration: [
                {
                    name: 'public',
                    subnetType: SubnetType.PUBLIC,
                },
                {
                    name: 'private',
                    subnetType: SubnetType.PRIVATE_WITH_EGRESS,
                    reserved: buildConfig.eksVPC.numNATGateways <= 0, // Reserves subnet CIDR to avoid changing subnet masks
                },
            ],
        });

        // Flow logs
        if (buildConfig.eksVPC.enableVpcFlowLogs) {
            this.vpc.addFlowLog('FlowLogS3', {
                destination: FlowLogDestination.toS3(),
                trafficType: FlowLogTrafficType.REJECT,
                maxAggregationInterval: FlowLogMaxAggregationInterval.TEN_MINUTES,
            });
        }

        // Look up default security group for core VPC
        this.defaultSecGroup = SecurityGroup.fromSecurityGroupId(
            this,
            'EksDefaultSecurityGroup',
            this.vpc.vpcDefaultSecurityGroup
        );

        const coreVpcId = ssm.StringParameter.valueFromLookup(this, '/network-stack/core-vpc-id');
        const coreVPC = Vpc.fromLookup(this, `ImportCoreVPC`, {
            isDefault: false,
            vpcId: coreVpcId,
        });
        //const coreVpcId = props.networkStack.coreVpc.vpcId;
        const coreVpcCidr = coreVPC.vpcCidrBlock;

        // Peer EKS to Core
        const peeringConnId = this.createVPCPeeringConnection(
            'CORE',
            coreVpcId,
            buildConfig.awsEnvAccount.awsAccountID,
            buildConfig.awsEnvAccount.targetRegion
        );

        // Create routes from EKS to Core
        this.createPeeringRoutesInSource(this.vpc, 'EKS', 'CORE', coreVpcCidr, peeringConnId);

        // Create routes from Core to EKS
        this.createPeeringRoutesInDest(coreVPC, 'CORE', 'EKS', this.vpc.vpcCidrBlock, peeringConnId);

        for (const target of buildConfig.eksVPC.peeringTarget) {
            if (target.allowICMPinDefaultSG) {
                // Allow ICMP from Target vpc in Eks VPC's default security group
                this.defaultSecGroup.addIngressRule(
                    Peer.ipv4(target.networkCIDR),
                    Port.icmpPing(),
                    'Allow ping from anywhere'
                );
            }

            // Setup VPC Peering connection
            const targetAwsAccountID = target.accountID ? target.accountID : buildConfig.awsEnvAccount.awsAccountID;
            const targetAwsRegion = target.region ? target.region : buildConfig.awsEnvAccount.targetRegion;

            let peeringConnID: string = target.id;
            if (!target.targetIsPeeringConn) {
                peeringConnID = this.createVPCPeeringConnection(
                    target.name,
                    target.id,
                    targetAwsAccountID,
                    targetAwsRegion,
                    target.peeringRole
                );
            }

            // Create routes from EKS to Target VPC
            this.createPeeringRoutesInSource(this.vpc, 'EKS', target.name, target.networkCIDR, peeringConnID);

            // Only attempt adding routes to peering connection when target is in the same account && region
            if (
                targetAwsAccountID === buildConfig.awsEnvAccount.awsAccountID &&
                targetAwsRegion === buildConfig.awsEnvAccount.targetRegion
            ) {
                // Configure route tables for existing EKS VPC
                const targetVPCResource = Vpc.fromLookup(this, `Import${target.name}VPC-${target.region}`, {
                    isDefault: false,
                    vpcId: target.id,
                });

                this.createPeeringRoutesInDest(
                    targetVPCResource,
                    target.name,
                    'EKS',
                    this.vpc.vpcCidrBlock,
                    peeringConnID
                );
            }
        }

        // Setup any TGW attachments
        if (buildConfig.eksVPC.transitGatewayId) {
            // Lookup transit gateway
            const tgw = tg.TransitGateway.fromTransitGatewayId(this, 'Tgw', buildConfig.eksVPC.transitGatewayId);

            // attach the sandbox vpc to the transit gateway
            const tgwAttachment = tgw.addVpcAttachment(this.vpc, {
                subnets: this.vpc.selectSubnets().subnets,
            });

            new ssm.StringParameter(this, 'eksVpcTgwAttachmentId', {
                stringValue: tgwAttachment.transitGatewayAttachmentId,
                parameterName: 'eksVpcTgwAttachmentId',
            });

            this.createTransitGatewayRoutes(
                this.vpc,
                buildConfig.eksVPC.transitGatewayTargetCIDRs,
                buildConfig.eksVPC.transitGatewayId
            );
        }

        const tagAllSubnets = (subnets: ISubnet[], tagName: string, tagValue: string) => {
            for (const subnet of subnets) {
                Tags.of(subnet).add(tagName, tagValue);
            }
        };
        // To use the auto-discover subnets, kubernetes.io/role/elb, kubernetes.io/role/internal-elb tags should be set as 1
        tagAllSubnets(this.vpc.publicSubnets, 'kubernetes.io/role/elb', '1');
        tagAllSubnets(this.vpc.privateSubnets, 'kubernetes.io/role/internal-elb', '1');

        const parameter = new ssm.StringParameter(this, 'SSMVPCID', {
            parameterName: '/eks-vpc/vpc-id',
            stringValue: this.vpc.vpcId,
        });
        new CfnOutput(this, 'VPC', { value: this.vpc.vpcId });
        new CfnOutput(this, 'SSMParameter', { value: parameter.parameterName });
        new CfnOutput(this, 'SSMParameterValue', { value: this.vpc.vpcId });
        new CfnOutput(this, 'SSMURL', {
            value: `https://${this.region}.console.aws.amazon.com/systems-manager/parameters/`,
        });
    }

    // Create peering connection to another VPC and add routes
    private createVPCPeeringConnection(
        targetName: string,
        peerVpcId: string,
        accepterAccountID: string,
        peerRegion: string,
        peerRoleArn: string = ''
    ): string {
        const peeringConnection = new CfnVPCPeeringConnection(this, `PeeringEksTo${targetName}`, {
            peerVpcId: peerVpcId, // Accepter VPC ID
            vpcId: this.vpc.vpcId, // Requester VPC ID
            peerOwnerId: accepterAccountID, // Accepter VPC Account ID
            peerRegion: peerRegion, // Accepter Region,
            peerRoleArn: peerRoleArn ? peerRoleArn : undefined, // Accepter Role Arn,
            tags: [
                {
                    key: 'Name',
                    value: `eks-vpc-to-${targetName.toLowerCase()}`,
                },
                {
                    key: 'from',
                    value: 'eks-vpc',
                },
                {
                    key: 'to',
                    value: `${targetName}-vpc`.toLowerCase(),
                },
            ],
        });

        return peeringConnection.ref;
    }

    private createPeeringRoutesInSource(
        sourceVpc: Vpc,
        sourceName: string,
        targetName: string,
        targetCidr: string,
        peeringConnId: string
    ) {
        // The list of subnets returned by CDK is not consistent
        // This results in duplicate entries and CF stack failure hence why using Sets
        const subnetsSet = new Set<ISubnet>();
        sourceVpc.isolatedSubnets.forEach(subnetsSet.add, subnetsSet);
        sourceVpc.privateSubnets.forEach(subnetsSet.add, subnetsSet);
        sourceVpc.publicSubnets.forEach(subnetsSet.add, subnetsSet);

        for (const subnet of subnetsSet) {
            new CfnRoute(this, `VPCPeeringRouteBetween${sourceName}And${targetName}-${subnet.ipv4CidrBlock}`, {
                destinationCidrBlock: targetCidr,
                routeTableId: subnet.routeTable.routeTableId,
                vpcPeeringConnectionId: peeringConnId,
            });
        }
    }

    private createPeeringRoutesInDest(
        sourceVpc: IVpc,
        sourceName: string,
        targetName: string,
        targetCidr: string,
        peeringConnId: string
    ) {
        // Hack to get around https://github.com/aws/aws-cdk/issues/19425
        // First run creates the VPC and populate the context file
        // Second run creates the actual routes
        if (sourceVpc.vpcId.startsWith('vpc-12345')) {
            console.log(`Skipping route create for subnets in ${sourceName}, second run will create them!`);
            return;
        }

        // We had to take this approach because fromLookup gets confused
        // when dealing with public subnets and returns all subnets
        // This results in duplicate entries and CF stack failure hence why using Sets
        const subnetsMap = new Map<string, ISubnet>();

        // Assuming the routeTable property exists and has a routeTableId property
        const addSubnetToMap = (subnet: ISubnet) => {
            const routeTableId = subnet.routeTable.routeTableId;
            subnetsMap.set(routeTableId, subnet);
        };

        sourceVpc.isolatedSubnets.forEach(addSubnetToMap);
        sourceVpc.privateSubnets.forEach(addSubnetToMap);
        sourceVpc.publicSubnets.forEach(addSubnetToMap);

        const subnetsSet = Array.from(subnetsMap.values());

        for (const subnet of subnetsSet) {
            new CfnRoute(this, `VPCPeeringRouteBetween${sourceName}And${targetName}-${subnet.ipv4CidrBlock}`, {
                destinationCidrBlock: targetCidr,
                routeTableId: subnet.routeTable.routeTableId,
                vpcPeeringConnectionId: peeringConnId,
            });
        }
    }

    private createTransitGatewayRoutes(sourceVpc: IVpc, targetCidrs: string[], transitGatewayId: string) {
        for (const cidr of targetCidrs) {
            // We had to take this approach because fromLookup gets confused
            // when dealing with public subnets and returns all subnets
            // This results in duplicate entries and CF stack failure hence why using Sets
            const subnetsMap = new Map<string, ISubnet>();

            // Assuming the routeTable property exists and has a routeTableId property
            const addSubnetToMap = (subnet: ISubnet) => {
                const routeTableId = subnet.routeTable.routeTableId;
                subnetsMap.set(routeTableId, subnet);
            };

            sourceVpc.isolatedSubnets.forEach(addSubnetToMap);
            sourceVpc.privateSubnets.forEach(addSubnetToMap);
            sourceVpc.publicSubnets.forEach(addSubnetToMap);

            const subnetsSet = Array.from(subnetsMap.values());

            for (const subnet of subnetsSet) {
                // This is a temp hack
                // I need a weekend maintenance window to apply these route changes to prod
                // For now we are just removing the GCP dev range until we rename these routes.
                // https://linear.app/unblocked/issue/UNB-1600/fix-transitgateway-route-names-in-dev-and-prod
                const resourceNamePostfix = targetCidrs.length > 1 ? `-to-${cidr.replace(/[./]/g, '')}` : '';

                new CfnRoute(this, `tgw-route-${subnet.ipv4CidrBlock}${resourceNamePostfix}`, {
                    destinationCidrBlock: cidr,
                    routeTableId: subnet.routeTable.routeTableId,
                    transitGatewayId: transitGatewayId,
                });
            }
        }
    }
}
