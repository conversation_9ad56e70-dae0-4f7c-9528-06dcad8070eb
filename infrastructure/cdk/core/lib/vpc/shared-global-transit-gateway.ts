import * as ram from 'aws-cdk-lib/aws-ram';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import * as tg from '../common/transit-gateway';

export interface TransitGatewayStackProps extends StackProps {
    resourceShareName?: string;
    accountsToShareWith?: string[];
}

export class TransitGatewayStack extends Stack {
    public readonly transitGateway: tg.ITransitGateway;
    public readonly defaultRouteTable: tg.ITgwRouteTable;

    constructor(scope: Construct, id: string, props: TransitGatewayStackProps = {}) {
        super(scope, id, props);

        this.transitGateway = new tg.TransitGateway(this, 'TransitGateway');

        this.defaultRouteTable = new tg.TgwRouteTable(this, 'DefaultRouteTable', {
            transitGateway: this.transitGateway,
            name: 'DefaultRouteTable',
        });

        new ram.CfnResourceShare(this, 'Share', {
            name: props.resourceShareName ?? 'TGW-Share',
            allowExternalPrincipals: false,
            resourceArns: [this.transitGateway.transitGatewayArn],
            principals: props.accountsToShareWith,
        });

        // storing the TgwId in a SSM parameter so that we can
        // perform a lookup in other CDK apps
        new ssm.StringParameter(this, 'TgwId', {
            stringValue: this.transitGateway.transitGatewayId,
            parameterName: 'tgw-id',
        });
    }
}
