import { CfnOutput, Stack, StackProps } from 'aws-cdk-lib';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { Construct } from 'constructs';
import { AwsEnvAccount } from '../build-config';
import {
    Vpc,
    GatewayVpcEndpointAwsService,
    SecurityGroup,
    CfnVPCPeeringConnection,
    Peer,
    Port,
    SubnetType,
    ISubnet,
    InterfaceVpcEndpointAwsService,
    CfnRoute,
    ISecurityGroup,
    IpAddresses,
    IVpc,
    FlowLogDestination,
    FlowLogTrafficType,
    FlowLogMaxAggregationInterval,
} from 'aws-cdk-lib/aws-ec2';
import { VPCConfig } from '../common/vpc/config';
import * as tg from '../common/transit-gateway';

interface NetworkStackConfig {
    awsEnvAccount: AwsEnvAccount;
    coreVPC: VPCConfig;
}

export class NetworkStack extends Stack {
    readonly coreVpc: Vpc;
    readonly defaultSecGroup: ISecurityGroup;

    constructor(scope: Construct, id: string, props: StackProps, buildConfig: NetworkStackConfig) {
        super(scope, id, props);

        if (buildConfig.awsEnvAccount.coldSite) {
            console.log('Skipping NetworkStack.');
            return;
        }

        this.coreVpc = new Vpc(this, 'CORE-VPC', {
            vpcName: 'core-vpc',
            maxAzs: buildConfig.coreVPC.maxAZs,
            natGateways: buildConfig.coreVPC.numNATGateways,
            ipAddresses: IpAddresses.cidr(buildConfig.coreVPC.networkCIDR),
            subnetConfiguration: [
                {
                    name: 'public',
                    subnetType: SubnetType.PUBLIC,
                },
                {
                    name: 'private',
                    subnetType: SubnetType.PRIVATE_WITH_EGRESS,
                    reserved: buildConfig.coreVPC.numNATGateways <= 0, // Reserves subnet CIDR to avoid changing subnet masks
                },
                {
                    name: 'isolated',
                    subnetType: SubnetType.PRIVATE_ISOLATED,
                },
            ],
        });

        // Flow logs
        this.coreVpc.addFlowLog('FlowLogS3', {
            destination: FlowLogDestination.toS3(),
            trafficType: FlowLogTrafficType.REJECT,
            maxAggregationInterval: FlowLogMaxAggregationInterval.TEN_MINUTES,
        });

        // Create Stack exports to remove dependencies
        // TODO: Create exports for security group and other elements as needed
        const parameter = new ssm.StringParameter(this, 'SSMVPCID', {
            parameterName: '/network-stack/core-vpc-id',
            stringValue: this.coreVpc.vpcId,
        });
        new CfnOutput(this, 'VPC', { value: this.coreVpc.vpcId });
        new CfnOutput(this, 'SSMParameter', { value: parameter.parameterName });
        new CfnOutput(this, 'SSMParameterValue', { value: this.coreVpc.vpcId });

        // Look up default security group for core VPC
        this.defaultSecGroup = SecurityGroup.fromSecurityGroupId(
            this,
            'DefaultSecurityGroup',
            this.coreVpc.vpcDefaultSecurityGroup
        );

        this.configureDefaultSGMongoAtlasRules(
            this.defaultSecGroup,
            buildConfig.coreVPC.defaultSGMongoAtlasAllowedCIDRs
        );

        for (const target of buildConfig.coreVPC.peeringTarget) {
            if (target.allowICMPinDefaultSG) {
                // Allow ICMP from Target vpc in CoreVPC's default security group
                this.defaultSecGroup.addIngressRule(
                    Peer.ipv4(target.networkCIDR),
                    Port.icmpPing(),
                    'Allow ping from anywhere'
                );
            }

            // Setup VPC Peering connection
            const targetAwsAccountID = target.accountID ? target.accountID : buildConfig.awsEnvAccount.awsAccountID;
            const targetAwsRegion = target.region ? target.region : buildConfig.awsEnvAccount.targetRegion;
            const peeringRole = target.peeringRole ? target.peeringRole : undefined;

            let peeringConnectionID: string = target.id;
            if (!target.targetIsPeeringConn) {
                const peeringConnection = new CfnVPCPeeringConnection(this, `PeeringCoreTo${target.name}`, {
                    peerVpcId: target.id, // Accepter VPC ID
                    vpcId: this.coreVpc.vpcId, // Requester VPC ID
                    peerOwnerId: targetAwsAccountID,
                    peerRegion: targetAwsRegion, // Accepter Region,
                    peerRoleArn: peeringRole, // Accepter Role Arn,
                    tags: [
                        {
                            key: 'Name',
                            value: `core-vpc-to-${target.name.toLowerCase()}`,
                        },
                        {
                            key: 'from',
                            value: 'core-vpc',
                        },
                        {
                            key: 'to',
                            value: `${target.name}-vpc`.toLowerCase(),
                        },
                    ],
                });

                peeringConnectionID = peeringConnection.ref;
            }

            // We had to take this approach because fromLookup gets confused
            // when dealing with public subnets and returns all subnets
            // This results in duplicate entries and CF stack failure hence why using Sets
            const coreSubnetsSet = new Set<ISubnet>();
            this.coreVpc.isolatedSubnets.forEach(coreSubnetsSet.add, coreSubnetsSet);
            this.coreVpc.privateSubnets.forEach(coreSubnetsSet.add, coreSubnetsSet);
            this.coreVpc.publicSubnets.forEach(coreSubnetsSet.add, coreSubnetsSet);

            for (const subnet of coreSubnetsSet) {
                new CfnRoute(this, `VPCPeeringCoreTo${target.name}-${subnet.ipv4CidrBlock}`, {
                    destinationCidrBlock: target.networkCIDR,
                    routeTableId: subnet.routeTable.routeTableId,
                    vpcPeeringConnectionId: peeringConnectionID,
                });
            }

            // Only attempt adding routes to peering connection when target is in the same account && region
            if (
                targetAwsAccountID === buildConfig.awsEnvAccount.awsAccountID &&
                targetAwsRegion === buildConfig.awsEnvAccount.targetRegion
            ) {
                // Configure route tables for existing EKS VPC
                const targetVPCResource = Vpc.fromLookup(this, `Import${target.name}VPC-${target.region}`, {
                    isDefault: false,
                    vpcId: target.id,
                });

                // We had to take this approach because fromLookup gets confused
                // when dealing with public subnets and returns all subnets
                // This results in duplicate entries and CF stack failure hence why using Sets
                const subnetsMap = new Map<string, ISubnet>();

                // Assuming the routeTable property exists and has a routeTableId property
                const addSubnetToMap = (subnet: ISubnet) => {
                    const routeTableId = subnet.routeTable.routeTableId;
                    subnetsMap.set(routeTableId, subnet);
                };

                targetVPCResource.isolatedSubnets.forEach(addSubnetToMap);
                targetVPCResource.privateSubnets.forEach(addSubnetToMap);
                targetVPCResource.publicSubnets.forEach(addSubnetToMap);

                const subnetsSet = Array.from(subnetsMap.values());
                for (const subnet of subnetsSet) {
                    new CfnRoute(this, `VPCPeeringRouteBetween${target.name}AndCore-${subnet.ipv4CidrBlock}`, {
                        destinationCidrBlock: buildConfig.coreVPC.networkCIDR,
                        routeTableId: subnet.routeTable.routeTableId,
                        vpcPeeringConnectionId: peeringConnectionID,
                    });
                }
            }
        }

        // Setup any TGW attachments
        if (buildConfig.coreVPC.transitGatewayId) {
            // Lookup transit gateway
            const tgw = tg.TransitGateway.fromTransitGatewayId(this, 'Tgw', buildConfig.coreVPC.transitGatewayId);

            // attach the sandbox vpc to the transit gateway
            const tgwAttachment = tgw.addVpcAttachment(this.coreVpc, {
                subnets: this.coreVpc.selectSubnets().subnets,
            });

            new ssm.StringParameter(this, 'coreVpcTgwAttachmentId', {
                stringValue: tgwAttachment.transitGatewayAttachmentId,
                parameterName: 'coreVpcTgwAttachmentId',
            });

            this.createTransitGatewayRoutes(
                this.coreVpc,
                buildConfig.coreVPC.transitGatewayTargetCIDRs,
                buildConfig.coreVPC.transitGatewayId
            );
        }

        // Create S3 gateway endpoint in Core VPC
        if (buildConfig.coreVPC.enableS3GatewayEndpoint) {
            this.coreVpc.addGatewayEndpoint('S3GatewayEndpoint', {
                service: GatewayVpcEndpointAwsService.S3,
            });
        }

        // Create Dynamodb gateway endpoint in Core VPC
        if (buildConfig.coreVPC.enableDynamodbEndpoint) {
            this.coreVpc.addGatewayEndpoint('DynamodbGatewayEndpoint', {
                service: GatewayVpcEndpointAwsService.DYNAMODB,
            });
        }

        // Create ECR interface endpoint in Core VPC
        if (buildConfig.coreVPC.enableEcrInterfaceEndpoint) {
            this.coreVpc.addInterfaceEndpoint('EcrDockerInterfaceEndpoint', {
                service: InterfaceVpcEndpointAwsService.ECR_DOCKER,
                privateDnsEnabled: true,
            });
            this.coreVpc.addInterfaceEndpoint('EcrInterfaceEndpoint', {
                service: InterfaceVpcEndpointAwsService.ECR,
                privateDnsEnabled: true,
            });
        }

        // Create EFS interface endpoint in Core VPC
        if (buildConfig.coreVPC.enableEfsInterfaceEndpoint) {
            this.coreVpc.addInterfaceEndpoint('EfsInterfaceEndpoint', {
                service: InterfaceVpcEndpointAwsService.ELASTIC_FILESYSTEM,
                privateDnsEnabled: true,
            });
        }

    }

    private configureDefaultSGMongoAtlasRules(sg: ISecurityGroup, cidrs: string[]): void {
        // This was a hack to allow traffic destined for MongoDB interface. CDK does not support
        // Endpoint Interface lookups. The endpoint inherits the "default" security group so
        // as a workaround we are adding MongoDB access rules to the default SG
        cidrs?.forEach(cidr => {
            sg.addIngressRule(
                Peer.ipv4(cidr),
                Port.tcpRange(0, 65535),
                `Allow all TCP from ${cidr}`
                );
        });
    }

    private createTransitGatewayRoutes(sourceVpc: IVpc, targetCidrs: string[], transitGatewayId: string) {
        for (const cidr of targetCidrs) {
            // We had to take this approach because fromLookup gets confused
            // when dealing with public subnets and returns all subnets
            // This results in duplicate entries and CF stack failure hence why using Sets
            const subnetsMap = new Map<string, ISubnet>();

            // Assuming the routeTable property exists and has a routeTableId property
            const addSubnetToMap = (subnet: ISubnet) => {
                const routeTableId = subnet.routeTable.routeTableId;
                subnetsMap.set(routeTableId, subnet);
            };

            sourceVpc.isolatedSubnets.forEach(addSubnetToMap);
            sourceVpc.privateSubnets.forEach(addSubnetToMap);
            sourceVpc.publicSubnets.forEach(addSubnetToMap);

            const subnetsSet = Array.from(subnetsMap.values());

            for (const subnet of subnetsSet) {
                new CfnRoute(this, `tgw-route-${subnet.ipv4CidrBlock}-${cidr}`, {
                    destinationCidrBlock: cidr,
                    routeTableId: subnet.routeTable.routeTableId,
                    transitGatewayId: transitGatewayId,
                });
            }
        }
    }
}
