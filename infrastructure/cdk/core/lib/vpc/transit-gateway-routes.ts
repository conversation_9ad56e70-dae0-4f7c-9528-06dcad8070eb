import { Stack, StackProps } from 'aws-cdk-lib';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { TransitGateway } from '../common/transit-gateway/config';
import * as tg from '../common/transit-gateway';

interface TransitGatewayRoutesStackConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
    transitGateway: TransitGateway;
}

export interface TransitGatewayRoutesStackProps extends StackProps {
    buildConfig: TransitGatewayRoutesStackConfig;
}

export class TransitGatewayRoutesStack extends Stack {
    readonly tgwAttachments = new Map<string, tg.ITransitGatewayAttachment>();
    readonly routeTables = new Map<string, tg.TgwRouteTable>();
    constructor(scope: Construct, id: string, props: TransitGatewayRoutesStackProps) {
        super(scope, id, props);

        if (
            !props.buildConfig.transitGateway ||
            props.buildConfig.transitGateway.transitGatewayAttachments.length == 0
        ) {
            console.log('Skipping TransitGatewayRoutesStack.');
            return;
        }

        const tgwId = ssm.StringParameter.valueFromLookup(this, 'tgw-id');
        const localAttachmentId = ssm.StringParameter.valueFromLookup(this, 'coreVpcTgwAttachmentId');

        // Lookup transit gateway
        const tgw = tg.TransitGateway.fromTransitGatewayId(this, 'Tgw', tgwId);

        // Lookup all attachments (this will be used for route setup)
        for (const attachment of props.buildConfig.transitGateway.transitGatewayAttachments) {
            const tgwAttachment = tg.TransitGatewayAttachment.fromTransitGatewayAttachmentAttributes(
                tgw,
                `attachment-${attachment.name}`,
                {
                    transitGatewayId: tgw.transitGatewayId,
                    transitGatewayAttachmentId: attachment.attachmentId,
                }
            );
            this.tgwAttachments.set(attachment.name, tgwAttachment);
        }

        // Lookup local attachment
        const localAttachment = tg.TransitGatewayAttachment.fromTransitGatewayAttachmentAttributes(
            tgw,
            'localTgwCoreVpcAttachment',
            {
                transitGatewayId: tgw.transitGatewayId,
                transitGatewayAttachmentId: localAttachmentId,
            }
        );

        // Create the Route Table that you would associate the VPN with
        const secopsVpcRouteTable = new tg.TgwRouteTable(this, 'SecopsCoreVpcTgwAttachment', {
            transitGateway: tgw,
            name: 'SecopsCoreVpcTgwAttachmentt',
        });

        // Attach route table to SecOps core VPC
        secopsVpcRouteTable.addAssociation(localAttachment, 'secopsVpcTgwAttachment');

        // Create route table for each attachment
        for (const attachment of props.buildConfig.transitGateway.transitGatewayAttachments) {
            // create the Route Table that you would associate the VPC with
            const vpcRouteTable = new tg.TgwRouteTable(this, `${attachment.name}`, {
                transitGateway: tgw,
                name: `RouteTable-${attachment.name}`,
            });
            this.routeTables.set(attachment.name, vpcRouteTable);
        }

        // Setup route tables for each attachment and configure it with VPN routes
        for (const attachment of props.buildConfig.transitGateway.transitGatewayAttachments) {
            const remoteAttachment = this.tgwAttachments.get(attachment.name);
            const vpcRouteTable = this.routeTables.get(attachment.name);
            if (!remoteAttachment || !vpcRouteTable) {
                throw 'error vpc route table or attachment not found';
            }

            // Attach route table to remote attachment
            vpcRouteTable.addAssociation(remoteAttachment, attachment.name);

            // Allow communications with SecOps core VPC (VPN)
            vpcRouteTable.addPropagation(localAttachment, attachment.name);

            // Allow communications from SecOps core VPC (VPN) to remote attachment
            secopsVpcRouteTable.addPropagation(remoteAttachment, attachment.name);

            for (const target of attachment.targetAttachmentNames) {
                const tgwAttachment = this.tgwAttachments.get(target);
                const targetRouteTable = this.routeTables.get(target);
                if (tgwAttachment && targetRouteTable) {
                    // Allow communications with SecOps core VPC (VPN)
                    vpcRouteTable.addPropagation(tgwAttachment, `${attachment.name}-${target}`);

                    // Allow communications from SecOps core VPC (VPN) to remote attachment
                    // Enable when we need to contact AWS from GCP
                    //targetRouteTable.addPropagation(remoteAttachment, `${attachment.name}-${target}`);
                }
            }
        }
    }
}
