import { array, JSONObject, optional, required } from 'ts-json-object';

export class WebAclEndpointIPFilterRule extends J<PERSON><PERSON>Object {
    @required
    name: string;
    @required
    // Allowed values: 'CONTAINS', 'CONTAINS_WORD', 'EXACTLY', 'STARTS_WITH', 'ENDS_WITH'
    positionalConstraint: string;
    @required
    searchStrings: Array<string>;
    @required
    ipv4CIDRWhiteList: Array<string>;
}

export class WebAclRateBasedRuleExcludedSearchString extends J<PERSON>NObject {
    @required
    positionalConstraint: string;
    @required
    searchString: string;
    @optional('NONE')
    textTransformationType: string;
}

export class WebAclRateBasedRules extends J<PERSON>NObject {
    @required
    name: string;
    // Defaults to "Count" action
    @optional(false)
    block: boolean;
    @required
    limit: number;
    // Allowed values: 'CONTAINS', 'CONTAINS_WORD', 'EXACTLY', 'STARTS_WITH', 'ENDS_WITH'
    @required
    positionalConstraint: string;
    @required
    searchString: string;
    @optional([])
    @array(WebAclRateBasedRuleExcludedSearchString)
    excludedSearchStrings: Array<WebAclRateBasedRuleExcludedSearchString>;
    @required
    aggregateKeyType: string;
    @optional([])
    aggregateKeyHeaders: string[];
    @required
    headerTextTransformationType: string;
    @optional
    customResponseCode: number;
    @optional(undefined)
    customResponseBodyKey: string;
}

export class WebAclCustomResponse extends JSONObject {
    //1-128 characters. Valid characters: A-Z, a-z, 0-9, - (hyphen), and _ (underscore)
    @required
    name: string;
    // Allowed values are "JSON", "HTML" and "Plain text"
    @required
    contentType: string;
    // Max 4k in size
    @required
    content: string;
}

export class WebAclCloudFront extends JSONObject {
    @required
    name: string;
    @optional([])
    endpointIPFilterRules: Array<WebAclEndpointIPFilterRule>;
    @optional([])
    rateBasedRules: Array<WebAclRateBasedRules>;
    @optional([])
    customResponses: Array<WebAclCustomResponse>;
}
