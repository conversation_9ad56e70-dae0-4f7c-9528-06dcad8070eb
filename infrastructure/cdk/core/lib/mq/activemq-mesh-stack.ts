import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount } from '../build-config';
import { NetworkStack } from '../vpc/network-stack';
import { VPCConfig } from '../common/vpc/config';
import { ActiveMQMesh } from '../common/mq/config';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { ActiveMQMeshConstruct } from '../common/mq/activemq-mesh-construct';

export interface ActiveMQMeshStackProps extends StackProps {
    activeMQMesh: ActiveMQMesh;
    networkStack: NetworkStack;
    coreVPC: VPCConfig;
    awsEnvAccount: AwsEnvAccount;
}

export class ActiveMQMeshStack extends Stack {
    constructor(scope: Construct, id: string, props: ActiveMQMeshStackProps) {
        super(scope, id, props);

        if (props.awsEnvAccount.coldSite) {
            console.log('Skipping ActiveMQStack');
            return;
        }

        this.addDependency(props.networkStack);

        new ActiveMQMeshConstruct(this, props.activeMQMesh.baseBrokerName, {
            activeMQMesh: props.activeMQMesh,
            vpc: props.networkStack.coreVpc,
            networkCIDR: props.coreVPC.networkCIDR,
            vpcSubnets: props.networkStack.coreVpc.selectSubnets({
                subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
            }),
        });
    }
}
