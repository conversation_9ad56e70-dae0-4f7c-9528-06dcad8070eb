import { Stack, StackProps, CfnOutput } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount } from '../build-config';
import * as route53 from 'aws-cdk-lib/aws-route53';
import * as acm from 'aws-cdk-lib/aws-certificatemanager';
import { Dns } from '../common/dns/config';
import { CertRequest } from '../common/acm/config';

interface AcmStackConfig {
    awsEnvAccount: AwsEnvAccount;
    dns: Dns;
    certRequests: Array<CertRequest>;
}

export class AcmStack extends Stack {
    readonly wildcardCert: acm.Certificate;
    readonly envRootHostedZone: route53.IHostedZone;
    readonly certs = new Map<string, acm.Certificate>();

    constructor(scope: Construct, id: string, props: StackProps, buildConfig: AcmStackConfig) {
        super(scope, id, props);

        this.envRootHostedZone = route53.HostedZone.fromHostedZoneAttributes(this, 'EnvRootHostedZone', {
            zoneName: buildConfig.dns.route53HostedZoneName,
            hostedZoneId: buildConfig.dns.route53HostedZoneID,
        });

        for (const req of buildConfig.certRequests) {
            // Look up zone if no email validation is requested
            let targetZone: route53.IHostedZone = this.envRootHostedZone;
            if (req.hostedZoneIDOverride && !req.useEmailValidation) {
                targetZone = route53.HostedZone.fromHostedZoneAttributes(
                    this,
                    `${req.hostedZoneNameOverride}-${req.name}`,
                    {
                        zoneName: req.hostedZoneNameOverride,
                        hostedZoneId: req.hostedZoneIDOverride,
                    }
                );
            }

            // Get and dedupe list of SANs
            const sans = new Set<string>();
            for (const name of req.subjectAlternativeNames) {
                sans.add(name);
            }

            // Added each allowed region as a wildcard san
            if (req.addRegionWildcardAsSan) {
                sans.add(`*.${buildConfig.awsEnvAccount.targetRegion}.${req.domainName.replace('*.', '')}`);
            }

            const cert = new acm.Certificate(this, `cert-${req.name}`, {
                domainName: req.domainName,
                validation: !req.useEmailValidation
                    ? acm.CertificateValidation.fromDns(targetZone)
                    : acm.CertificateValidation.fromEmail(),
                subjectAlternativeNames: Array.from(sans),
            });

            new CfnOutput(this, req.name, { value: cert.certificateArn });

            // This is a hack https://www.endoflineblog.com/cdk-tips-03-how-to-unblock-cross-stack-references
            this.exportValue(cert.certificateArn);

            this.certs.set(req.name, cert);
        }
    }
}
