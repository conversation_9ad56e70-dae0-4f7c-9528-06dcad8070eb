import { Removal<PERSON><PERSON><PERSON>, Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { AwsEnvAccount, CdkAppInfo } from '../build-config';
import { S3BucketConstruct } from '../common/s3-bucket-construct';
import { EnumHelpers } from '../common/utils/enum-helpers';
import { BucketEncryption } from 'aws-cdk-lib/aws-s3';
import { LambdaConstruct } from '../common/lambda/lambda-construct';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as cdk from 'aws-cdk-lib';

import { S3Bucket } from '../common/s3/config';

interface S3BucketsConfig {
    cdkAppInfo: CdkAppInfo;
    awsEnvAccount: AwsEnvAccount;
    s3Buckets: Array<S3Bucket>;
}

export interface S3BucketsStackProps extends StackProps {
    buildConfig: S3BucketsConfig;
}

export class S3BucketsStack extends Stack {
    constructor(scope: Construct, id: string, props: S3BucketsStackProps) {
        super(scope, id, props);

        for (const bucketConfig of props.buildConfig.s3Buckets) {
            const bucket = new S3BucketConstruct(this, bucketConfig.name, {
                bucketEncryption: EnumHelpers.findEnumType(BucketEncryption, bucketConfig.bucketEncryptionType),
                excludeEnvSuffix: bucketConfig.excludeEnvSuffix,
                bucketKeyEnabled: bucketConfig.bucketKeyEnabled,
                bucketTier: bucketConfig.bucketTier,
                cors: undefined,
                publicReadAccess: bucketConfig.publicReadAccess && bucketConfig.name.includes('public'),
                blockPublicAccess: bucketConfig.blockPublicAccess,
                transferAcceleration: undefined,
                name: bucketConfig.name,
                enableVersioning: bucketConfig.enableVersioning,
                awsEnvAccount: props.buildConfig.awsEnvAccount,
                cdkAppInfo: props.buildConfig.cdkAppInfo,
                removalPolicy: bucketConfig.deleteProtection ? RemovalPolicy.RETAIN : undefined,
                autoDeleteObjects: bucketConfig.autoDeleteObjects,
                addAccessLogPolicy: bucketConfig.addAccessLogPolicy,
                replicationTargetRegion: bucketConfig.replicationTargetRegion,
                enableLogRetentionRule: bucketConfig.enableLogRetentionRule,
                enableUserdataRetentionRule: bucketConfig.enableUserdataRetentionRule,
                enableBackupRetentionPolicyRule: bucketConfig.enableBackupRetentionPolicyRule,
                createRootAccountIAMRole: bucketConfig.createRootAccountIAMRole,
            });

            if (bucketConfig.triggerLambda) {
                new LambdaConstruct(this, bucketConfig.triggerLambda.functionName, {
                    name: bucketConfig.triggerLambda.functionName,
                    runtime: lambda.Runtime.PYTHON_3_7,
                    lambdaPath: bucketConfig.triggerLambda.lambdaPath,
                    containerPath: bucketConfig.triggerLambda.containerPath,
                    handler: bucketConfig.triggerLambda.handler,
                    environment: {
                        ...bucketConfig.triggerLambda.environment,
                    },
                    region: props.buildConfig.awsEnvAccount.targetRegion,
                    keyPrefix: bucketConfig.triggerLambda.keyPrefix,
                    keySuffix: bucketConfig.triggerLambda.keySuffix,
                    policies: [
                        new iam.PolicyStatement({
                            resources: ['*'],
                            actions: ['logs:CreateLogGroup', 'logs:CreateLogStream', 'logs:PutLogEvents'],
                        }),
                        new iam.PolicyStatement({
                            resources: [bucket.s3Bucket.arnForObjects('*')],
                            actions: ['s3:Get*', 's3:List*', 's3:Put*'],
                        }),
                        new iam.PolicyStatement({
                            resources: [`arn:aws:kms:*:${props.buildConfig.awsEnvAccount.awsAccountID}:key/*`],
                            actions: ['kms:Encrypt', 'kms:Decrypt', 'kms:ReEncrypt*', 'kms:GenerateDataKey*'],
                        }),
                    ],
                    timeout: cdk.Duration.minutes(bucketConfig.triggerLambda.timeoutInMin),
                    bucket: bucket.s3Bucket,
                });
            }
        }
    }
}
