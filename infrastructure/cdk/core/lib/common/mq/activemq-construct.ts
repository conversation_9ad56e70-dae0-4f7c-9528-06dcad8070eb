import { Construct } from 'constructs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { ActiveMQ } from './config';
import { CfnBroker, CfnConfiguration, CfnConfigurationAssociation } from 'aws-cdk-lib/aws-amazonmq';
import { aws_logs } from 'aws-cdk-lib';
import { Peer, Port, SecurityGroup } from 'aws-cdk-lib/aws-ec2';
import { readFileSync } from 'fs';
import ConfigurationIdProperty = CfnConfigurationAssociation.ConfigurationIdProperty;
import { ActiveMQUserConstruct } from './activemq-user-construct';

export interface ActiveMQConstructProps {
    activeMQ: ActiveMQ;
    networkCIDR: string;
    vpc: ec2.IVpc;
    vpcSubnets: ec2.SubnetSelection;
}

export class ActiveMQConstruct extends Construct {
    readonly openWirePort = 61617;
    readonly consolePort = 8162;
    readonly stompPort = 61614;
    readonly broker: CfnBroker;

    constructor(scope: Construct, id: string, props: ActiveMQConstructProps) {
        super(scope, id);

        const users = this.createUsers(props);
        const securityGroup = this.createSecurityGroup(props);

        // Create subnet group
        const subnetIds = props.vpcSubnets.subnets?.map((subnet) => subnet.subnetId);
        if (!subnetIds) {
            throw new Error('No VPC subnets have been provided');
        }

        // Required to output logs
        // https://docs.aws.amazon.com/amazon-mq/latest/developer-guide/configure-logging-monitoring-activemq.html#security-logging-monitoring-configure-cloudwatch-permissions
        const policyDocument = {
            Version: '2012-10-17',
            Statement: [
                {
                    Effect: 'Allow',
                    Principal: { Service: 'mq.amazonaws.com' },
                    Action: ['logs:CreateLogStream', 'logs:PutLogEvents'],
                    Resource: 'arn:aws:logs:*:*:log-group:/aws/amazonmq/*',
                },
            ],
        };
        new aws_logs.CfnResourcePolicy(this, `${props.activeMQ.brokerName}-AmazonMQ-Log-Policy`, {
            policyName: '${props.activeMQ.brokerName}-AmazonMQ-Log-Policy',
            policyDocument: JSON.stringify(policyDocument),
        });

        const configuration = this.createConfiguration(props);

        switch (props.activeMQ.deploymentMode) {
            case 'SINGLE_INSTANCE':
                console.log('Handling Single Instance Deployment Mode');
                this.broker = this.createActiveMQBroker(
                    props,
                    subnetIds.slice(0, 1),
                    securityGroup,
                    users,
                    configuration
                );
                break;
            case 'ACTIVE_STANDBY_MULTI_AZ':
                console.log('Handling Active-Standby Multi-AZ Deployment Mode');
                this.broker = this.createActiveMQBroker(
                    props,
                    subnetIds.slice(0, 2),
                    securityGroup,
                    users,
                    configuration
                );
                break;
            default:
                throw new Error(`Do not support the provided deployment mode: ${props.activeMQ.deploymentMode}`);
        }
    }

    private createConfiguration(props: ActiveMQConstructProps): CfnConfiguration {
        if (!props.activeMQ.configurationFilePath) {
            throw new Error('No configuration file path has been provided!');
        }
        const activemqConfBase64 = readFileSync(props.activeMQ.configurationFilePath, { encoding: 'base64' });
        return new CfnConfiguration(this, `${props.activeMQ.brokerName}-configuration`, {
            data: activemqConfBase64,
            engineType: 'ACTIVEMQ',
            engineVersion: props.activeMQ.engineVersion,
            name: `${props.activeMQ.brokerName}-configuration`,
            description: 'Custom Unblocked configuration for primary-dev-activemq on ActiveMQ 5.17.3',
        });
    }

    private createActiveMQBroker(
        props: ActiveMQConstructProps,
        subnetIds: string[],
        securityGroup: ec2.ISecurityGroup,
        users: CfnBroker.UserProperty[],
        configuration?: CfnConfiguration
    ): CfnBroker {
        const configurationId: ConfigurationIdProperty | undefined = configuration
            ? {
                  id: configuration.ref,
                  revision: configuration.attrRevision,
              }
            : undefined;

        // Do NOT specify a name as it affects the ability to replace the instance inline
        const brokerName = `${props.activeMQ.brokerName}-activemq-broker`;
        return new CfnBroker(this, brokerName, {
            autoMinorVersionUpgrade: props.activeMQ.autoMinorVersionUpgrade,
            brokerName: brokerName,
            deploymentMode: props.activeMQ.deploymentMode,
            engineType: 'ACTIVEMQ',
            engineVersion: props.activeMQ.engineVersion,
            hostInstanceType: props.activeMQ.hostInstanceType,
            publiclyAccessible: false,
            users: users,
            subnetIds: subnetIds,
            securityGroups: [securityGroup.securityGroupId],
            configuration: configurationId,
            logs: {
                audit: true,
                general: true,
            },
        });
    }

    private createSecurityGroup(props: ActiveMQConstructProps): ec2.ISecurityGroup {
        const securityGroupName = `${props.activeMQ.brokerName}-sg`;
        const sg = new SecurityGroup(this, securityGroupName, {
            vpc: props.vpc,
            securityGroupName: securityGroupName,
            description: 'security group for activeMQ broker',
        });

        sg.addIngressRule(
            Peer.ipv4(props.networkCIDR),
            Port.tcp(this.stompPort),
            'allow inbound stomp traffic from CoreVPC CIDR to activeMQ'
        );

        for (const cidr of props.activeMQ.additionalAllowedCIDRs) {
            sg.addIngressRule(
                Peer.ipv4(cidr),
                Port.tcp(this.openWirePort),
                `allow inbound broker traffic from ${cidr}`
            );
            sg.addIngressRule(
                Peer.ipv4(cidr),
                Port.tcp(this.stompPort),
                `allow inbound stomp broker traffic from ${cidr}`
            );
            sg.addIngressRule(
                Peer.ipv4(cidr),
                Port.tcp(this.consolePort),
                `allow inbound console traffic from ${cidr}`
            );
        }

        return sg;
    }

    private createUsers(props: ActiveMQConstructProps): CfnBroker.UserProperty[] {
        const users: CfnBroker.UserProperty[] = [];
        for (const user of props.activeMQ.users) {
            const activeMQUser = new ActiveMQUserConstruct(this, `${user.userName}`, {
                user: user,
            });
            users.push(activeMQUser.user);
        }
        return users;
    }
}
