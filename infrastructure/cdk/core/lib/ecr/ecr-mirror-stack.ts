import * as cdk from 'aws-cdk-lib';
import { Stack, StackProps } from 'aws-cdk-lib';
import { Construct } from 'constructs';
import { BuildConfig } from '../build-config';
import { CfnRegistryPolicy } from 'aws-cdk-lib/aws-ecr';
import * as iam from 'aws-cdk-lib/aws-iam';
import * as events from 'aws-cdk-lib/aws-events';
import * as targets from 'aws-cdk-lib/aws-events-targets';
import { LambdaConstruct } from '../common/lambda/lambda-construct';
import { IamHelpers } from '../common/utils/iam-helpers';
import { LambdaHelpers } from '../common/utils/lambda-helpers';
import { LambdaConfig } from '../common/lambda/config';

export interface EcrMirrorStackProps extends StackProps {
    buildConfig: BuildConfig;
}

export class EcrMirrorStack extends Stack {
    constructor(scope: Construct, id: string, props: EcrMirrorStackProps) {
        super(scope, id, props);

        const ecrConfig = props.buildConfig.ecr;
        if (ecrConfig.ecrReplicationTargets.length > 0) {
            throw new TypeError(`Cannot specify ecrReplicationTargets for this stack`);
        }

        new CfnRegistryPolicy(this, 'AllowECRCrossAccountReplicationPolicy', {
            policyText: JSON.parse(`
            {
                "Version":"2012-10-17",
                "Statement":[
                    {
                        "Sid":"ReplicationAccessCrossAccount",
                        "Effect":"Allow",
                        "Principal":{
                            "AWS":"arn:aws:iam::${ecrConfig.sourceAccount}:root"
                        },
                        "Action":[
                            "ecr:CreateRepository",
                            "ecr:ReplicateImage"
                        ],
                        "Resource": [
                            "arn:aws:ecr:*:${props.buildConfig.awsEnvAccount.awsAccountID}:repository/*"
                        ]
                    }
                ]
            }`),
        });

        if (props.buildConfig.ecr.cleanupLambda) {
            this.createCleanupLambda(props, props.buildConfig.ecr.cleanupLambda);
        }
    }

    private createCleanupLambda(props: EcrMirrorStackProps, lambdaConfig: LambdaConfig) {
        const roleName = IamHelpers.getRoleName(
            props.buildConfig.awsEnvAccount.targetRegion,
            lambdaConfig.functionName
        );

        // ECR role allowing cleanup Lambda function to list and delete images in tagged repos
        const ecrCleanupLambdaRole = new iam.Role(this, roleName, {
            assumedBy: new iam.ServicePrincipal('lambda.amazonaws.com'),
            description: 'Role assumed by lambda job for cleaning replicated ECR repos',
            inlinePolicies: {
                DescribeACMCerts: new iam.PolicyDocument({
                    statements: [
                        new iam.PolicyStatement({
                            resources: ['*'],
                            actions: [
                                'ecr:BatchDeleteImage',
                                'ecr:DescribeRepositories',
                                'ecr:ListImages',
                                'ecr:DescribeImages',
                            ],
                        }),
                    ],
                }),
            },
        });

        const ecrCleanupLambda = new LambdaConstruct(this, lambdaConfig.functionName, {
            name: lambdaConfig.functionName,
            runtime: LambdaHelpers.findLambdaRuntime(lambdaConfig.runtime),
            lambdaPath: lambdaConfig.lambdaPath,
            handler: lambdaConfig.handler,
            region: props.buildConfig.awsEnvAccount.targetRegion,
            timeout: cdk.Duration.minutes(lambdaConfig.timeoutInMin),
            lambdaRole: ecrCleanupLambdaRole,
            environment: lambdaConfig.environment,
        });

        // Run once a day
        const eventRule = new events.Rule(this, 'scheduleRule', {
            schedule: events.Schedule.cron({ minute: '1' }),
        });
        eventRule.addTarget(new targets.LambdaFunction(ecrCleanupLambda.lambdaFunction));
    }
}
