#!/usr/bin/env node
import 'source-map-support/register';
import * as cdk from 'aws-cdk-lib';
import * as fs from 'fs';
import * as path from 'path';

import {
    BuildConfig,
    StandardBuildConfig,
    SecOpsBuildConfig,
    MgtBuildConfig,
    CdkAppInfo,
    AwsEnvAccount,
} from '../lib/build-config';
import { AcmStack } from '../lib/acm/acm-stack';
import { BatchesStack } from '../lib/batch/batches-stack';
import { CloudwatchEc2AlarmsStack } from '../lib/cloudwatch/cloudwatch-ec2-alarms';
import { CustomerHTTPProxyStack } from '../lib/ec2/customer-http-proxy-stack';
import { HttpProxyStack } from '../lib/ec2/http-proxy-stack';
import { EksClustersStack } from '../lib/eks/eks-clusters-stack';
import { BastionHostStack } from '../lib/ec2/bastion-host-stack';
import { CloudFrontStack } from '../lib/cloudfront/cloudfront-stack';
import { CloudTrailAlarmsStack } from '../lib/cloudwatch/cloudtrail-alarms-stack';
import { CloudwatchGlobalLogRetentionStack } from '../lib/cloudwatch/cloudwatch-global-logretention-policy-stack';
import { CodeIngestionDataPipelineStack } from '../lib/data-pipelines/code-ingestion-data-pipeline-stack';
import { AlbMetricsAggregatorLambdaStack } from '../lib/lambda/alb_metrics_aggregator';
import { GuardDutyAlarmLambdaStack } from '../lib/lambda/guardduty-alarms';
import { MlRuntimeLambdaStack } from '../lib/lambda/ml_runtimes';
import { AuroraServerlessStack } from '../lib/rds/aurora-serverless-stack';
import { DatabaseBackupCleanupStack } from '../lib/rds/database-backup-cleanup-stack';
import { CustomerAssetsStack } from '../lib/s3/customer-assets-stack';
import { DatabaseStack } from '../lib/rds/database-stack';
import { DnsStack } from '../lib/dns/dns-stack';
import { DynamodbStack } from '../lib/dynamodb/dynamodb-stack';
import { EcrMirrorStack } from '../lib/ecr/ecr-mirror-stack';
import { EcrRegistryStack } from '../lib/ecr/ecr-registry-stack';
import { IamStack } from '../lib/iam/iam-stack';
import { CustomerGatewaysStack } from '../lib/vpc/customer-gateways';
import { EksNetworkStack } from '../lib/vpc/eks-network-stack';
import { NetworkStack } from '../lib/vpc/network-stack';
import { OpenvpnStack } from '../lib/ec2/openvpn-stack';
import { PullRequestDataPipelineStack } from '../lib/data-pipelines/pull-request-data-pipeline-stack';
import { RedisStack } from '../lib/elasticache/elasticache-redis-stack';
import { S3BucketsStack } from '../lib/s3/s3-buckets-stack';
import { SecretsStack } from '../lib/asm/secrets-stack';
import { SendGridCloudFrontStack } from '../lib/cloudfront/sendgrid-cloudfront-stack';
import { StaticSiteStack } from '../lib/static-sites/static-site-stack';
import { SubdomainRedirectsCloudfrontStack } from '../lib/cloudfront/subdomain-redirects-cloudfront-stack';
import { TopicIngestionDataPipelineStack } from '../lib/data-pipelines/topic-ingestion-data-pipeline-stack';
import { TransitGatewayStack } from '../lib/vpc/shared-global-transit-gateway';
import { TransitGatewayRoutesStack } from '../lib/vpc/transit-gateway-routes';
import { WebAclCloudFrontStack } from '../lib/wafv2/web-acl-cloudfront-stack';
import { loadEnvironmentFromFile } from '../lib/common/env/environment';
import { TestBatchSubmitPipelineStack } from '../lib/data-pipelines/test-batch-submit-pipeline';
import { ActiveMQMeshStack } from '../lib/mq/activemq-mesh-stack';
import { EFSStack } from '../lib/efs/efs-stack';
import { OpenSearchStack } from '../lib/opensearch/opensearch-stack';

const app = new cdk.App();

/**
 * Deploy stacks to root (Management) account
 * @param buildConfig
 */
function deployMgtEnv(buildConfig: MgtBuildConfig) {
    new CloudwatchGlobalLogRetentionStack(app, 'CloudwatchGlobalLogRetentionStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    new IamStack(app, 'IamStack', {
        terminationProtection: false,
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    new DnsStack(app, 'DnsStack', {
        terminationProtection: false,
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    new CloudTrailAlarmsStack(app, 'CloudTrailAlarmsStack', {
        terminationProtection: false,
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });
}

/**
 * Deploy stacks to AWS Standard region
 * @param buildConfig
 */
function deployStandardRegionStacksForEnv(buildConfig: StandardBuildConfig) {
    new DnsStack(app, 'DnsStack', {
        terminationProtection: false,
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    new CloudwatchGlobalLogRetentionStack(app, 'CloudwatchGlobalLogRetentionStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    const acmStack = new AcmStack(
        app,
        'AcmStack',
        {
            terminationProtection: false,
            env: {
                region: buildConfig.awsEnvAccount.targetRegion,
                account: buildConfig.awsEnvAccount.awsAccountID,
            },
        },
        buildConfig
    );

    const webAclCloudFrontStack = new WebAclCloudFrontStack(
        app,
        'WebAclCloudFrontStack',
        {
            terminationProtection: false,
            env: {
                region: buildConfig.awsEnvAccount.targetRegion,
                account: buildConfig.awsEnvAccount.awsAccountID,
            },
        },
        buildConfig
    );

    const staticSiteStack = new StaticSiteStack(app, 'StaticSiteStack', {
        terminationProtection: true,
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    const cloudFrontStack = new CloudFrontStack(app, 'CloudFrontStack', {
        terminationProtection: true,
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
        acmStack: acmStack,
        webAclCloudFrontStack: webAclCloudFrontStack,
    });
    cloudFrontStack.addDependency(acmStack);

    const sendGridCloudFrontStack = new SendGridCloudFrontStack(app, 'SendGridCloudFrontStack', {
        terminationProtection: true,
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
        acmStack: acmStack,
    });
    cloudFrontStack.addDependency(sendGridCloudFrontStack);

    const subdomainRedirectsCloudfrontStack = new SubdomainRedirectsCloudfrontStack(
        app,
        'SubdomainRedirectsCloudfrontStack',
        {
            terminationProtection: true,
            env: {
                region: buildConfig.awsEnvAccount.targetRegion,
                account: buildConfig.awsEnvAccount.awsAccountID,
            },
            buildConfig: buildConfig,
            acmStack: acmStack,
        }
    );
    cloudFrontStack.addDependency(subdomainRedirectsCloudfrontStack);
}

/**
 * Deploy stacks for a standard worker environment
 * @param buildConfig
 */
function deployCoreAppStacksforEnv(buildConfig: BuildConfig) {
    new CloudwatchGlobalLogRetentionStack(app, 'CloudwatchGlobalLogRetentionStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    new CloudwatchEc2AlarmsStack(app, 'CloudwatchEc2AlarmsStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    const networkStack = new NetworkStack(
        app,
        'NetworkStack',
        {
            env: {
                region: buildConfig.awsEnvAccount.targetRegion,
                account: buildConfig.awsEnvAccount.awsAccountID,
            },
        },
        buildConfig
    );

    const eksNetworkStack = new EksNetworkStack(app, 'EksNetworkStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    eksNetworkStack.addDependency(networkStack);

    const eksClustersStack = new EksClustersStack(app, 'EksClustersStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    eksClustersStack.addDependency(eksNetworkStack);

    new EFSStack(app, 'EFSStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
        networkStack: networkStack,
    });

    new BatchesStack(app, 'BatchesStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        networkStack,
        buildConfig,
        cdkAppInfo: buildConfig.cdkAppInfo,
    });

    new AuroraServerlessStack(app, 'AuroraServerlessStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
        networkStack: networkStack,
    });

    // Depends on networkStack
    new DatabaseStack(
        app,
        'DatabaseStack',
        {
            terminationProtection: true,
            env: {
                region: buildConfig.awsEnvAccount.targetRegion,
                account: buildConfig.awsEnvAccount.awsAccountID,
            },
        },
        buildConfig,
        networkStack
    );

    // Depends on networkStack
    new DatabaseBackupCleanupStack(
        app,
        'DatabaseBackupCleanupStack',
        {
            terminationProtection: true,
            env: {
                region: buildConfig.awsEnvAccount.targetRegion,
                account: buildConfig.awsEnvAccount.awsAccountID,
            },
        },
        buildConfig
    );

    // Dynamodb
    new DynamodbStack(app, 'DynamodbStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    // Depends on networkStack
    new RedisStack(app, 'RedisStack', {
        terminationProtection: false,
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
        networkStack: networkStack,
    });

    new PullRequestDataPipelineStack(app, 'PullRequestDataPipelineStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
        networkStack: networkStack,
    });

    new CodeIngestionDataPipelineStack(app, 'CodeIngestionDataPipelineStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
        networkStack: networkStack,
    });

    new TopicIngestionDataPipelineStack(app, 'TopicIngestionDataPipelineStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
        networkStack: networkStack,
    });

    new EcrMirrorStack(app, 'EcrMirrorStack', {
        terminationProtection: false,
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    new IamStack(app, 'IamStack', {
        terminationProtection: false,
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    const acmStack = new AcmStack(
        app,
        'AcmStack',
        {
            terminationProtection: false,
            env: {
                region: buildConfig.awsEnvAccount.targetRegion,
                account: buildConfig.awsEnvAccount.awsAccountID,
            },
        },
        buildConfig
    );

    const dnsStack = new DnsStack(app, 'DnsStack', {
        terminationProtection: false,
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    new CustomerAssetsStack(app, 'CustomerAssetsStack', {
        terminationProtection: true,
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    new S3BucketsStack(app, 'S3BucketsStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    new SecretsStack(app, 'SecretsStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    const httpProxyStack = new HttpProxyStack(app, 'HttpProxyStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });
    httpProxyStack.addDependency(networkStack);

    new TestBatchSubmitPipelineStack(app, 'TestBatchSubmitPipelineStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
        networkStack: networkStack,
    });

    new ActiveMQMeshStack(app, 'ActiveMQMeshStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        awsEnvAccount: buildConfig.awsEnvAccount,
        coreVPC: buildConfig.coreVPC,
        networkStack: networkStack,
        activeMQMesh: buildConfig.activeMQMesh,
    });

    new MlRuntimeLambdaStack(app, 'MlRuntimeLambdaStack', {
        env: {
            region: buildConfig.awsEnvAccount.targetRegion,
            account: buildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: buildConfig,
    });

    if (buildConfig.openSearch) {
        new OpenSearchStack(app, 'OpenSearchStack', {
            env: {
                region: buildConfig.awsEnvAccount.targetRegion,
                account: buildConfig.awsEnvAccount.awsAccountID,
            },
            awsEnvAccount: buildConfig.awsEnvAccount,
            networkStack: networkStack,
            openSearch: buildConfig.openSearch,
            peerVPCs: [buildConfig.coreVPC, buildConfig.eksVPC],
        });
    }

    if (buildConfig.customerHTTPProxy.length) {
        new CustomerHTTPProxyStack(app, 'CustomerHTTPProxyStack', {
            env: {
                region: buildConfig.awsEnvAccount.targetRegion,
                account: buildConfig.awsEnvAccount.awsAccountID,
            },
            buildConfig: buildConfig,
        });
    }
}

/**
 * Deploy stack(s) for sec-ops shared resources environment
 * @param secOpsBuildConfig
 */
function deploySecOpsEnv(secOpsBuildConfig: SecOpsBuildConfig) {
    new IamStack(app, 'IamStack', {
        terminationProtection: false,
        env: {
            region: secOpsBuildConfig.awsEnvAccount.targetRegion,
            account: secOpsBuildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: secOpsBuildConfig,
    });

    new EcrRegistryStack(app, 'ReplicatedEcrRegistryStack', {
        terminationProtection: true,
        env: {
            region: secOpsBuildConfig.awsEnvAccount.targetRegion,
            account: secOpsBuildConfig.awsEnvAccount.awsAccountID,
        },
        secOpsbuildConfig: secOpsBuildConfig,
    });

    const transitGatewayStack = new TransitGatewayStack(app, 'TransitGatewayStack', {
        env: {
            account: process.env.SHARED_SERVICES_ACCOUNT,
            region: secOpsBuildConfig.awsEnvAccount.targetRegion,
        },
        accountsToShareWith: [secOpsBuildConfig.awsEnvAccount.awsOrgArn],
    });

    const networkStack = new NetworkStack(
        app,
        'NetworkStack',
        {
            env: {
                region: secOpsBuildConfig.awsEnvAccount.targetRegion,
                account: secOpsBuildConfig.awsEnvAccount.awsAccountID,
            },
        },
        secOpsBuildConfig
    );
    networkStack.addDependency(transitGatewayStack);

    const tgwRoutes = new TransitGatewayRoutesStack(app, 'TransitGatewayRoutesStack', {
        env: {
            region: secOpsBuildConfig.awsEnvAccount.targetRegion,
            account: secOpsBuildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: secOpsBuildConfig,
    });
    tgwRoutes.addDependency(networkStack);

    const customerGatewaysStack = new CustomerGatewaysStack(app, 'CustomerGatewaysStack', {
        env: {
            region: secOpsBuildConfig.awsEnvAccount.targetRegion,
            account: secOpsBuildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: secOpsBuildConfig,
    });
    customerGatewaysStack.addDependency(transitGatewayStack);

    const acmStack = new AcmStack(
        app,
        'AcmStack',
        {
            env: {
                region: secOpsBuildConfig.awsEnvAccount.targetRegion,
                account: secOpsBuildConfig.awsEnvAccount.awsAccountID,
            },
        },
        secOpsBuildConfig
    );

    new EFSStack(app, 'EFSStack', {
        env: {
            region: secOpsBuildConfig.awsEnvAccount.targetRegion,
            account: secOpsBuildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: secOpsBuildConfig,
        networkStack: networkStack,
    });

    const eksClustersStack = new EksClustersStack(app, 'EksClustersStack', {
        env: {
            region: secOpsBuildConfig.awsEnvAccount.targetRegion,
            account: secOpsBuildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: secOpsBuildConfig,
    });

    eksClustersStack.addDependency(networkStack);

    new OpenvpnStack(
        app,
        'OpenvpnStack',
        {
            env: {
                region: secOpsBuildConfig.awsEnvAccount.targetRegion,
                account: secOpsBuildConfig.awsEnvAccount.awsAccountID,
            },
        },
        secOpsBuildConfig,
        networkStack
    );

    new BastionHostStack(app, 'BastionHostStack', {
        env: {
            region: secOpsBuildConfig.awsEnvAccount.targetRegion,
            account: secOpsBuildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: secOpsBuildConfig,
        networkStack: networkStack,
    });

    new S3BucketsStack(app, 'S3BucketsStack', {
        env: {
            region: secOpsBuildConfig.awsEnvAccount.targetRegion,
            account: secOpsBuildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: secOpsBuildConfig,
    });

    new GuardDutyAlarmLambdaStack(app, 'GuardDutyAlarmLambdaStack', {
        env: {
            region: secOpsBuildConfig.awsEnvAccount.targetRegion,
            account: secOpsBuildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: secOpsBuildConfig,
    });

    new AlbMetricsAggregatorLambdaStack(app, 'AlbMetricsAggregatorLambdaStack', {
        env: {
            region: secOpsBuildConfig.awsEnvAccount.targetRegion,
            account: secOpsBuildConfig.awsEnvAccount.awsAccountID,
        },
        buildConfig: secOpsBuildConfig,
    });
}

/**
 * Controls which stacks to deployed based on target env type
 */
function Main() {
    const env: string = app.node.tryGetContext('config');
    if (!env) {
        throw new Error('Context variable missing on CDK command. Pass in as `-c config=XXX`');
    } else if (env.startsWith('sec-ops')) {
        const secOpsBuildConfig: SecOpsBuildConfig = new SecOpsBuildConfig(
            JSON.parse(fs.readFileSync(path.resolve('./config/' + env + '.json'), 'utf8'))
        );
        loadEnvironmentFromFile(secOpsBuildConfig.cdkAppInfo.environment);
        deploySecOpsEnv(secOpsBuildConfig);
    } else if (env.startsWith('mgt-us-')) {
        const mgtBuildConfig: MgtBuildConfig = new MgtBuildConfig(
            JSON.parse(fs.readFileSync(path.resolve('./config/' + env + '.json'), 'utf8'))
        );
        deployMgtEnv(mgtBuildConfig);
    } else if (env.endsWith('standard-us-east-1')) {
        const standardBuildConfig: StandardBuildConfig = new StandardBuildConfig(
            JSON.parse(fs.readFileSync(path.resolve('./config/' + env + '.json'), 'utf8'))
        );
        deployStandardRegionStacksForEnv(standardBuildConfig);
    } else {
        const buildConfig: BuildConfig = new BuildConfig(
            JSON.parse(fs.readFileSync(path.resolve('./config/' + env + '.json'), 'utf8'))
        );
        loadEnvironmentFromFile(buildConfig.cdkAppInfo.environment);
        deployCoreAppStacksforEnv(buildConfig);
    }
}

Main();
