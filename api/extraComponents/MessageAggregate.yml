type: object
properties:
  id:
    $ref: "../components/ApiResourceId.yml"
  authorTeamMemberId:
    $ref: "../components/ApiResourceId.yml"
  messageContent:
    $ref: "../components/MessageContent.yml"
  reactions:
    type: array
    items:
      $ref: "../components/MessageReaction.yml"
  mentions:
    type: array
    items:
      $ref: "../components/ApiResourceId.yml"
  isDeleted:
    type: boolean
  canDelete:
    type: boolean
  canEdit:
    type: boolean
  createdAt:
    type: string
    format: date-time
  editedAt:
    type: string
    format: date-time
  feedback:
    $ref: "../components/MessageFeedback.yml"
  feedbackTeamMembers:
    type: object
    properties:
      items:
        type: array
        items:
          $ref: "./MessageFeedbackAggregate.yml"
    required:
      - items
  suggestions:
    $ref: "../components/MessageSuggestions.yml"
  links:
    $ref: "../components/MessageLinks.yml"
  isAuthorCurrentPerson:
    type: boolean
  author:
    $ref: "../components/TeamMember.yml"
  references:
    type: array
    items:
      $ref: "../components/MessageReference.yml"
  referenceAggregates:
    type: array
    items:
      $ref: "./MessageReferenceAggregate.yml"
  dataSourcePreset:
    $ref: "../components/MessageDataSourcePreset.yml"
  archivedReferences:
    type: array
    items:
      $ref: "../components/ArchivedReference.yml"
  state:
    $ref: "../components/MessageState.yml"
  isStreaming:
    type: boolean
  attachments:
    type: array
    description: |
      A list of files attached to this message.
    items:
      $ref: "../components/MessageAttachment.yml"
required:
  - id
  - authorTeamMemberId
  - messageContent
  - createdAt
  - links
  - author
  - isAuthorCurrentPerson
  - canDelete
