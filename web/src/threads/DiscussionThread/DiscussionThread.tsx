import classNames from 'classnames';
import { useEffect, useMemo, useState } from 'react';
import { useLocation, useNavigate, useParams, useSearchParams } from 'react-router-dom';

import { Provider, TeamMember } from '@shared/api/generatedExtraApi';

import { Events } from '@shared/metrics';
import { useApiDataStream } from '@shared/stores/ApiDataStream';
import { useStream } from '@shared/stores/DataCacheStream';
import { DsacSettingsStoreTraits } from '@shared/stores/DsacSettingsTypes';
import { PersonStoreTraits } from '@shared/stores/PersonStoreTypes';
import { ThreadInfoAggregate } from '@shared/stores/ThreadInfoAggregate';
import { ThreadStoreTraits } from '@shared/stores/ThreadStoreTypes';
import { useStore } from '@shared/stores/useStore';
import { Banner } from '@shared/webComponents/Banner/Banner';
import { Button } from '@shared/webComponents/Button/Button';
import { ExpertButton } from '@shared/webComponents/Button/ExpertButton';
import { LoopInExpertsList } from '@shared/webComponents/Experts/LoopInExpertsList';
import { Icon } from '@shared/webComponents/Icon/Icon';
import { MarkReference } from '@shared/webComponents/MarkReference';
import { MessageInput } from '@shared/webComponents/MessageInput/MessageInput';
import { useModalContext } from '@shared/webComponents/Modal/ModalContext';
import { ScrollToBottomButton } from '@shared/webComponents/ScrollToBottomButton/ScrollToBottomButton';
import { SaveInput } from '@shared/webComponents/TextInput/SaveInput';
import { ArchivedText } from '@shared/webComponents/ThreadArchivedText/ThreadArchivedText';
import { ThreadSensitiveDataBanner } from '@shared/webComponents/ThreadSensitiveDataBanner/ThreadSensitiveDataBanner';
import { ThreadView } from '@shared/webComponents/ThreadView/ThreadView';
import { ThreadViewContextMenu } from '@shared/webComponents/ThreadView/ThreadViewContextMenu';
import { ToggleThreadPrivacyDropdown } from '@shared/webComponents/ToggleThreadPrivacyDropdown/ToggleThreadPrivacyDropdown';
import { DashboardUrls } from '@shared/webUtils';
import { findPersonTeamMember } from '@shared/webUtils/TeamMemberUtils';
import { canEditThreadTitle } from '@shared/webUtils/ThreadUtils';
import { useAppLayoutContext } from '@web/components/Layout/AppLayoutContext';
import { LoadingSummaryViewList } from '@web/components/Loading/LoadingSummaryView';
import { useTeamContext } from '@web/components/Team/TeamContext';
import { ThreadIcon } from '@web/components/Thread/ThreadIcon';
import { useDocumentTitle } from '@web/hooks/useDocumentTitle';
import { useInviteToastColumn } from '@web/teamMembers/InviteToastColumn';
import { useHistoryContext } from '@web/utils/HistoryContext';

import { faPipe } from '@fortawesome/pro-light-svg-icons/faPipe';

import { DeleteThreadDialog } from '../DeleteThreadDialog';
import { DiscussionThreadHubFallback } from '../DiscussionThreadHubFallback/DiscussionThreadHubFallback';

import './DiscussionThread.scss';

const DiscussionThreadLoader = ({
    teamId,
    threadId,
    initialThreadInfo,
}: {
    teamId: string;
    threadId: string;
    initialThreadInfo?: ThreadInfoAggregate;
}) => {
    const store = useStore(ThreadStoreTraits, { teamId, threadId });
    const streamState = useApiDataStream(() => store.stream, [store]);

    const body = useMemo(() => {
        if (streamState.$case === 'loading') {
            if (initialThreadInfo) {
                return <DiscussionThreadBody teamId={teamId} threadInfo={initialThreadInfo} />;
            }
            return <LoadingSummaryViewList />;
        }

        return <DiscussionThreadBody teamId={teamId} threadInfo={streamState.value} />;
    }, [teamId, streamState, initialThreadInfo]);

    return <div className="discussion_thread">{body}</div>;
};

const DiscussionThreadBody = ({ teamId, threadInfo }: { teamId: string; threadInfo: ThreadInfoAggregate }) => {
    const { currentTeamId } = useTeamContext();
    const personStore = useStore(PersonStoreTraits, {});
    const person = useStream(() => personStore.person, [personStore]);
    const [searchParams, setSearchParams] = useSearchParams();
    const navigate = useNavigate();
    const { navigateBackwards } = useHistoryContext();
    const dsacSettingsStore = useStore(DsacSettingsStoreTraits, { teamId: currentTeamId });
    const dsacSettings = useStream(() => dsacSettingsStore.stream, [dsacSettingsStore], { $case: 'loading' });

    const [initialPrivacyState] = useState<boolean | undefined>(threadInfo.thread.isPrivate);
    const [showSensitiveContentBanner, setShowSensitiveContentBanner] = useState<boolean>(false);

    useEffect(() => {
        // Banner is only shown when thread starts off public but is transitioned to private due to sensitive source data
        if (
            !initialPrivacyState &&
            threadInfo.thread.isPrivate &&
            threadInfo.sensitiveDataSources &&
            threadInfo.sensitiveDataSources.length > 0
        ) {
            setShowSensitiveContentBanner(true);
        }
    }, [initialPrivacyState, threadInfo]);

    useDocumentTitle(() => {
        return threadInfo.thread.title;
    }, [threadInfo.thread.title]);

    useInviteToastColumn(currentTeamId);

    const currentTeamMember = useMemo(() => findPersonTeamMember(person, currentTeamId), [person, currentTeamId]);

    useEffect(() => {
        const messageId = searchParams.get('message');
        if (!messageId) {
            return;
        }
        const element = document.getElementById(messageId);
        element?.scrollIntoView({ behavior: 'smooth' });
        if (element) {
            setSearchParams({}, { replace: true });
        }
    });

    const isDsacEnabled = useMemo(() => dsacSettings.$case === 'ready' && dsacSettings.isEnabled, [dsacSettings]);

    const {
        setLayoutHeader,
        setLayoutClassName,
        setLayoutIndent,
        setLayoutFooter,
        setLayoutFooterIndent,
        setLayoutBanner,
        setLayoutOnClose,
    } = useAppLayoutContext();
    setLayoutHeader(() => <DiscussionThreadHeader threadInfo={threadInfo} />, [threadInfo]);

    setLayoutIndent(() => false, []);
    setLayoutClassName(() => 'discussion_thread', []);
    setLayoutFooter(
        () => (
            <DiscussionThreadFooter
                threadInfo={threadInfo}
                currentTeamId={currentTeamId}
                currentTeamMember={currentTeamMember}
            />
        ),
        [threadInfo, currentTeamId, currentTeamMember]
    );
    setLayoutBanner(() => {
        if (threadInfo.thread.archivedAt) {
            return <RestorationBanner threadInfo={threadInfo} />;
        }
        if (showSensitiveContentBanner) {
            return (
                <ThreadSensitiveDataBanner
                    threadInfo={threadInfo}
                    closeBanner={() => setShowSensitiveContentBanner(false)}
                />
            );
        }
        return null;
    }, [threadInfo, showSensitiveContentBanner]);

    setLayoutOnClose(
        () => () => {
            navigateBackwards(DashboardUrls.mine(teamId));
        },
        [navigateBackwards, teamId]
    );

    setLayoutFooterIndent(() => false, []);

    // Should always be at least one message in a thread. TODO: better error handling?
    if (!threadInfo.messages.length) {
        return <div>No messages</div>;
    }

    return (
        <>
            <DiscussionThreadHubFallback />
            <div className="discussion_thread__body_content">
                <MarkReference threadInfo={threadInfo} variant="primary" />
                <ThreadView
                    thread={threadInfo}
                    messages={threadInfo.messages}
                    provider={threadInfo.thread.provider}
                    onNavigateUser={
                        !isDsacEnabled
                            ? (teamMemberId) => navigate(DashboardUrls.teamMember(teamId, teamMemberId))
                            : undefined
                    }
                />
            </div>
        </>
    );
};

const DiscussionThreadHeader = ({ threadInfo }: { threadInfo: ThreadInfoAggregate }) => {
    const store = useStore(ThreadStoreTraits, { teamId: threadInfo.thread.teamId, threadId: threadInfo.thread.id });
    const { thread } = threadInfo;
    const { openModal } = useModalContext();
    const navigate = useNavigate();

    const [editingTitle, setEditingTitle] = useState<boolean>(false);

    const slackInfo = useMemo(() => {
        if (!threadInfo.slack) {
            return null;
        }
        return (
            <span className="discussion_thread__subheader">
                <a className="discussion_thread__slack_channel" href={threadInfo.thread.links.externalUrl}>
                    #{threadInfo.slack.channelName}
                </a>
            </span>
        );
    }, [threadInfo]);

    const subheader = useMemo(() => {
        if (threadInfo.slack) {
            return slackInfo;
        }

        return null;
    }, [threadInfo, slackInfo]);

    const threadMenu = (
        <ThreadViewContextMenu
            iconSize="large"
            threadInfo={threadInfo}
            archiveThread={() => store.confirmArchive()}
            restoreThread={() => store.confirmRestore()}
            deleteThread={
                threadInfo.thread.provider === Provider.Unblocked
                    ? () =>
                          openModal(
                              <DeleteThreadDialog
                                  teamId={thread.teamId}
                                  threadId={thread.id}
                                  onNavigate={() => navigate(DashboardUrls.mine(thread.teamId))}
                              />
                          )
                    : undefined
            }
            enableEditTitle={canEditThreadTitle(threadInfo) ? () => setEditingTitle(true) : undefined}
        />
    );

    const classes = classNames({
        discussion_thread__header: true,
        discussion_thread__header__archived: !!threadInfo.thread.archivedAt,
    });

    return (
        <>
            <div className={classes}>
                <div className="discussion_thread__header__content">
                    <ThreadIcon threadInfo={threadInfo} size="xxLarge" />
                    {editingTitle ? (
                        <SaveInput
                            placeholder={threadInfo.thread.title}
                            value={thread.title}
                            onSubmitPromise={async (title: string) => {
                                await store.updateTitle(title);
                                setEditingTitle(false);
                            }}
                            onCancel={() => setEditingTitle(false)}
                            secondaryButtonVariant="outline"
                            disabled={(title: string) => title === thread.title}
                            autoFocus
                            fullWidth
                            autoSelect
                        />
                    ) : (
                        <h1>{thread.title}</h1>
                    )}
                    {subheader}
                </div>

                <div className="discussion_thread__header__metadata">
                    <Icon icon={faPipe} size="xLarge" className="discussion_thread__header__metadata__divider" />
                    <ToggleThreadPrivacyDropdown teamId={thread.teamId} threadId={thread.id} />
                    {threadMenu}
                </div>
            </div>
        </>
    );
};

// Basic parsing to see if the passed-in state is a ThreadInfoAggregate
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isThreadInfoAggregate(obj: any): obj is ThreadInfoAggregate {
    return obj && obj.thread && obj.thread.id && obj.messages;
}

const RestorationBanner = ({ threadInfo }: { threadInfo: ThreadInfoAggregate }) => {
    const thread = threadInfo.thread;
    const archivedAt = thread.archivedAt;
    const archivedBy = thread.archivedBy;
    const store = useStore(ThreadStoreTraits, { teamId: threadInfo.thread.teamId, threadId: threadInfo.thread.id });
    if (!archivedAt) {
        return null;
    }
    const restoreButton = (
        <Button variant="secondary" size="small" onClick={() => store.confirmRestore()}>
            Restore Discussion
        </Button>
    );
    return (
        <Banner
            icon={false}
            actions={restoreButton}
            className="discussion_thread__restoration_banner"
            variant="private"
        >
            <ArchivedText
                archivedBy={archivedBy}
                archivedAt={archivedAt}
                teamParticipants={threadInfo.teamParticipants}
            />
        </Banner>
    );
};

export const DiscussionThread = () => {
    const { teamId, threadId } = useParams();
    const [searchParams, setSearchParams] = useSearchParams();

    const state = useLocation().state as { threadInfo: ThreadInfoAggregate };
    let initialThreadInfo: ThreadInfoAggregate | undefined;

    useEffect(() => {
        if (searchParams.get('tutorial')) {
            searchParams.delete('tutorial');
            setSearchParams(searchParams);
        }
    }, [searchParams, setSearchParams]);

    useEffect(() => {
        if (teamId && threadId) {
            Events.threads.viewThread({
                teamId,
                threadId,
            });
        }
    }, [teamId, threadId]);
    if (isThreadInfoAggregate(state?.threadInfo)) {
        initialThreadInfo = state?.threadInfo;
    }

    if (!teamId) {
        // TODO: Error Pages
        return <div>Missing Team</div>;
    }
    if (!threadId) {
        // TODO: Error Pages
        return <div>Missing Insight</div>;
    }

    return <DiscussionThreadLoader teamId={teamId} threadId={threadId} initialThreadInfo={initialThreadInfo} />;
};

const DiscussionThreadFooter = ({
    threadInfo,
    currentTeamId,
    currentTeamMember,
}: {
    threadInfo: ThreadInfoAggregate;
    currentTeamId: string;
    currentTeamMember: TeamMember | undefined;
}) => {
    if (!threadInfo.messages.length) {
        return null;
    }

    return threadInfo.capabilities.canReply ? (
        <div className="discussion_thread__footer">
            <ScrollToBottomButton />
            <MessageInput teamId={threadInfo.thread.teamId} threadId={threadInfo.thread.id} />
            <LoopInExpertsList
                teamId={currentTeamId}
                experts={threadInfo.experts}
                currentTeamMemberId={currentTeamMember?.id}
                threadAuthorId={threadInfo.messages[0].authorTeamMemberId}
                buttonTemplate={(expert) => <ExpertButton key={expert.id} expert={expert} variant="secondary" />}
            />
        </div>
    ) : (
        <div className="discussion_thread__bottom_button_container">
            <ScrollToBottomButton />
        </div>
    );
};
