import { AutoResponseMode, SlackChannel } from '@shared/api/generatedApi';

import { IconCustomSize } from '@shared/webComponents/Icon/Icon';
import { IconRow } from '@web/components/Row/IconRow';

import { faAsterisk } from '@fortawesome/pro-regular-svg-icons/faAsterisk';
import { faHashtag } from '@fortawesome/pro-regular-svg-icons/faHashtag';
import { faLock } from '@fortawesome/pro-regular-svg-icons/faLock';

import { SlackListItem } from './SlackSettingsStore';

export function SlackItemRow({ item }: { item: SlackListItem }) {
    switch (item.$case) {
        case 'channel':
            return <SlackChannelRow channel={item.channel} />;

        case 'pattern':
            return <SlackPatternRow pattern={item.pattern} />;
    }
}

export const SlackChannelRow = ({ channel, iconSize }: { channel: SlackChannel; iconSize?: IconCustomSize }) => (
    <IconRow
        icon={channel.isPrivate ? faLock : faHashtag}
        title={`#${channel.name}`}
        iconSize={iconSize}
        className="slack_channel_row"
    >
        {channel.name}
    </IconRow>
);

export const SlackPatternRow = ({ pattern }: { pattern: string }) => (
    <IconRow icon={faAsterisk} title={`Public channels containing "${pattern}"`}>
        {' '}
        <span>
            Public channels containing <b>&ldquo;{pattern}&rdquo;</b>
        </span>
    </IconRow>
);

export const getAutoRespondModeLabel = (mode: AutoResponseMode): string => {
    switch (mode) {
        case AutoResponseMode.HighAccuracy:
            return 'High confidence';
        case AutoResponseMode.LowAccuracy:
            return 'Moderate confidence';
        case AutoResponseMode.Disabled:
            return "Don't auto-respond";
        case AutoResponseMode.UnknownDefaultOpenApi:
            return 'Unknown';
    }
};
