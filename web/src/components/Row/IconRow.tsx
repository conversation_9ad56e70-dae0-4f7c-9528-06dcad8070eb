import classNames from 'classnames';
import { HTMLAttributes } from 'react';

import { Icon, IconCustomSize, IconSrc } from '@shared/webComponents/Icon/Icon';

import './IconRow.scss';

type Props = {
    children: React.ReactNode;
    icon: IconSrc;
    iconSize?: IconCustomSize;
} & HTMLAttributes<HTMLDivElement>;

export const IconRow = ({ children, icon, iconSize = 'medium', className, ...props }: Props) => {
    return (
        <div className={classNames('icon_row', className)} {...props}>
            <Icon className="icon_row__icon" icon={icon} size={iconSize} />
            <div className="icon_row__content">{children}</div>
        </div>
    );
};
