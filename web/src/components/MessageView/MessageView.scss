@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'media' as *;
@use 'fonts-mixin' as *;
@use 'fonts' as *;
@use 'flex' as *;

.message_view {
    @media (max-width: $small) {
        // 'Compressed' message view for smaller viewports
        grid-template:
            'avatar author references actions'
            'avatar date references actions'
            'title title title title'
            'attachments attachments attachments attachments'
            'content content content content'
            / auto 1fr auto auto;
    }

    .message_view__actions {
        color: themed($message-actions);

        & > div {
            height: $size-18;
            width: $size-18;
            border-radius: $border-radius-circle $border-radius-circle;
            transition: all 0.1s ease-in;
            transition-property: background, color;
            @include flex-center-center;

            .icon {
                margin-right: 0;
            }

            &:hover {
                background: themed($context-menu-hover-bg);
            }
        }
    }

    .message_actions {
        .icon {
            opacity: 0.6;
            cursor: pointer;
        }
    }

    .message_view__author {
        @include paragraph;

        font-weight: $font-weight-bold;
        color: themed($text);
    }

    &.message_view__author_navigable {
        .message_view__author:hover {
            color: themed($link);
        }

        .message_view__icon:hover {
            ~ .message_view__author {
                color: themed($link);
            }
        }
    }

    .message_view__icon,
    .message_view__preset_icon__container {
        margin-top: $spacer-1;
        margin-right: $spacer-12;
        @include icon-size(xx-large);

        border: none;
    }

    .message_view__preset_icon {
        @include icon-size(xx-large);
    }

    .message_view__timestamp {
        @include paragraph;

        color: themed($text-tertiary);
        align-self: baseline;

        @media (max-width: $small) {
            @include subline-spacing;
        }
    }

    .message_view__dropdown_references,
    .message_view__followup {
        @include detail;

        font-size: $font-size-13;
    }

    .message_view__message {
        color: themed($text-secondary);
        margin-top: $spacer-6;
        @include paragraph;

        @include breakpoint(lg) {
            @include subline-spacing;

            .code_block {
                margin-top: $spacer-8;
            }
        }

        p {
            color: themed($text-secondary);
        }
    }

    .message_view__message p:last-of-type {
        margin-bottom: 0;
    }

    .message_reference_list {
        display: flex;
        align-items: flex-start;
        gap: $spacer-4 $spacer-6;

        margin: $spacer-12 0;

        .message_reference_list__header {
            font-size: $font-size-13;
            font-weight: $font-weight-normal;
            color: themed($text);
            margin-top: $spacer-2;
        }
    }

    .message_view__feedback_container {
        padding: $spacer-6 0;
        margin-top: $spacer-6;
    }

    .message_view__feedback {
        color: themed($text-tertiary);
        font-size: $font-size-13;
        line-height: $line-height-16;
    }

    // set sibling top border if current not first of type
    & + .message_view:not(:first-of-type) {
        border-top-color: themed($message-border);
    }

    &:last-of-type {
        border-bottom-color: themed($message-border);
    }

    blockquote {
        // Rounded bar to the left of the quote
        &::before {
            content: '';
            margin-right: $spacer-8;
            border-left: $border-width-3 $border-style themed($quote-element-border);
            border-radius: $border-width-3;
            position: absolute;
            top: $spacer-0;
            bottom: $spacer-0;
            left: $spacer-0;
        }

        position: relative;
        margin: $spacer-8;
        padding: 0 $spacer-12;
        color: themed($quote-element-text);
    }

    .code_block {
        margin: $spacer-12 0;
    }

    code {
        font-family: $source;
        font-size: $font-size-13;
        background: themed($code-inline-element-bg);
        border-radius: $spacer-2;
        padding: 0 $spacer-2;
    }

    pre {
        font-family: $source;
        border-radius: $spacer-6;
        font-size: $font-size-13;
        line-height: $line-height-20;
        background: themed($code-element-bg);
        padding: $spacer-12;
        margin-bottom: $spacer-12;
        overflow-x: auto;
    }

    ul {
        margin-bottom: $spacer-12;
    }

    ol {
        margin-bottom: $spacer-12;
    }

    .video_block_metadata {
        margin-top: $spacer-20;

        .tabs {
            border-color: themed($border);
            border-radius: $border-radius-2;

            .tabs__headers {
                margin-top: -$spacer-1;
            }

            .tab_header {
                flex: 1;

                &:first-of-type {
                    border-left: none;
                    border-top-left-radius: $border-radius-2;
                }

                &:last-of-type {
                    border-top-right-radius: $border-radius-2;
                }
            }
        }

        .file_position_link,
        .transcription_segment__start {
            font-family: $source;
        }

        .transcription__error {
            .transcription__error__cause {
                font-family: $source;
            }
        }

        .transcription_segment {
            &.transcription_segment--highlighted,
            &:hover {
                background-color: themed($transcription-highlight-bg);
            }
        }
    }
}

.code_references__row {
    height: $size-48;
    border-bottom: none !important;
    border-top-color: themed($border);

    .reference_title_item {
        grid-template-columns: $size-20 1fr;
        grid-column-gap: $spacer-16;

        .file_icon,
        .url_icon {
            flex: none;

            @include icon-size(large);

            svg {
                color: themed($text-secondary);
            }
        }
    }
}

.message_header {
    &.message_header__author_navigable {
        .message_header__author:hover {
            color: themed($link);
        }

        .message_header__icon:hover {
            ~ .message_header__author {
                color: themed($link);
            }
        }
    }
}

.message_view_loading {
    > * {
        background: themed($message-loading);
    }
}
