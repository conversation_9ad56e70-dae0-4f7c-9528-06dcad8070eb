@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'flex' as *;
@use 'media' as *;
@use 'animations' as *;
@use 'colors' as *;

@use './LayoutContext' as *;

$detail-layout-header-min-height: 34px;

@mixin entry-transition {
    &.transition {
        transition-property: transform margin;
        transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
        transition-duration: 400ms;
    }

    &.transition-top {
        transform: translateY($spacer-24);
    }

    &.transition-bottom {
        transform: translateY(0);
    }
}

@mixin disabled-overlay {
    position: relative;
    pointer-events: none;

    &::after {
        content: '';
        position: absolute;
        height: 100%;
        width: 100%;
        inset: 0;
        z-index: 5;
        pointer-events: none;
        background-color: themed($content-overlay);
    }
}

.quarter_round {
    display: none;
    @include breakpoint(md) {
        display: block;
    }
    width: $border-radius-8;
    height: $border-radius-8;
    background-color: themed($bg);
    position: absolute;
    bottom: $layout-spacer-tiny;

    .quarter_round__content {
        width: 100%;
        height: 100%;
        background-color: themed($pinned-header-bg);
    }

    &.quarter_round__left {
        .quarter_round__content {
            border-bottom-left-radius: $border-radius-8;
        }

        left: 0;
    }

    &.quarter_round__right {
        .quarter_round__content {
            border-bottom-right-radius: $border-radius-8;
        }

        right: 0;
    }
}

.main_layout_view {
    flex: 1;
    position: relative;
    background: themed($nav-bg);
    color: themed($text);
    @include container-shadow;
    @include flex-column;

    @include breakpoint(md) {
        min-height: auto;
        border-radius: $border-radius-8;
        &.main_layout_view__wide {
            overflow: clip visible;
        }
    }

    .main_layout_view__bottom_container {
        grid-area: footer;
        position: sticky;
        z-index: 10;
        bottom: 0;
        background-color: themed($bg);

        @include breakpoint(md) {
            padding-bottom: $layout-spacer-tiny;
        }
    }

    .main_layout_view__footer {
        background-color: themed($pinned-header-bg);

        &.main_layout_view__footer__indented {
            padding: $spacer-16;
            border-top: $border-style $border-width themed($header-border);
        }

        &.main_layout_view__footer__disabled {
            @include disabled-overlay;
        }

        @include breakpoint(md) {
            border-bottom-left-radius: $border-radius-8;
            border-bottom-right-radius: $border-radius-8;
        }
    }

    .main_layout_view__top_container {
        grid-area: header;
        position: sticky;
        z-index: 10;
        top: 0;
        background-color: themed($bg);

        @include breakpoint(md) {
            padding-top: $layout-spacer-tiny;
        }

        .tabs {
            &.tabs__primary,
            &.tabs__secondary {
                background-color: themed($nav-bg);
                margin-top: -$spacer-1;

                .tabs__headers::after {
                    border-top: $border-width $border-style themed($border);
                    margin-bottom: 0;
                }
            }

            &.tabs__secondary {
                border-radius: $border-radius-6;
            }
        }
    }

    .main_layout_view__header {
        background-color: themed($pinned-header-bg);
        padding-right: $spacer-16;
        display: grid;

        &.main_layout_view__header__with_content {
            grid-template:
                'headerContent buttons' 1fr
                / 1fr auto;
            border-bottom: $border-style $border-width themed($header-border);
        }

        &.main_layout_view__header__with_header_banner {
            grid-template:
                'headerBanner headerBanner' auto
                'headerContent buttons' 1fr
                / 1fr auto;
            padding-right: 0;

            .banner {
                @include fill-available;
            }
        }

        .main_layout_view__header_content {
            grid-area: headerContent;
            min-height: $detail-layout-header-min-height;
            padding: $spacer-8 0;
            color: themed($text);
            @include flex-center-between;

            .main_layout_inline_nav {
                display: none;
            }

            @media ($tablet <= width <= $small) {
                .main_layout_inline_nav {
                    display: block;

                    .main_layout_inline_nav__content {
                        width: 180px;
                    }
                }
            }
        }

        .main_layout_view__buttons {
            grid-area: buttons;
        }

        .main_layout_view__header_banner {
            grid-area: headerBanner;
            @include flex-center-center;
            @include fill-available;

            padding: $spacer-16;
        }

        @include header-shadow;

        @include breakpoint(md) {
            border-top-left-radius: $border-radius-8;
            border-top-right-radius: $border-radius-8;
            min-height: $spacer-60;
        }
    }

    .main_layout_view__banner {
        @include shadow-border;

        .banner:not(.banner__floating) {
            border-right: none;
            border-left: none;
        }
    }

    .main_layout_view__pinned_content {
        background: themed($pinned-header-bg);
        border-bottom: $border-width $border-style themed($border);

        &.main_layout_view__pinned_content__indented {
            padding: 0 $spacer-20;
        }

        @include breakpoint(md) {
            &.main_layout_view__content__indented {
                padding: 0 $spacer-24;
            }
        }
    }

    .main_layout_view__content {
        flex: 1;

        &.main_layout_view__content__indented {
            padding: 0 $spacer-20;
        }

        &.main_layout_view__content__disabled {
            @include disabled-overlay;
        }

        @include breakpoint(md) {
            border-bottom-left-radius: $border-radius-8;
            border-bottom-right-radius: $border-radius-8;

            &.main_layout_view__content__indented {
                padding: 0 $spacer-24;
            }
        }
    }

    .main_layout_view__buttons {
        @include flex-center;

        .main_layout_view__divider {
            color: themed($border);
        }

        .main_layout_view__button {
            color: themed($link);
            border-radius: $border-radius-circle;
            padding: $spacer-6 $spacer-6;
            transition: background 0.1s ease-in;
            @include flex-center-center;

            &:hover {
                background: themed($context-menu-hover-bg);
            }
        }
    }

    &.main_layout_view__light {
        background: themed($thread-summary-bg);

        .main_layout_view__header {
            background: themed($thread-summary-bg);
        }

        .tabs {
            &.tabs__primary,
            &.tabs__secondary {
                background: transparent;

                .tabs__headers {
                    background-color: themed($thread-summary-bg);

                    .tab_header {
                        background: transparent;

                        &:first-of-type {
                            border-left-color: themed($thread-summary-bg);
                        }

                        &.tab_header__selected {
                            border-bottom-color: themed($thread-summary-bg);
                        }
                    }
                }
            }

            &.tabs__secondary {
                border-radius: $border-radius-6;
            }
        }

        .main_layout_view__pinned_content {
            background: themed($thread-summary-bg);
        }

        .quarter_round {
            .quarter_round__content {
                background-color: themed($thread-summary-bg);
            }
        }
    }
}

.main_layout_nav_container {
    position: sticky;
    display: grid;
    grid-template: 'nav content' max-content / max-content 1fr;

    @include breakpoint(md) {
        .main_layout_view {
            animation: none;
        }
    }

    .main_layout_view__nav {
        grid-area: nav;
        position: sticky;
        top: 0;
        bottom: 0;
        z-index: 15;
        border-right: $border-width $border-style themed($bg);
        height: 100vh;
        @include flex-column;

        .main_layout_view__nav_content {
            padding: $spacer-20;
            background: themed($thread-summary-bg);
            height: 100%;
        }

        .main_layout_view__nav_top {
            background-color: themed($bg);
            position: sticky;
            top: 60px;
        }

        .main_layout_view__nav_bottom {
            background-color: themed($bg);
            position: sticky;
            bottom: 0;

            .quarter_round__left {
                .quarter_round__content {
                    background: themed($thread-summary-bg);
                }
            }
        }

        @include breakpoint(md) {
            .main_layout_view__nav_top {
                top: 0;
                padding-top: $spacer-8;
            }

            .main_layout_view__nav_bottom {
                padding-bottom: $spacer-8;
            }

            .main_layout_view__nav_content {
                border-top-left-radius: $border-radius-8;
                border-bottom-left-radius: $border-radius-8;
            }

            & ~ .main_layout_view__full_height .main_layout_view {
                .main_layout_view__top_container {
                    .main_layout_view__header {
                        border-top-left-radius: 0;
                    }
                }

                .main_layout_view__bottom_container {
                    .quarter_round__left {
                        display: none;
                    }
                }
            }
        }

        @include breakpoint(lg) {
            width: 210px;
        }
    }
}

.main_layout_view__full_height {
    display: flex;
    flex-direction: column;
    flex: 1;
}
