@use '../../styles/colors' as *;
@use '../../styles/fonts' as *;
@use '../../styles/layout' as *;
@use '../../styles/layout-mixin' as *;
@use '../../styles/misc' as *;
@use '../../styles/theme' as *;
@use '../../styles/flex' as *;

// constants
$home-header-height: 0;
$banner-height: 40px;
$home-header-banner-height: $home-header-height + $banner-height; // home header + banner
$home-header-sm-height: 60px;
$home-header-banner-sm-height: $home-header-sm-height + $banner-height; // home header + banner
$detail-layout-header-height: 68px;
$detail-layout-header-sm-height: 50px;
$pinned-subheader-offset: $detail-layout-header-height;
$pinned-subheader-banner-offset: $detail-layout-header-height + $banner-height;
$pinned-subheader-sm-offset: $home-header-sm-height + $detail-layout-header-sm-height;
$pinned-subheader-sm-banner-offset: $home-header-sm-height + $detail-layout-header-sm-height + $banner-height;
$layout-spacer: $spacer-20;
$layout-spacer-large: $spacer-24;
$layout-spacer-small: $spacer-16;
$layout-spacer-tiny: $spacer-8;

// Left and right column widths
$side-column-width-md: 188px + $layout-spacer;
$side-column-width-lg: 202px + $layout-spacer;
$side-column-width-xlg: 280px + $layout-spacer;

// Body column widths
$body-column-lg-min: 540px + $layout-spacer + $layout-spacer;
$body-column-lg-max: 760px + $layout-spacer + $layout-spacer;

// Basic properties
.navigator {
    display: grid;
    width: 100%;
    height: 100%;
    overflow-y: auto;

    &.navigator__lock_scroll {
        overflow-y: hidden;
    }

    .navigator__body {
        grid-area: body;
        z-index: 0;

        main {
            display: flex;
            flex-direction: column;
        }

        .main_layout_nav_container {
            .main_layout_view__nav_top {
                top: $home-header-sm-height;
            }

            @include breakpoint(md) {
                .main_layout_view__nav_top {
                    top: $home-header-height;
                }
            }
        }

        .main_layout_view {
            min-height: calc(100vh - $home-header-sm-height);

            .main_layout_view__top_container {
                top: $home-header-sm-height;
            }

            .detail_pinned_subheader {
                top: $pinned-subheader-sm-offset;
            }

            @include breakpoint(md) {
                min-height: 100vh;

                .main_layout_view__top_container {
                    top: $home-header-height;
                }

                .detail_pinned_subheader {
                    top: $pinned-subheader-offset;
                }
            }
        }
    }

    .navigator__header {
        grid-area: header;
        position: sticky;
        top: 0;
        z-index: 15;
    }

    // Left and right columns stick to top
    .navigator__left_column,
    .navigator__right_column {
        position: sticky;
        top: $home-header-height;
        align-self: start;
        display: none;
        box-sizing: border-box;
    }

    .navigator__left_column {
        grid-area: left;
        padding: $layout-spacer-large 0 $layout-spacer $layout-spacer;
        z-index: 1;
        height: 100vh;

        .navigator__left_column__content {
            @include flex-column;

            overflow: hidden;

            .desktop_navigator {
                overflow: auto;

                &::-webkit-scrollbar {
                    display: none;
                }
            }
        }

        .unblocked_home_icon {
            margin-bottom: $spacer-12;
        }
    }

    .navigator__right_column {
        grid-area: right;
        padding: $layout-spacer-tiny $layout-spacer $layout-spacer 0;
    }

    &.navigator__with_banner {
        .main_layout_view {
            min-height: calc(100vh - $home-header-banner-sm-height);

            .detail_pinned_subheader {
                top: $pinned-subheader-sm-banner-offset;
            }
        }

        @include breakpoint(md) {
            .main_layout_view {
                min-height: calc(100vh - $home-header-banner-height);

                .detail_pinned_subheader {
                    top: $pinned-subheader-banner-offset;
                }
            }
        }
    }

    .navigator__left_column_button {
        margin-bottom: $spacer-16;

        .button {
            width: 100%;
        }
    }
}

// Small display (< 768px): show body only
/* stylelint-disable-next-line no-duplicate-selectors */
.navigator {
    // Small display (< 768px): only display content
    grid:
        'header' auto
        'body' 1fr
        / #{100%};

    &.navigator__layout_overview {
        .navigator__body {
            padding: $layout-spacer $layout-spacer-small;
        }
    }
}

// Medium display (between 768px and 1024px) : left column and content
@include breakpoint(md) {
    .navigator {
        grid:
            'header header' auto
            'left body' 1fr /
            #{$side-column-width-md} minmax(0, 1fr);

        .navigator__left_column {
            @include flex-column;

            justify-content: space-between;
        }

        &.navigator__layout_overview {
            .navigator__body {
                padding: $layout-spacer;
            }
        }

        &.navigator__layout_detail {
            .navigator__body {
                padding: 0 $layout-spacer-tiny 0 $layout-spacer;
            }
        }

        &.navigator__layout_wide {
            .navigator__right_column {
                display: none;
            }

            .navigator__body {
                padding: 0 $layout-spacer-tiny 0 $layout-spacer;
            }
        }
    }
}

// Large display (> 1024px): left column, right column, and content
@include breakpoint(lg) {
    .navigator {
        grid:
            'header header header header header' auto
            'spacer-left left body right spacer-right' 1fr /
            1fr
            #{$side-column-width-lg}
            minmax($body-column-lg-min, $body-column-lg-max)
            #{$side-column-width-lg}
            1fr;

        .navigator__right_column {
            display: grid;
        }

        &.navigator__layout_detail {
            .navigator__body {
                padding-right: $layout-spacer;
            }
        }

        &.navigator__layout_wide {
            grid:
                'header header header header' auto
                'spacer-left left body spacer-right' 1fr /
                1fr
                #{$side-column-width-lg}
                minmax($body-column-lg-min, calc($body-column-lg-max + $side-column-width-lg))
                1fr;

            .navigator__body {
                padding: 0 $layout-spacer-tiny 0 $layout-spacer;
            }
        }
    }
}

// X-large display (> 1280px): left column, right column, and content
@include breakpoint(xl) {
    .navigator {
        grid:
            'header header header header header' auto
            'spacer-left left body right spacer-right' 1fr /
            1fr
            #{$side-column-width-xlg}
            minmax($body-column-lg-min, $body-column-lg-max)
            #{$side-column-width-xlg}
            1fr;

        &.navigator__layout_wide {
            grid:
                'header header header header' auto
                'spacer-left left body spacer-right' 1fr /
                1fr
                #{$side-column-width-xlg}
                minmax($body-column-lg-min, calc($body-column-lg-max + $side-column-width-xlg))
                1fr;
        }
    }
}

html,
body,
#root,
#root > div,
.app {
    height: 100%;
}
