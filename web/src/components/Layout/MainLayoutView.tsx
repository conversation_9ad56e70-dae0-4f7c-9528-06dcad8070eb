import classNames from 'classnames';
import { forwardRef, Ref, useEffect, useMemo } from 'react';

import { AnimateContent } from '@shared/webComponents/Animation/AnimateContent';
import { Icon } from '@shared/webComponents/Icon/Icon';
import { Tab<PERSON>eaders<PERSON>enderer, TabHeadersRendererProps } from '@shared/webComponents/Tabs/Tabs';
import { useMediaBreakpoint } from '@web/hooks/useMediaBreakpoint';

import { faPipe } from '@fortawesome/pro-light-svg-icons/faPipe';
import { faClose } from '@fortawesome/pro-solid-svg-icons/faClose';

import { LayoutType, useLayoutContext } from './LayoutContext';
import { CloseContextProvider, MainLayoutInlineNav } from './MainLayoutInlineNav';

import './MainLayoutView.scss';

export type ViewVariant = 'primary' | 'light';
export type ViewWidth = 'standard' | 'wide';

interface Props {
    className?: string;
    nav?: React.ReactNode;
    header?: React.ReactNode | false;
    headerBanner?: React.ReactNode;
    actions?: React.ReactNode;
    banner?: React.ReactNode;
    children: React.ReactNode;
    footer?: React.ReactNode;
    indentContent?: boolean;
    indentFooter?: boolean;
    onClose?: () => void;
    disabled?: boolean;
    variant?: ViewVariant;
    width?: ViewWidth;
    tabs?: TabHeadersRendererProps | null;
    pinnedContent?: React.ReactNode;
}

// Helper class, renders a small "quarter round" border, so that the
// bottom corner appears rounded correctly
function QuarterRound(props: { side: 'left' | 'right' }) {
    const classnames = classNames({
        quarter_round: true,
        [`quarter_round__${props.side}`]: true,
    });

    return (
        <div className={classnames}>
            <div className="quarter_round__content" />
        </div>
    );
}

const MainLayoutViewBase = (
    {
        className,
        header,
        headerBanner,
        actions,
        banner,
        children,
        footer,
        onClose,
        indentContent = true,
        indentFooter = true,
        disabled,
        variant = 'primary',
        width = 'standard',
        tabs,
        pinnedContent,
        nav,
    }: Props,
    ref: Ref<HTMLDivElement>
) => {
    const atLargeBreakpoint = useMediaBreakpoint('lg');
    const { setWithBanner } = useLayoutContext();

    useEffect(() => {
        if (banner) {
            setWithBanner(true);
        } else {
            setWithBanner(false);
        }
    }, [banner, setWithBanner]);

    const buttons = useMemo(() => {
        if (onClose) {
            return (
                <div className="main_layout_view__buttons">
                    {header ? <Icon icon={faPipe} size="xLarge" className="main_layout_view__divider" /> : null}
                    <Icon icon={faClose} className="main_layout_view__button" onClick={onClose} size="medium" />
                </div>
            );
        }
        return actions;
    }, [onClose, header, actions]);

    const bannerContainer = useMemo(() => {
        return (
            <AnimateContent axis="height">
                {banner && <div className="main_layout_view__banner">{banner}</div>}
            </AnimateContent>
        );
    }, [banner]);

    const containerClasses = classNames({
        main_layout_view: true,
        [`main_layout_view__${variant}`]: true,
        [`main_layout_view__${width}`]: true,
        [`${className}`]: !!className,
    });

    const headerClasses = classNames({
        main_layout_view__header: true,
        auto_scroller__header_target: true,
        main_layout_view__header__with_header_banner: !!headerBanner,
        main_layout_view__header__with_content: !!header,
    });

    const pinnedContentClasses = classNames({
        main_layout_view__pinned_content: true,
        main_layout_view__pinned_content__indented: indentContent,
    });

    const contentClasses = classNames({
        main_layout_view__content: true,
        main_layout_view__content__indented: indentContent,
        main_layout_view__content__disabled: disabled,
    });

    const footerClasses = classNames({
        main_layout_view__footer: true,
        main_layout_view__footer__indented: indentFooter,
        main_layout_view__footer__disabled: disabled,
    });

    const mainView = useMemo(() => {
        return (
            <div className={containerClasses}>
                <div className="main_layout_view__top_container">
                    <div className={headerClasses}>
                        {headerBanner ? <div className="main_layout_view__header_banner">{headerBanner}</div> : null}
                        {header !== false ? (
                            <>
                                <div className="main_layout_view__header_content">
                                    {nav ? <MainLayoutInlineNav>{nav}</MainLayoutInlineNav> : null}
                                    {header}
                                </div>
                                {buttons}
                            </>
                        ) : null}
                    </div>
                    {bannerContainer}
                    {tabs ? <TabHeadersRenderer {...tabs} /> : null}
                    {pinnedContent ? <div className={pinnedContentClasses}>{pinnedContent}</div> : null}
                </div>
                <div className={contentClasses} ref={ref}>
                    {children}
                </div>
                <div className="main_layout_view__bottom_container">
                    <QuarterRound side="left" />
                    <QuarterRound side="right" />
                    {footer && <div className={footerClasses}>{footer}</div>}
                </div>
            </div>
        );
    }, [
        bannerContainer,
        containerClasses,
        headerClasses,
        contentClasses,
        pinnedContentClasses,
        footerClasses,
        ref,
        header,
        headerBanner,
        tabs,
        buttons,
        children,
        footer,
        nav,
        pinnedContent,
    ]);

    const { setLayout } = useLayoutContext();

    useEffect(() => {
        switch (width) {
            case 'standard':
                setLayout(LayoutType.detail);
                break;

            case 'wide':
                setLayout(LayoutType.wide);
        }
        return () => setLayout(LayoutType.detail);
    }, [setLayout, width]);

    if (nav && atLargeBreakpoint) {
        return (
            <div key="parent-wrapper" className="main_layout_nav_container">
                <div className="main_layout_view__nav">
                    <div className="main_layout_view__nav_top" />
                    <div className="main_layout_view__nav_content">
                        <CloseContextProvider>{nav}</CloseContextProvider>
                    </div>
                    <div className="main_layout_view__nav_bottom">
                        <QuarterRound side="left" />
                    </div>
                </div>

                <div key="main-view-wrapper" className="main_layout_view__full_height">
                    {mainView}
                </div>
            </div>
        );
    }

    // We have to add these goofy wrappers so that the main view stays in the same place
    // in the DOM, so react knows not to umount/remount when we switch between nav & non-nav styles
    return (
        <div key="parent-wrapper" className="main_layout_view__full_height">
            <div key="main-view-wrapper" className="main_layout_view__full_height">
                {mainView}
            </div>
        </div>
    );
};

export const MainLayoutView = forwardRef(MainLayoutViewBase);
