import classNames from 'classnames';
import { ReactNode, useEffect, useRef } from 'react';
import { useLocation, useNavigationType } from 'react-router-dom';

import { ScrollContextProvider, useScrollContext } from '@shared/webComponents/ScrollContext';
import { isReactChildFunction, ReactNodeOrFunction } from '@shared/webUtils/ReactUtils';
import { UnblockedNavLinkIcon } from '@web/home/<USER>';

import { LayoutContextProvider, useLayoutContext } from '../Layout/LayoutContext';
import { MobileNavigator } from './MobileNavigator';
import { NavigationSectionItem } from './NavigationSection';
import { NavigationSections, NavigationSectionsProps } from './NavigatorComponents';

import './Navigator.scss';

const DesktopNavigator = ({ sections }: NavigationSectionsProps) => {
    return (
        <div className="desktop_navigator">
            {/* Sidebar component, swap this element with another sidebar if you like */}
            <nav>
                {/* Hidden for mobile */}
                <NavigationSections sections={sections} />
            </nav>
        </div>
    );
};

const ScrollToTopOnNavigate = () => {
    const location = useLocation();
    const navigationType = useNavigationType();
    const { scrollToTop } = useScrollContext();

    useEffect(() => {
        if (navigationType === 'PUSH') {
            scrollToTop();
        }
    }, [location, scrollToTop, navigationType]);

    return null;
};

const NavigatorContent = ({
    navigatorButton,
    sections,
    profileSection,
    profilePosition = 'bottom',
    children,
}: NavigatorProps) => {
    const scrollRef = useRef<HTMLDivElement>(null);
    const contentRef = useRef<HTMLDivElement>(null);
    const { layout, rightColumnContent, lockScroll, withBanner } = useLayoutContext();

    const navigatorClasses = classNames({
        navigator: true,
        navigator__with_banner: withBanner,
        [`navigator__layout_${layout}`]: true,
        navigator__lock_scroll: lockScroll,
    });

    const profile = isReactChildFunction(profileSection) ? profileSection({}) : profileSection;

    const rightColumnLayoutClassName = classNames({
        navigator__right_column: true,
    });

    return (
        <ScrollContextProvider scrollElement={scrollRef} contentElement={contentRef}>
            <ScrollToTopOnNavigate />
            <div className={navigatorClasses} ref={scrollRef}>
                <div className="navigator__header">
                    <MobileNavigator sections={sections} profileSection={profileSection} />
                </div>

                <div className="navigator__left_column">
                    <div className="navigator__left_column__content">
                        <UnblockedNavLinkIcon />
                        {profilePosition === 'top' ? (
                            <div className="navigator__left_column__profile">{profile}</div>
                        ) : null}
                        {navigatorButton ? (
                            <div className="navigator__left_column_button">{navigatorButton}</div>
                        ) : null}
                        <DesktopNavigator sections={sections} />
                    </div>
                    {profilePosition === 'bottom' ? profile : null}
                </div>
                <div className="navigator__body">
                    <main ref={contentRef}>{children}</main>
                </div>
                <div className={rightColumnLayoutClassName}>{rightColumnContent}</div>
            </div>
        </ScrollContextProvider>
    );
};

export interface NavigatorProps {
    navigatorButton?: ReactNode;
    sections: NavigationSectionItem[];
    profileSection: ReactNodeOrFunction<{ close?: () => void }>;
    profilePosition?: 'top' | 'bottom';
    children: ReactNode;
}

export const Navigator = (props: NavigatorProps) => {
    return (
        <LayoutContextProvider>
            <NavigatorContent {...props} />
        </LayoutContextProvider>
    );
};
