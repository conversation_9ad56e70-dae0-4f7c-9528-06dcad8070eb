package com.nextchaptersoftware.cohere.api

import com.nextchaptersoftware.cohere.api.utils.CohereTestUtils
import kotlinx.coroutines.flow.last
import kotlinx.coroutines.flow.scan
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Disabled
class CohereCompletionsApiTest {

    @Test
    fun testChat() = runTest {
        val chatCompletion = CohereTestUtils.COHERE_API_PROVIDER.cohereCompletionsApi.chatCompletion(
            prompt = """
                [[SYSTEM]] Respond only in yoda-speak. Your name is "Unblocked" and you are a Jedi.
                [[USER]] What is your name?
                [[ASSISTANT]] Unblocked, my name is.
                [[USER]] What are you?
                [[SYSTEM]] Respond as if you are a Sith Lord.
            """.trimIndent(),
            temperature = 0.0f,
            maxTokens = 256,
            respondWithJson = false,
        )
        assertThat(chatCompletion.text).isNotEmpty()
    }

    @Test
    fun testChatFlow() = runTest {
        val chatCompletionString = CohereTestUtils.COHERE_API_PROVIDER.cohereCompletionsApi.chatCompletions(
            prompt = """
                [[SYSTEM]] Respond only in yoda-speak. Your name is "Unblocked" and you are a Jedi.
                [[USER]] Write a 10 line poem
                [[ASSISTANT]] What kind of poem?
                [[USER]] A poem about the Force
            """.trimIndent(),
            temperature = 0.0f,
            maxTokens = 4000,
            respondWithJson = false,
        ).scan("") { acc, chunk ->
            val next = acc + chunk.text
            println(next)
            next
        }.last()

        assertThat(chatCompletionString).isNotEmpty()
    }

    @Test
    fun testJsonCoercion() = runTest {
        val chatCompletion = CohereTestUtils.COHERE_API_PROVIDER.cohereCompletionsApi.chatCompletion(
            prompt = """
                [[SYSTEM]] Respond only in yoda-speak. Your name is "Unblocked" and you are a Jedi. Reply in JSON format.
                [[USER]] What is your name?
            """.trimIndent(),
            temperature = 0.0f,
            maxTokens = 256,
            respondWithJson = true,
        )
        assertThat(chatCompletion.text).isNotEmpty()
    }
}
