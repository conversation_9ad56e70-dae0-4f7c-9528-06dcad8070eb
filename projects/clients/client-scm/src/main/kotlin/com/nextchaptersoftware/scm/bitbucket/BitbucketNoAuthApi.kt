package com.nextchaptersoftware.scm.bitbucket

import com.nextchaptersoftware.scm.ScmNoAuthApi
import com.nextchaptersoftware.scm.ScmNoAuthHttpClientFactory.makeScmNoAuthHttpClient
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketOrg
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketUser
import com.nextchaptersoftware.scm.github.models.AppConfig
import com.nextchaptersoftware.scm.models.ScmOrg
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmUser
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.http.Url

internal class BitbucketNoAuthApi(
    private val baseApiUrl: Url,
) : ScmNoAuthApi {

    private val client = makeScmNoAuthHttpClient(
        orgIds = null,
        baseApiUrl = baseApiUrl,
    )

    override suspend fun getOrgByLogin(login: String): ScmOrg {
        return client.get("workspaces/$login").body<BitbucketOrg>().asScmOrg
    }

    override suspend fun getUserByLogin(login: String): ScmUser {
        return client.get("users/$login").body<BitbucketUser>().asScmUser
    }

    override suspend fun getPublicRepo(orgName: String, repoName: String): ScmRepository {
        error("Not applicable")
    }

    override suspend fun getPublicRepoLanguages(orgName: String, repoName: String): Map<String, Int> {
        error("Not applicable")
    }

    override suspend fun completeAppManifest(code: String): AppConfig {
        error("Not applicable")
    }

    override fun close() {
        client.close()
    }
}
