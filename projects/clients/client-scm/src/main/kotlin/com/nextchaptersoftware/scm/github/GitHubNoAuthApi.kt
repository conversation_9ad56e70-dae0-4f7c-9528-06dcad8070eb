package com.nextchaptersoftware.scm.github

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.scm.ScmNoAuthApi
import com.nextchaptersoftware.scm.ScmNoAuthHttpClientFactory.makeScmNoAuthHttpClient
import com.nextchaptersoftware.scm.github.models.AppConfig
import com.nextchaptersoftware.scm.github.models.GitHubOrg
import com.nextchaptersoftware.scm.github.models.GitHubRepo
import com.nextchaptersoftware.scm.github.models.GitHubUser
import com.nextchaptersoftware.scm.models.HostAvailability
import com.nextchaptersoftware.scm.models.HostAvailabilityStatus
import com.nextchaptersoftware.scm.models.ScmOrg
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmUser
import io.ktor.client.call.body
import io.ktor.client.plugins.retry
import io.ktor.client.plugins.timeout
import io.ktor.client.request.get
import io.ktor.client.request.head
import io.ktor.client.request.post
import io.ktor.http.Url
import kotlin.time.Duration.Companion.seconds

internal class GitHubNoAuthApi(
    orgIds: Set<OrgId>?,
    private val baseApiUrl: Url,
) : ScmNoAuthApi, ScmNoAuthApi.WithHostAvailability {

    companion object {
        fun fromAuthority(orgIds: Set<OrgId>?, authority: String): GitHubNoAuthApi {
            return GitHubNoAuthApi(
                orgIds = orgIds,
                baseApiUrl = Url("https://$authority/api/v3/"),
            )
        }
    }

    private val client = makeScmNoAuthHttpClient(
        orgIds = orgIds,
        baseApiUrl = baseApiUrl,
    )

    override suspend fun getHostAvailability(): HostAvailabilityStatus {
        return HostAvailability.checkAvailability(baseApiUrl.host) {
            isEnterpriseHost()
        }
    }

    private suspend fun isEnterpriseHost(): Boolean {
        return client.head("meta") {
            timeout {
                requestTimeoutMillis = 4.seconds.inWholeMilliseconds
            }
            retry {
                maxRetries = 0
            }
        }.let { response ->
            response.headers.names().any { it.startsWith("x-github-", ignoreCase = true) }
        }
    }

    override suspend fun completeAppManifest(code: String): AppConfig {
        return client.post("app-manifests/$code/conversions").body()
    }

    override suspend fun getOrgByLogin(login: String): ScmOrg {
        return client.get("orgs/$login").body<GitHubOrg>().asScmOrg
    }

    override suspend fun getUserByLogin(login: String): ScmUser {
        return client.get("users/$login").body<GitHubUser>().asScmUser
    }

    override suspend fun getPublicRepo(orgName: String, repoName: String): ScmRepository {
        return client.get("repos/$orgName/$repoName").body<GitHubRepo>().asScmRepository
    }

    /**
     * Returns a map of language name to bytes of code in that language.
     *
     * This is the GET response:
     *
     * ```
     * {
     *   "C": 78769,
     *   "C++": 108,
     *   "CMake": 100
     * }
     * ```
     */
    override suspend fun getPublicRepoLanguages(orgName: String, repoName: String): Map<String, Int> {
        return client.get("repos/$orgName/$repoName/languages").body()
    }

    override fun close() {
        client.close()
    }
}
