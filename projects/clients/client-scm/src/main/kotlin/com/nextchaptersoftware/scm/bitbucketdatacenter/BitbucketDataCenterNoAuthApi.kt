package com.nextchaptersoftware.scm.bitbucketdatacenter

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.scm.ScmNoAuthApi
import com.nextchaptersoftware.scm.ScmNoAuthHttpClientFactory.makeScmNoAuthHttpClient
import com.nextchaptersoftware.scm.github.models.AppConfig
import com.nextchaptersoftware.scm.models.HostAvailability
import com.nextchaptersoftware.scm.models.HostAvailabilityStatus
import com.nextchaptersoftware.scm.models.ScmOrg
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmUser
import io.ktor.client.plugins.retry
import io.ktor.client.plugins.timeout
import io.ktor.http.Url
import kotlin.time.Duration.Companion.seconds

internal class BitbucketDataCenterNoAuthApi(
    orgIds: Set<OrgId>?,
    private val baseApiUrl: Url,
) : ScmNoAuthApi, ScmNoAuthApi.WithHostAvailability {

    companion object {
        fun fromAuthority(orgIds: Set<OrgId>?, authority: String): BitbucketDataCenterNoAuthApi {
            return BitbucketDataCenterNoAuthApi(
                orgIds = orgIds,
                baseApiUrl = "https://$authority/rest/api/latest".asUrl,
            )
        }
    }

    private val client = makeScmNoAuthHttpClient(
        orgIds = orgIds,
        baseApiUrl = baseApiUrl,
    )

    override suspend fun getOrgByLogin(login: String): ScmOrg {
        TODO()
    }

    override suspend fun getUserByLogin(login: String): ScmUser {
        TODO()
    }

    override suspend fun getPublicRepo(orgName: String, repoName: String): ScmRepository {
        TODO()
    }

    override suspend fun getPublicRepoLanguages(orgName: String, repoName: String): Map<String, Int> {
        TODO()
    }

    override suspend fun getHostAvailability(): HostAvailabilityStatus {
        return HostAvailability.checkAvailability(baseApiUrl.host) {
            isEnterpriseHost()
        }
    }

    private suspend fun isEnterpriseHost(): Boolean {
        return client.bitbucketDataCenterApplication {
            timeout {
                requestTimeoutMillis = 4.seconds.inWholeMilliseconds
            }
            retry {
                maxRetries = 0
            }
        }.let { app ->
            app.displayName == "Bitbucket"
        }
    }

    override suspend fun completeAppManifest(code: String): AppConfig {
        error("Not applicable")
    }

    override fun close() {
        client.close()
    }
}
