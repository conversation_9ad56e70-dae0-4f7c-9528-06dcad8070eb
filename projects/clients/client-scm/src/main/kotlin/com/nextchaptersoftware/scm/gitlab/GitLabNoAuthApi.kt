package com.nextchaptersoftware.scm.gitlab

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.scm.ScmNoAuthApi
import com.nextchaptersoftware.scm.ScmNoAuthHttpClientFactory.makeScmNoAuthHttpClient
import com.nextchaptersoftware.scm.github.models.AppConfig
import com.nextchaptersoftware.scm.gitlab.models.GitLabOrg
import com.nextchaptersoftware.scm.gitlab.models.GitLabUser
import com.nextchaptersoftware.scm.models.HostAvailability
import com.nextchaptersoftware.scm.models.HostAvailabilityStatus
import com.nextchaptersoftware.scm.models.ScmOrg
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmUser
import io.ktor.client.call.body
import io.ktor.client.plugins.retry
import io.ktor.client.plugins.timeout
import io.ktor.client.request.get
import io.ktor.client.request.head
import io.ktor.client.request.parameter
import io.ktor.http.Url
import kotlin.time.Duration.Companion.seconds

internal class GitLabNoAuthApi(
    orgIds: Set<OrgId>?,
    private val baseApiUrl: Url,
) : ScmNoAuthApi, ScmNoAuthApi.WithHostAvailability {

    companion object {
        fun fromAuthority(orgIds: Set<OrgId>?, authority: String): GitLabNoAuthApi {
            return GitLabNoAuthApi(
                orgIds = orgIds,
                baseApiUrl = GitLabCommonApi.apiBaseUrl(authority),
            )
        }
    }

    private val client = makeScmNoAuthHttpClient(
        orgIds = orgIds,
        baseApiUrl = baseApiUrl,
    )

    override suspend fun getHostAvailability(): HostAvailabilityStatus {
        return HostAvailability.checkAvailability(baseApiUrl.host) {
            isEnterpriseHost()
        }
    }

    private suspend fun isEnterpriseHost(): Boolean {
        if (baseApiUrl.host.lowercase() == "gitlab.com") {
            return false
        }

        return client.head("broadcast_messages") {
            parameter("per_page", 1)
            timeout {
                requestTimeoutMillis = 4.seconds.inWholeMilliseconds
            }
            retry {
                maxRetries = 0
            }
        }.let { response ->
            response.headers.names().any { it.startsWith("x-gitlab-", ignoreCase = true) } || baseApiUrl.host.lowercase().contains("gitlab")
        }
    }

    override suspend fun completeAppManifest(code: String): AppConfig {
        error("Not applicable")
    }

    override suspend fun getOrgByLogin(login: String): ScmOrg {
        return client.get("groups/$login").body<GitLabOrg>().asScmOrg
    }

    override suspend fun getUserByLogin(login: String): ScmUser {
        return client.get("users") {
            parameter("username", login)
        }.body<GitLabUser>().asScmUser
    }

    override suspend fun getPublicRepo(orgName: String, repoName: String): ScmRepository {
        error("Not applicable")
    }

    override suspend fun getPublicRepoLanguages(orgName: String, repoName: String): Map<String, Int> {
        error("Not applicable")
    }

    override fun close() {
        client.close()
    }
}
