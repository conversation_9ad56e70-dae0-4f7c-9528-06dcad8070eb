package com.nextchaptersoftware.scm

import com.nextchaptersoftware.scm.github.models.AppConfig
import com.nextchaptersoftware.scm.models.HostAvailabilityStatus
import com.nextchaptersoftware.scm.models.ScmOrg
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmUser
import java.lang.AutoCloseable

interface ScmNoAuthApi : AutoCloseable {

    suspend fun getOrgByLogin(login: String): ScmOrg

    suspend fun getUserByLogin(login: String): ScmUser

    suspend fun getPublicRepo(orgName: String, repoName: String): ScmRepository

    suspend fun getPublicRepoLanguages(orgName: String, repoName: String): Map<String, Int>

    suspend fun completeAppManifest(code: String): AppConfig

    interface WithHostAvailability {
        suspend fun getHostAvailability(): HostAvailabilityStatus
    }
}
