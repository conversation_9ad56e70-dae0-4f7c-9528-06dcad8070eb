package com.nextchaptersoftware.scm.azuredevops

import com.nextchaptersoftware.scm.ScmNoAuthApi
import com.nextchaptersoftware.scm.github.models.AppConfig
import com.nextchaptersoftware.scm.models.ScmOrg
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmUser
import io.ktor.http.Url

@Suppress("UnusedPrivateMember")
internal class AzureDevOpsNoAuthApi(
    baseApiUrl: Url,
) : ScmNoAuthApi {

    override suspend fun getOrgByLogin(login: String): ScmOrg {
        error("Not applicable")
    }

    override suspend fun getUserByLogin(login: String): ScmUser {
        error("Not applicable")
    }

    override suspend fun getPublicRepo(orgName: String, repoName: String): ScmRepository {
        error("Not applicable")
    }

    override suspend fun getPublicRepoLanguages(orgName: String, repoName: String): Map<String, Int> {
        error("Not applicable")
    }

    override suspend fun completeAppManifest(code: String): AppConfig {
        error("Not applicable")
    }

    @Suppress("EmptyFunctionBlock")
    override fun close() {
    }
}
