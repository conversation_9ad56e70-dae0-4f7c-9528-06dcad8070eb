package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.analytics.AnalyticsAnswers
import com.nextchaptersoftware.analytics.AnalyticsCIReports
import com.nextchaptersoftware.analytics.AnalyticsDataResolution
import com.nextchaptersoftware.analytics.AnalyticsMcpInvocations
import com.nextchaptersoftware.analytics.AnalyticsMembers
import com.nextchaptersoftware.api.TimeSeriesApiDelegateInterface
import com.nextchaptersoftware.api.models.DataMemberLabels
import com.nextchaptersoftware.api.models.DataResolution
import com.nextchaptersoftware.api.models.DataTimeSeries
import com.nextchaptersoftware.api.models.converters.toDuration
import com.nextchaptersoftware.db.models.FeedbackType
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.asAgentApiModel
import com.nextchaptersoftware.db.stores.Stores.orgStore
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.models.orgId
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.server.routing.RoutingContext
import kotlin.time.Duration
import kotlin.time.DurationUnit
import kotlin.time.Instant
import kotlin.time.toDuration
import kotlinx.datetime.TimeZone
import org.openapitools.server.Resources

class TimeSeriesApiDelegateImpl(
    private val analyticsAnswers: AnalyticsAnswers = AnalyticsAnswers(),
    private val analyticsCIReports: AnalyticsCIReports = AnalyticsCIReports(scmConfig = ScmConfig.INSTANCE),
    private val analyticsMembers: AnalyticsMembers = AnalyticsMembers(),
    private val analyticsMcpInvocations: AnalyticsMcpInvocations = AnalyticsMcpInvocations(),
) : TimeSeriesApiDelegateInterface {

    override suspend fun timeSeriesAnswers(
        context: RoutingContext,
        input: Resources.timeSeriesAnswers,
    ): DataTimeSeries {
        val orgId = context.orgId
        val now = Instant.nowWithMicrosecondPrecision()
        val timeZone = parseTimeZone(input.timeZone)
        val dataResolution = calculateAnalyticsResolution(
            resolution = input.dataResolution,
            now = now,
            earliestDate = { analyticsAnswers.earliestAnswer(orgId) },
        )

        val (x: List<Instant>, series: Map<FeedbackType, List<Long>>) = analyticsAnswers.answerTimeSeriesByFeedback(
            orgId = orgId,
            resolution = dataResolution,
            since = input.dataDuration.toDuration?.let { now.minus(it) },
            until = now,
            timeZone = timeZone,
        )

        return DataTimeSeries(
            duration = input.dataDuration,
            resolution = dataResolution.apiDataResolution,
            timeZone = timeZone.id,
            x = x.map { it.toEpochMilliseconds().toDouble() },
            series = series.map { (k, v) ->
                k.apiFeedbackType.enumValue to v.map { it.toFloat() }
            }.toMap(),
        )
    }

    override suspend fun timeSeriesCIReports(
        context: RoutingContext,
        input: Resources.timeSeriesCIReports,
    ): DataTimeSeries {
        val orgId = context.orgId
        val now = Instant.nowWithMicrosecondPrecision()
        val timeZone = parseTimeZone(input.timeZone)

        analyticsCIReports.expectCIInstallation(orgId)

        val dataResolution = calculateAnalyticsResolution(
            resolution = input.dataResolution,
            now = now,
            earliestDate = { analyticsCIReports.earliestReport(orgId) },
        )

        val (x: List<Instant>, series: Map<FeedbackType, List<Long>>) = analyticsCIReports.ciReportTimeSeriesByFeedback(
            orgId = orgId,
            resolution = dataResolution,
            since = input.dataDuration.toDuration?.let { now.minus(it) },
            until = now,
            timeZone = timeZone,
        )

        return DataTimeSeries(
            duration = input.dataDuration,
            resolution = dataResolution.apiDataResolution,
            timeZone = timeZone.id,
            x = x.map { it.toEpochMilliseconds().toDouble() },
            series = series.map { (k, v) ->
                k.apiFeedbackType.enumValue to v.map { it.toFloat() }
            }.toMap(),
        )
    }

    override suspend fun timeSeriesMcpInvocations(
        context: RoutingContext,
        input: Resources.timeSeriesMcpInvocations,
    ): DataTimeSeries {
        val orgId = context.orgId
        val now = Instant.nowWithMicrosecondPrecision()
        val timeZone = parseTimeZone(input.timeZone)
        val dataResolution = calculateAnalyticsResolution(
            resolution = input.dataResolution,
            now = now,
            earliestDate = { analyticsMcpInvocations.earliestInvocation(orgId) },
        )

        val (x: List<Instant>, series: Map<ProductAgentType, List<Long>>) = analyticsMcpInvocations.invocationTimeSeriesByAgent(
            orgId = orgId,
            resolution = dataResolution,
            since = input.dataDuration.toDuration?.let { now.minus(it) },
            until = now,
            timeZone = timeZone,
        )

        return DataTimeSeries(
            duration = input.dataDuration,
            resolution = dataResolution.apiDataResolution,
            timeZone = timeZone.id,
            x = x.map { it.toEpochMilliseconds().toDouble() },
            series = series.map { (k, v) ->
                k.asAgentApiModel().enumValue to v.map { it.toFloat() }
            }.toMap(),
        )
    }

    override suspend fun timeSeriesMembers(context: RoutingContext, input: Resources.timeSeriesMembers): DataTimeSeries {
        val orgId = context.orgId
        val now = Instant.nowWithMicrosecondPrecision()
        val timeZone = parseTimeZone(input.timeZone)
        val dataResolution = calculateAnalyticsResolution(
            resolution = input.dataResolution,
            now = now,
            earliestDate = { orgStore.findById(orgId = context.orgId).required().enabledAt },
        )

        val (x: List<Instant>, series: Map<DataMemberLabels, List<Long>>) = analyticsMembers.membersTimeSeries(
            orgId = orgId,
            resolution = dataResolution,
            since = input.dataDuration.toDuration?.let { now.minus(it) },
            until = now,
            timeZone = timeZone,
        )

        return DataTimeSeries(
            duration = input.dataDuration,
            resolution = dataResolution.apiDataResolution,
            timeZone = timeZone.id,
            x = x.map { it.toEpochMilliseconds().toDouble() },
            series = series.map { (k, v) ->
                k.enumValue to v.map { it.toFloat() }
            }.toMap(),
        )
    }

    private fun parseTimeZone(input: String): TimeZone {
        return runSuspendCatching {
            TimeZone.of(input)
        }.getOrElse {
            TimeZone.currentSystemDefault()
        }
    }

    private suspend fun calculateAnalyticsResolution(
        resolution: DataResolution,
        now: Instant,
        earliestDate: suspend () -> Instant?,
    ): AnalyticsDataResolution {
        return when (resolution) {
            DataResolution.month -> AnalyticsDataResolution.Month
            DataResolution.week -> AnalyticsDataResolution.Week
            DataResolution.day -> AnalyticsDataResolution.Day
            DataResolution.default -> getDefaultAnalyticsResolution(now, earliestDate())
        }
    }

    private fun getDefaultAnalyticsResolution(
        now: Instant,
        earliestDate: Instant?,
    ): AnalyticsDataResolution {
        if (earliestDate == null) return AnalyticsDataResolution.Day

        val age = now - earliestDate
        return when {
            age < 3.weeks -> AnalyticsDataResolution.Day
            age < 3.months -> AnalyticsDataResolution.Week
            else -> AnalyticsDataResolution.Month
        }
    }

    private val AnalyticsDataResolution.apiDataResolution: DataResolution
        get() = when (this) {
            AnalyticsDataResolution.Day -> DataResolution.day
            AnalyticsDataResolution.Week -> DataResolution.week
            AnalyticsDataResolution.Month -> DataResolution.month
        }

    @Suppress("MagicNumber")
    private inline val Int.weeks: Duration get() = toDuration(DurationUnit.DAYS).times(7)

    @Suppress("MagicNumber")
    private inline val Int.months: Duration get() = toDuration(DurationUnit.DAYS).times(30)
}
