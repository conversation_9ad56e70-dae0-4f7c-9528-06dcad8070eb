@file:Suppress("TooManyFunctions")

package com.nextchaptersoftware.adminwebservice.plugins

import com.nextchaptersoftware.adminwebservice.adminweb.AdminNavigationTab
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalInferenceId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalInstallation
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalInstallationId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalOrgId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.orgId
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.api.TampermonkeyApi.triageRequestEventFromJobURL
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.alert
import com.nextchaptersoftware.adminwebservice.adminweb.component.autoResizeIframe
import com.nextchaptersoftware.adminwebservice.adminweb.page.AWSAccountInstallationPage.deleteAWSAccountInstallation
import com.nextchaptersoftware.adminwebservice.adminweb.page.AWSAccountInstallationPage.renderAWSAccountInstallationPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.AWSAccountInstallationPage.updateAWSAccountInstallation
import com.nextchaptersoftware.adminwebservice.adminweb.page.AWSAccountInstallationsPage.createDemoAWSAccountInstallation
import com.nextchaptersoftware.adminwebservice.adminweb.page.AWSAccountInstallationsPage.renderAWSAccountInstallationsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsAnswersPage.renderAnalyticsAnswersPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsCiPage.renderAnalyticsCiPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsDataPipelinePage.renderAnalyticsDataPipelinePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsMembersPage.downloadSessionActions
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsMembersPage.renderAnalyticsMembersPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnalyticsOnboardingPage.renderAnalyticsOnboardingPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.AnswerPreferencesPage.renderAnswerPreferencesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.AsanaInstallationPage.forceReIngestAsanaProjects
import com.nextchaptersoftware.adminwebservice.adminweb.page.AsanaInstallationPage.renderAsanaWorkspacePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.AsanaInstallationPage.renderAsanaWorkspacesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiBuildPage.buildGetLogs
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiBuildPage.buildJobGetAnnotations
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiBuildPage.buildJobGetFocusLogs
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiBuildPage.buildJobGetLogs
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiBuildPage.ciFetch
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiBuildPage.renderCiBuildPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiBuildsPage.renderCiBuildsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiProviderPage.ciScmModeUpdate
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiProviderPage.getCiProviderToken
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiProviderPage.renderCiProviderPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriageEphemeralReRunPage.renderCiTriageEphemeralReRunPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriageEphemeralReRunPage.triageEphemeralReRun
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriagePage.renderCiTriagePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriagePage.triageDelete
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriagePage.triageMuteToggle
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriagePage.triageRefresh
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriagePage.triageRender
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriagePage.triageRerun
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriagePage.triageReview
import com.nextchaptersoftware.adminwebservice.adminweb.page.CiTriagesPage.renderCiTriagesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.CodaInstallationPage.extractCoda
import com.nextchaptersoftware.adminwebservice.adminweb.page.CodaInstallationPage.getCodaApiToken
import com.nextchaptersoftware.adminwebservice.adminweb.page.CodaInstallationPage.listPagesForDoc
import com.nextchaptersoftware.adminwebservice.adminweb.page.CodaInstallationPage.reingestCoda
import com.nextchaptersoftware.adminwebservice.adminweb.page.CodaInstallationPage.renderCodaInstallationPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.CodaPagePage.renderCodaPagePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConfigsPage.exportConfigs
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConfigsPage.getFullConfig
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConfigsPage.renderConfigsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConfluenceSitePage.clearNextUrls
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConfluenceSitePage.getConfluenceApiToken
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConfluenceSitePage.getConfluencePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConfluenceSitePage.getSpaces
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConfluenceSitePage.ingestPages
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConfluenceSitePage.reingestConfluenceSite
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConfluenceSitePage.reingestConfluenceSiteFromCreatedAt
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConfluenceSitePage.renderConfluenceSitePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConfluenceSitePage.syncSpaceContent
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConfluenceSitePage.toggleIngestPersonalSpaces
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConfluenceSitePage.upsertConfluenceMembers
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConfluenceSitesPage.renderConfluenceSitesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConversationAnalysisDetailsPage.createExecutiveSummary
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConversationAnalysisDetailsPage.downloadFeedbackAnalysis
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConversationAnalysisDetailsPage.editAnalysis
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConversationAnalysisDetailsPage.renderConversationAnalysisDetailsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConversationAnalysisDetailsPage.triggerClustering
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConversationAnalysisPage.analyzeFeedback
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConversationAnalysisPage.deleteAnalysis
import com.nextchaptersoftware.adminwebservice.adminweb.page.ConversationAnalysisPage.renderConversationAnalysisPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.DatabaseDeletionsPage.columnsForModel
import com.nextchaptersoftware.adminwebservice.adminweb.page.DatabaseDeletionsPage.events
import com.nextchaptersoftware.adminwebservice.adminweb.page.DatabaseDeletionsPage.renderDatabaseDeletionsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.DatabaseDeletionsPage.startDelete
import com.nextchaptersoftware.adminwebservice.adminweb.page.DocumentPage.renderDocumentPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.DocumentRetrievalPage.renderDocumentRetrievalPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.DocumentRetrievalPage.retrieveDocuments
import com.nextchaptersoftware.adminwebservice.adminweb.page.EvalsPage.renderEvalsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.FeaturesPage.renderConfigPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.FeaturesPage.updateGlobalCapability
import com.nextchaptersoftware.adminwebservice.adminweb.page.FeaturesPage.updateGlobalOrgCapability
import com.nextchaptersoftware.adminwebservice.adminweb.page.FeaturesPage.updateGlobalPersonCapability
import com.nextchaptersoftware.adminwebservice.adminweb.page.FileUploadUsagePage.renderFileUploadUsagePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.FunctionRetrievalPage.renderFunctionRetrievalPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.GlobalRegressionInferencesPage.removeInferenceFromGlobalList
import com.nextchaptersoftware.adminwebservice.adminweb.page.GlobalRegressionInferencesPage.renderGlobalRegressionInferencesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.GoogleDriveInstallationPage.getAccessibleFiles
import com.nextchaptersoftware.adminwebservice.adminweb.page.GoogleDriveInstallationPage.getFile
import com.nextchaptersoftware.adminwebservice.adminweb.page.GoogleDriveInstallationPage.getSharedDrives
import com.nextchaptersoftware.adminwebservice.adminweb.page.GoogleDriveInstallationPage.reingestGoogle
import com.nextchaptersoftware.adminwebservice.adminweb.page.GoogleDriveInstallationPage.renderGoogleDriveInstallationPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.GoogleDriveInstallationsPage.renderGoogleDriveInstallationsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.GrowthPage.renderGrowthPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.HomePage.renderHomePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.IdeFeatureUsagePage.renderIdeFeatureUsagePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.IdentityPage.getAccessToken
import com.nextchaptersoftware.adminwebservice.adminweb.page.IdentityPage.promptIdentityToReconnect
import com.nextchaptersoftware.adminwebservice.adminweb.page.IdentityPage.rawScmAccounts
import com.nextchaptersoftware.adminwebservice.adminweb.page.IdentityPage.rawScmUser
import com.nextchaptersoftware.adminwebservice.adminweb.page.IdentityPage.refreshProfileForIdentity
import com.nextchaptersoftware.adminwebservice.adminweb.page.IdentityPage.renderIdentityPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.IdentityPage.revokeIdentity
import com.nextchaptersoftware.adminwebservice.adminweb.page.IdentityPage.unlinkPersonFromIdentity
import com.nextchaptersoftware.adminwebservice.adminweb.page.ImpersonatePage.renderImpersonatePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.ImpersonatePage.startImpersonating
import com.nextchaptersoftware.adminwebservice.adminweb.page.ImpersonatePage.stopImpersonating
import com.nextchaptersoftware.adminwebservice.adminweb.page.IncognitoPreferencesPage.renderIncognitoPreferencesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.InferencePage.createOrUpdateIsQuestionAnswerRegressionTest
import com.nextchaptersoftware.adminwebservice.adminweb.page.InferencePage.createOrUpdateIsQuestionRegressionTest
import com.nextchaptersoftware.adminwebservice.adminweb.page.InferencePage.renderInferenceExamplePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.InferencePage.updateInferenceFeedback
import com.nextchaptersoftware.adminwebservice.adminweb.page.InferencePage.updateInferenceLabel
import com.nextchaptersoftware.adminwebservice.adminweb.page.InferencePage.updateInferenceResponse
import com.nextchaptersoftware.adminwebservice.adminweb.page.InferencePage.updateIsGlobalRegressionInference
import com.nextchaptersoftware.adminwebservice.adminweb.page.InferencesPage.moveToGolden
import com.nextchaptersoftware.adminwebservice.adminweb.page.InferencesPage.moveToUnlabeled
import com.nextchaptersoftware.adminwebservice.adminweb.page.InferencesPage.moveToValidation
import com.nextchaptersoftware.adminwebservice.adminweb.page.InferencesPage.renderInferencesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.InstallationRequestsPage.renderInstallationRequestsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.InstallationsPage.renderInstallationsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.IntegrationNewPage.createNewApp
import com.nextchaptersoftware.adminwebservice.adminweb.page.IntegrationNewPage.renderNewIntegrationPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.IntegrationPage.deleteScmApp
import com.nextchaptersoftware.adminwebservice.adminweb.page.IntegrationPage.renderIntegrationPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.IntegrationPage.updatePinnedIdentityId
import com.nextchaptersoftware.adminwebservice.adminweb.page.IsQuestionAnswerTemplateRegressionTestingPage.deleteIsQuestionAnswerRegressionTest
import com.nextchaptersoftware.adminwebservice.adminweb.page.IsQuestionAnswerTemplateRegressionTestingPage.renderIsQuestionAnswerTemplateRegressionTestingPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.IsQuestionAnswerTemplateRegressionTestingPage.runIsQuestionAnswerRegressionTest
import com.nextchaptersoftware.adminwebservice.adminweb.page.IsQuestionAnswerTemplateRegressionTestingPage.runSingleIsQuestionAnswerRegressionTest
import com.nextchaptersoftware.adminwebservice.adminweb.page.IsQuestionTemplateRegressionTestingPage.addIsQuestionRegressionTest
import com.nextchaptersoftware.adminwebservice.adminweb.page.IsQuestionTemplateRegressionTestingPage.deleteIsQuestionRegressionTest
import com.nextchaptersoftware.adminwebservice.adminweb.page.IsQuestionTemplateRegressionTestingPage.renderIsQuestionTemplateRegressionTestingPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.IsQuestionTemplateRegressionTestingPage.runIsQuestionRegressionTest
import com.nextchaptersoftware.adminwebservice.adminweb.page.IsQuestionTemplateRegressionTestingPage.runSingleIsQuestionRegressionTest
import com.nextchaptersoftware.adminwebservice.adminweb.page.IsQuestionTemplateRegressionTestingPage.updateIsQuestionRegressionTest
import com.nextchaptersoftware.adminwebservice.adminweb.page.JiraIssuePage.renderJiraIssuePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.JiraSitePage.getJiraApiToken
import com.nextchaptersoftware.adminwebservice.adminweb.page.JiraSitePage.getProjects
import com.nextchaptersoftware.adminwebservice.adminweb.page.JiraSitePage.reingestJiraSite
import com.nextchaptersoftware.adminwebservice.adminweb.page.JiraSitePage.renderJiraSitePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.JiraSitesPage.renderJiraSitesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.LinearIngestionsPage.renderLinearIngestionsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.LinearOrganizationPage.renderLinearOrganizationPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.LinearOrganizationsPage.renderLinearOrganizationsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.LinearTeamIngestionsPage.reembedLinearTeam
import com.nextchaptersoftware.adminwebservice.adminweb.page.LinearTeamIngestionsPage.reingestLinearTeam
import com.nextchaptersoftware.adminwebservice.adminweb.page.LinearTeamIngestionsPage.renderLinearTeamIngestionsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.LinearTeamPage.renderLinearTeamPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.LinearTeamsPage.renderLinearTeamsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.LinkRetrievalPage.renderLinkRetrievalPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.LinkRetrievalPage.retrieveLinkedDocuments
import com.nextchaptersoftware.adminwebservice.adminweb.page.LoginPage.handleLoginCallback
import com.nextchaptersoftware.adminwebservice.adminweb.page.LoginPage.handlePasswordLogin
import com.nextchaptersoftware.adminwebservice.adminweb.page.LoginPage.renderLoginPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.MLRouter.deleteMLRouter
import com.nextchaptersoftware.adminwebservice.adminweb.page.MLRouter.renderMLRouterPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.MLRouter.updateMLRouter
import com.nextchaptersoftware.adminwebservice.adminweb.page.MLRouters.createNewMLRouter
import com.nextchaptersoftware.adminwebservice.adminweb.page.MLRouters.renderMLRoutersPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.MLTemplatePage.renderMLTemplatePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.MLTemplatePage.updateMLTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.page.MLTemplatesPage.cloneMLTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.page.MLTemplatesPage.deleteMLTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.page.MLTemplatesPage.exportAllGlobalMLTemplates
import com.nextchaptersoftware.adminwebservice.adminweb.page.MLTemplatesPage.exportMLTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.page.MLTemplatesPage.importMLTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.page.MLTemplatesPage.makeMLTemplateDemo
import com.nextchaptersoftware.adminwebservice.adminweb.page.MLTemplatesPage.makeMLTemplateGlobal
import com.nextchaptersoftware.adminwebservice.adminweb.page.MLTemplatesPage.renderMLTemplatesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.MacNotificationPreferencesPage.renderMacNotificationPreferencesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.McpInferencesPage.renderMcpInferencesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.McpToolsPage.handleMcpToolEdit
import com.nextchaptersoftware.adminwebservice.adminweb.page.McpToolsPage.renderMcpToolsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.alignMemberForPerson
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.alignOrgMemberForPerson
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.archiveLowRelevanceThreadsForMember
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.clearMemberInstallationSuppressions
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.clearSenderInvites
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.clearUserConnectPromptHistory
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.createUserEngagement
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.disassociateIdentityPerson
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.disassociatePrimaryMember
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.getMcpToken
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.promptToReconnect
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.refreshEmailForMemberIfNeeded
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.removeFromTeamInviteFollowUpEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.renderMemberPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.resetQuestionsAsked
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendConnectIntegrationsReminderEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendFirstQuestionReminderEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendInviteEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendInviteTeamReminderEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendLicenseRequestedEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendNthMemberStartEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendPitchEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendProcessingCompleteEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendRepoFollowupEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendSuggestedPeersInviteeEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendTeamInviteEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendTeamInviteFollowUpEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendThreadInviteEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendThreadInviteJoinEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendTrialOneDayEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendTrialOneWeekEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendTrialStartEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendTrialSurveyEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendTrialThreeDaysEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendTrialTwoWeeksEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.sendUserAddedConfirmationEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.startOnboardedCampaign
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.toggleIgnoreThreads
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.updateOrgMemberOnboardingStates
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.updateOrgMemberRole
import com.nextchaptersoftware.adminwebservice.adminweb.page.MemberPage.updateProviderStates
import com.nextchaptersoftware.adminwebservice.adminweb.page.MembersPage.renderMembersPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.MessageFeedbackPage.downloadFeedback
import com.nextchaptersoftware.adminwebservice.adminweb.page.MessageFeedbackPage.renderMessageFeedbackPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.MessagePage.renderMessagePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.MessagesPage.renderMessagesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.MetricsPage.renderMetricsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.MetricsTriagePage.renderMetricsTriagePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.ModelsPage.renderModelsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.NotionInstallationPage.clearNotionNextCursor
import com.nextchaptersoftware.adminwebservice.adminweb.page.NotionInstallationPage.getBlock
import com.nextchaptersoftware.adminwebservice.adminweb.page.NotionInstallationPage.getBlockAndChildren
import com.nextchaptersoftware.adminwebservice.adminweb.page.NotionInstallationPage.getBlockChildren
import com.nextchaptersoftware.adminwebservice.adminweb.page.NotionInstallationPage.getNotionApiToken
import com.nextchaptersoftware.adminwebservice.adminweb.page.NotionInstallationPage.getNotionDatabase
import com.nextchaptersoftware.adminwebservice.adminweb.page.NotionInstallationPage.getNotionPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.NotionInstallationPage.getNotionUser
import com.nextchaptersoftware.adminwebservice.adminweb.page.NotionInstallationPage.ingestNotionPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.NotionInstallationPage.queryNotionDatabase
import com.nextchaptersoftware.adminwebservice.adminweb.page.NotionInstallationPage.reingestNotion
import com.nextchaptersoftware.adminwebservice.adminweb.page.NotionInstallationPage.renderNotionInstallationPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.NotionInstallationPage.searchNotionDatabases
import com.nextchaptersoftware.adminwebservice.adminweb.page.NotionInstallationPage.searchNotionPages
import com.nextchaptersoftware.adminwebservice.adminweb.page.NotionInstallationsPage.renderNotionInstallationsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.backfillEngagementMetrics
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.backfillGoogleConnectingMember
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.backfillGrowthCIMetrics
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.backfillGrowthMetrics
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.backfillGrowthSlackMetrics
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.backfillLastQuestionAskedAt
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.clearEngagementMetrics
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.clearGrowthCIMetrics
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.clearGrowthMetrics
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.clearGrowthSlackMetrics
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.deduplicateFeedbackResponses
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.downloadBusinessPlanOrgsCsv
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.executeMigrator
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.mergeDuplicatePeople
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.migrateDeferralDates
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.migrateHackOrgMember
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.migrateMembers
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.refreshBotProfile
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.refreshProfilesMissingPrimaryEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.reingestAllConfluenceSites
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.reingestAllGitHubIssues
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.reingestAllJiraSites
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.renderOperationsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.resetDataIngestionForTeamsNeedingReprocessing
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.retryErroredNotionPageIngestions
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.setAuditLogSearchableText
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.setLastSyncedForJiraIngestions
import com.nextchaptersoftware.adminwebservice.adminweb.page.OperationsPage.toggleIngestion
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgAnalyticsPage.renderOrgAnalyticsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgAnswersPage.renderOrgAnswersPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgInstallationsPage.renderOrgInstallationsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgMachineLearningPage.regenerateSampleQuestionsForOrg
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgMachineLearningPage.renderOrgMachineLearningPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgMachineLearningPage.updateOrgMLSettings
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgMembersPage.renderOrgMembersPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.clearLastExtendedOn
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.deleteOrg
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.deleteOrgPermanently
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.deleteOrgProxy
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.disableOrg
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.downgradeCapabilities
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.enableOrg
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.generateUserActivityReport
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.getActiveUsersInRange
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.getOrgActivity
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.getOrgStats
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.getVectorCounts
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.regenerateSocialNetwork
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.renderOrgPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.resetBilling
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.scheduleSeatDowngrade
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.scheduleSeatUpgrade
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.scheduleSubscriptionCancellation
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.sendAssignLicensesEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.sendBusinessPlanConfirmationEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.setQuarterlyStartDate
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.undeleteOrg
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.updateOrgCapability
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.updateOrgOnboardingStates
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.updateOrgProxy
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.updateOrgSettings
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.updatePlan
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.updateSlackSettings
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.updateSubscribedReleaseChannel
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgPage.updateTrialEndDate
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgProductFeedbackPage.deleteProductFeedbackResponse
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgProductFeedbackPage.downloadOrgProductFeedback
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgProductFeedbackPage.renderOrgProductFeedbackPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgProductFeedbackPage.resetProductFeedbackDeferral
import com.nextchaptersoftware.adminwebservice.adminweb.page.OrgsPage.renderOrgsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.PeoplePage.renderPeoplePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonMcpToolOverridesPage.handlePersonMcpToolOverridesEdit
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonMcpToolOverridesPage.renderPersonMcpToolOverridesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.deauthorizePerson
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.deleteEmailEvent
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.deletePerson
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.refreshProfileForPerson
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.removeFromInactiveFollowupLists
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.renderPersonPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.resetHubOnboardingStates
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.resetIntellijOnboardingStates
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.resetPersonPreferences
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.resetVSCodeOnboardingStates
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.sendWelcomeEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.updateOnboardingStates
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.updatePersonCapability
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.updatePersonEmailPreferences
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.updatePersonSubscribedReleaseChannel
import com.nextchaptersoftware.adminwebservice.adminweb.page.PersonPage.updatePrimaryEmail
import com.nextchaptersoftware.adminwebservice.adminweb.page.ProductFeedbackPage.renderProductFeedbackPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestCommitPage.renderPullRequestCommitPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestCommitsPage.renderPullRequestCommitsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestDiffPage.renderPullRequestDiffPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestPage.reindexForPullRequest
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestPage.reingestForPullRequest
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestPage.renderPullRequestPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestsPage.renderPullRequests
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestsParams.optionalPullRequest
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestsRawPage.renderPullRequestRawDiffPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestsRawPage.renderPullRequestRawFilesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestsRawPage.renderPullRequestRawLatestPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestsRawPage.renderPullRequestRawPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestsRawPage.renderPullRequestRawReviewCommentsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestsRawPage.renderPullRequestRawReviewsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.PullRequestsRawPage.renderPullRequestsRawPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.QueuesPage.flushDLQ
import com.nextchaptersoftware.adminwebservice.adminweb.page.QueuesPage.renderQueuesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.RegressionEvalDetailsPage.renderRegressionEvalDetailsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoBrowsePage.renderRepoBrowsePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoCommitPage.renderRepoCommitPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoCommitsPage.renderRepoCommitsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoDiffPage.renderRepoDiffPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.clearCalculatedSourcePointsForRepo
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.getRepoCloneUrl
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.getRepoHeadCommit
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.reingestIngestionIncompletePullRequests
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.renderRepoPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.rerunBulkGitHubIssueIngest
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.toggleIngestionCompleteFlag
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.toggleIsCiEnabledFlag
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.toggleSlackIngestionFlag
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.togglebulkingestFlag
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.triggerRepoBulkPrIngestion
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.triggerRepoPrIngestion
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.triggerRepoPullRequestIngestion
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.unmarkCodeIngestForRepo
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.unmarkPullRequestIngestForRepo
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoPage.unmarkTopicIngestForRepo
import com.nextchaptersoftware.adminwebservice.adminweb.page.RepoSearchPage.renderRepoSearchPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.ReposPage.renderReposPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SamlIdpMetadataPage.deleteSamlIdpMetadata
import com.nextchaptersoftware.adminwebservice.adminweb.page.SamlIdpMetadataPage.renderSamlIdpMetadataPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SamlIdpMetadataPage.updateSamlIdpMetadataSettings
import com.nextchaptersoftware.adminwebservice.adminweb.page.SampleQuestionPage.regenerateSampleQuestionsForMember
import com.nextchaptersoftware.adminwebservice.adminweb.page.SampleQuestionPage.renderSampleQuestionPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SampleQuestionsGeneratorPage.generateSampleQuestions
import com.nextchaptersoftware.adminwebservice.adminweb.page.SampleQuestionsGeneratorPage.renderSampleQuestionGeneratorPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmAppsPage.renderScmAppsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmInstallationPage.installationSuspend
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmInstallationPage.installationUninstall
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmInstallationPage.installationUnsuspend
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmInstallationPage.renderInstallationPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.archiveLowRelevanceThreads
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.archivePullRequests
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.backfillSourceMarkArchived
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.buildMemberAssociations
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.clearMemberAssociations
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.deleteTeam
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.deleteTeamPermanently
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.getApiToken
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.getRateLimit
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.pausePRIngestion
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.refreshScmResources
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.regenerateRecommendedTopicExperts
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.renderScmTeamPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.resetDataIngestion
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.runBulkPRIngestionForAllRepos
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.runEmbeddingExperiment
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.runLinearIngestionForAllTeams
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.setInitialIngestionCompletedAtForAllIntegrations
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.undeleteTeam
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.uninstallScmOrg
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.unmarkPullRequestIngestForTeam
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.unmarkTopicIngestForTeam
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.unpausePRIngestion
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamPage.updateTeamSettings
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamsPage.renderScmTeamsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.ScmTeamsParam.optionalScmTeamId
import com.nextchaptersoftware.adminwebservice.adminweb.page.SearchPage.renderSearchPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SemanticSearchPage.renderSemanticSearchPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SingleSignOnPage.createDomain
import com.nextchaptersoftware.adminwebservice.adminweb.page.SingleSignOnPage.deleteDomain
import com.nextchaptersoftware.adminwebservice.adminweb.page.SingleSignOnPage.renderSingleSignOnPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SingleSignOnPage.unverifyDomain
import com.nextchaptersoftware.adminwebservice.adminweb.page.SingleSignOnPage.verifyDomain
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackAutoAnswerDetailsPage.addToRegressionDataset
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackAutoAnswerDetailsPage.renderSlackAutoAnswerDetailsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackAutoAnswerPage.renderSlackAutoAnswerPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackChannelIngestionsPage.renderSlackChannelIngestionsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackChannelMembersPage.renderSlackChannelMembersPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackChannelPage.renderSlackChannelPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackChannelPreferencesPage.renderSlackChannelPreferencesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackChannelsPage.renderSlackChannelsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackConnectConversionPage.renderSlackConnectConversionPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackIngestionPage.renderSlackIngestionPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackIngestionsPage.renderSlackIngestionsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackInstallationPage.renderSlackInstallationPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackInstallationPage.runSlackIngestionForAllChannels
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackTeamPage.discardSlackCommandScopes
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackTeamPage.discardSlackPrivateUserScopes
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackTeamPage.getAuthedUserToken
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackTeamPage.getBotToken
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackTeamPage.renderSlackTeamPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SlackTeamsPage.renderSlackTeamsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SocialNetworkPage.renderSocialNetworkPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SourceMarkPage.renderSourceMarkPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.SourceMarksPage.renderSourceMarksPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.StackOverflowInstallationPage.getQuestions
import com.nextchaptersoftware.adminwebservice.adminweb.page.StackOverflowInstallationPage.reingestStackOverflow
import com.nextchaptersoftware.adminwebservice.adminweb.page.StackOverflowInstallationPage.renderStackOverflowInstallationPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.StackOverflowInstallationsPage.renderStackOverflowInstallationsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.TemplateRegressionTestDetailsPage.renderTemplateRegressionTestDetailsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.TemplateRegressionTestingPage.deleteRegressionTest
import com.nextchaptersoftware.adminwebservice.adminweb.page.TemplateRegressionTestingPage.renderTemplateRegressionTestingPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.TemplateRegressionTestingPage.runRegressionTest
import com.nextchaptersoftware.adminwebservice.adminweb.page.ThreadPage.deleteThread
import com.nextchaptersoftware.adminwebservice.adminweb.page.ThreadPage.deleteThreadTopics
import com.nextchaptersoftware.adminwebservice.adminweb.page.ThreadPage.generateThreadTopics
import com.nextchaptersoftware.adminwebservice.adminweb.page.ThreadPage.reindexThread
import com.nextchaptersoftware.adminwebservice.adminweb.page.ThreadPage.renderThreadPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.ThreadPage.summarizeThread
import com.nextchaptersoftware.adminwebservice.adminweb.page.ThreadsPage.renderThreadsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.TopicPage.renderTopicPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.TopicPage.triggerTopicExpertMapping
import com.nextchaptersoftware.adminwebservice.adminweb.page.TopicPage.triggerTopicMapping
import com.nextchaptersoftware.adminwebservice.adminweb.page.TopicPage.updateDescription
import com.nextchaptersoftware.adminwebservice.adminweb.page.TopicsPage.approveExistingTopic
import com.nextchaptersoftware.adminwebservice.adminweb.page.TopicsPage.renderTopicsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.UnblockedInstallationPage.renderUnblockedInstallationPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.UnreadsPage.deleteThreadUnread
import com.nextchaptersoftware.adminwebservice.adminweb.page.UnreadsPage.renderUnreadsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.VectorsPage.renderVectorsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.VersionPage.downloadInstaller
import com.nextchaptersoftware.adminwebservice.adminweb.page.VersionPage.downloadJetbrains
import com.nextchaptersoftware.adminwebservice.adminweb.page.VersionPage.makeVersionObsolete
import com.nextchaptersoftware.adminwebservice.adminweb.page.VersionPage.renderVersionPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.VersionPage.updateReleaseChannels
import com.nextchaptersoftware.adminwebservice.adminweb.page.VersionPage.updateVersionDescription
import com.nextchaptersoftware.adminwebservice.adminweb.page.VersionsPage.ingestNewBuild
import com.nextchaptersoftware.adminwebservice.adminweb.page.VersionsPage.renderVersionsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.VersionsPage.updateSubscribedReleaseChannelForOrg
import com.nextchaptersoftware.adminwebservice.adminweb.page.VersionsPage.updateSubscribedReleaseChannelForPerson
import com.nextchaptersoftware.adminwebservice.adminweb.page.VisualizePage.renderVisualizePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.WebIngestionPage.addWebIngestionSite
import com.nextchaptersoftware.adminwebservice.adminweb.page.WebIngestionPage.dropExcludedUrlsForSite
import com.nextchaptersoftware.adminwebservice.adminweb.page.WebIngestionPage.reingestSite
import com.nextchaptersoftware.adminwebservice.adminweb.page.WebIngestionPage.reingestSites
import com.nextchaptersoftware.adminwebservice.adminweb.page.WebIngestionPage.renderWebIngestionInstallationsPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.WebIngestionPage.renderWebIngestionSitePage
import com.nextchaptersoftware.adminwebservice.adminweb.page.WebIngestionPage.renderWebIngestionSitesPage
import com.nextchaptersoftware.adminwebservice.adminweb.page.WebIngestionPage.toggleDisableSite
import com.nextchaptersoftware.adminwebservice.adminweb.page.updateIgnoredTitleTags
import com.nextchaptersoftware.adminwebservice.adminweb.renderHeader
import com.nextchaptersoftware.adminwebservice.adminweb.renderNavbar
import com.nextchaptersoftware.adminwebservice.adminweb.template.BlankTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.api.HealthApiDelegateImpl
import com.nextchaptersoftware.adminwebservice.auth.AdminAuthenticationProvider
import com.nextchaptersoftware.adminwebservice.auth.AdminRoutingContextExtensions.getAdminIdentity
import com.nextchaptersoftware.adminwebservice.auth.google.GoogleVerifier
import com.nextchaptersoftware.adminwebservice.auth.password.PasswordVerifier
import com.nextchaptersoftware.adminwebservice.config.AdminConfig
import com.nextchaptersoftware.adminwebservice.migration.Migrator
import com.nextchaptersoftware.adminwebservice.migration.SchemaMigrator
import com.nextchaptersoftware.adminwebservice.migration.SocialNetworkMigrator.backfillSocialNetworks
import com.nextchaptersoftware.api.HealthApi
import com.nextchaptersoftware.atlassian.api.AtlassianDataCenterAuthProvider
import com.nextchaptersoftware.atlassian.services.AtlassianTokenProvider
import com.nextchaptersoftware.billing.events.queue.enqueue.BillingEventEnqueueService
import com.nextchaptersoftware.billing.services.BillingService
import com.nextchaptersoftware.ci.CILicenseService
import com.nextchaptersoftware.ci.CIProjectApiFactory
import com.nextchaptersoftware.ci.enqueue.TriageEventEnqueueService
import com.nextchaptersoftware.ci.logging.LogFocusService
import com.nextchaptersoftware.clientconfig.ClientConfigService
import com.nextchaptersoftware.coda.api.CodaApiProvider
import com.nextchaptersoftware.coda.data.services.CodaPagePersistenceService
import com.nextchaptersoftware.coda.events.queue.enqueue.CodaEventEnqueueService
import com.nextchaptersoftware.coda.services.CodaTokenProvider
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.confluence.api.ConfluenceCloudApiProvider
import com.nextchaptersoftware.confluence.api.ConfluenceDataCenterApiProvider
import com.nextchaptersoftware.confluence.enqueue.ConfluenceEventEnqueueService
import com.nextchaptersoftware.conversationanalysis.ConversationAnalysisService
import com.nextchaptersoftware.data.preset.DataSourcePresetConfigurationService
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.Cohort
import com.nextchaptersoftware.db.stores.MLRouterStore
import com.nextchaptersoftware.db.stores.SourceMarkStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.scmTeamStore
import com.nextchaptersoftware.embedding.encoding.EmbeddingEncoding
import com.nextchaptersoftware.embedding.events.queue.enqueue.EmbeddingEventEnqueueService
import com.nextchaptersoftware.embedding.service.stats.EmbeddingStatsFacade
import com.nextchaptersoftware.embedding.service.store.EmbeddingStoreFacade
import com.nextchaptersoftware.experts.TopicExpertService
import com.nextchaptersoftware.google.api.GoogleApiProvider
import com.nextchaptersoftware.google.services.GoogleCredentialProvider
import com.nextchaptersoftware.ingestion.redis.IngestionDisablementService
import com.nextchaptersoftware.insider.InsiderServiceInterface
import com.nextchaptersoftware.integration.queue.redis.cache.IngestionProgressServiceProvider
import com.nextchaptersoftware.integration.queue.redis.cache.StandardIngestionProgressServiceProvider
import com.nextchaptersoftware.jira.api.JiraApiProvider
import com.nextchaptersoftware.jira.data.services.JiraIssuePersistenceService
import com.nextchaptersoftware.ktor.utils.referrer
import com.nextchaptersoftware.linear.events.queue.enqueue.LinearEventEnqueueService
import com.nextchaptersoftware.links.LinkProcessorFactory
import com.nextchaptersoftware.maintenance.IdentityMaintenance
import com.nextchaptersoftware.maintenance.installation.StandardInstallationDeletionService
import com.nextchaptersoftware.maintenance.org.OrgDeletionService
import com.nextchaptersoftware.maintenance.scm.ScmTeamLifecycleMaintenance
import com.nextchaptersoftware.mcp.execution.McpToolInfoService
import com.nextchaptersoftware.mcp.execution.PersonMcpToolOverrideService
import com.nextchaptersoftware.migration.events.queue.enqueue.MigrationEventEnqueueService
import com.nextchaptersoftware.ml.api.MachineLearningApiProvider
import com.nextchaptersoftware.ml.inference.services.PersonMcpTemplateOverrideService
import com.nextchaptersoftware.ml.inference.services.inferences.MLInferenceService
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.notification.services.PeerInviteSuggestionService
import com.nextchaptersoftware.notion.api.NotionApiProvider
import com.nextchaptersoftware.notion.ingestion.redis.cache.NotionIngestionCache
import com.nextchaptersoftware.notion.ingestion.redis.cache.RedisNotionIngestionCache
import com.nextchaptersoftware.orchestration.enablement.OnboardingEnablementService
import com.nextchaptersoftware.pr.ingestion.queue.enqueue.PullRequestEventEnqueueService
import com.nextchaptersoftware.pr.summary.ingestion.pipeline.RepoPullRequestSummaryService
import com.nextchaptersoftware.pr.summary.ingestion.services.internal.PullRequestIngestionStateMachineProvider
import com.nextchaptersoftware.product.feedback.ProductFeedbackService
import com.nextchaptersoftware.rapid.services.RapidQueryService
import com.nextchaptersoftware.regression.testing.services.questionanswer.IsQuestionAnswerRegressionRunService
import com.nextchaptersoftware.regression.testing.services.questionanswer.IsQuestionAnswerRegressionService
import com.nextchaptersoftware.regression.testing.services.questionanswer.IsQuestionRegressionService
import com.nextchaptersoftware.repo.RepoAccessService
import com.nextchaptersoftware.repo.RepoComputeService
import com.nextchaptersoftware.repo.RepoFocusService
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.ScmCloneUrlProvider
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.scm.ScmTeamApiFactory
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.queue.enqueue.ScmEventProducer
import com.nextchaptersoftware.scm.services.ProfileService
import com.nextchaptersoftware.search.events.queue.enqueue.SearchPriorityEventEnqueueService
import com.nextchaptersoftware.search.semantic.services.EmbeddingExperimentService
import com.nextchaptersoftware.search.semantic.services.SemanticSearchQueryService
import com.nextchaptersoftware.search.semantic.services.eval.RegressionTestService
import com.nextchaptersoftware.search.semantic.services.functions.MLFunctionService
import com.nextchaptersoftware.search.semantic.services.retrieval.SemanticDocumentRetriever
import com.nextchaptersoftware.search.semantic.services.samplequestions.SampleQuestionGeneratorImpl
import com.nextchaptersoftware.search.semantic.services.samplequestions.SampleQuestionGeneratorService
import com.nextchaptersoftware.search.services.index.IndexingAndEmbeddingService
import com.nextchaptersoftware.service.ServiceHealthApi
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import com.nextchaptersoftware.sourcecode.ingestion.pipeline.RepoCodeIngestionService
import com.nextchaptersoftware.stackoverflowteams.api.StackOverflowTeamsApiProvider
import com.nextchaptersoftware.stackoverflowteams.api.StackOverflowTeamsAuthProvider
import com.nextchaptersoftware.summarization.events.queue.enqueue.SummarizationEventEnqueueService
import com.nextchaptersoftware.topic.events.queue.enqueue.TopicEventEnqueueService
import com.nextchaptersoftware.topic.ingestion.pipeline.RepoTopicIngestionService
import com.nextchaptersoftware.topic.insight.services.map.TopicMappingService
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.nextchaptersoftware.version.VersionService
import com.nextchaptersoftware.web.ingestion.WebIngestionService
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.auth.authenticate
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.http.content.staticResources
import io.ktor.server.request.path
import io.ktor.server.request.uri
import io.ktor.server.response.respond
import io.ktor.server.response.respondRedirect
import io.ktor.server.routing.Route
import io.ktor.server.routing.RoutingContext
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import io.ktor.server.sse.sse
import kotlinx.html.a
import kotlinx.html.h2

fun Application.configureRouting(
    adminAuthenticationProvider: AdminAuthenticationProvider,
    atlassianDataCenterAuthProvider: AtlassianDataCenterAuthProvider,
    billingEventEnqueueService: BillingEventEnqueueService,
    billingService: BillingService,
    ciLicenseService: CILicenseService,
    ciProjectApiFactory: CIProjectApiFactory,
    clientConfigService: ClientConfigService = ClientConfigService(),
    codaApiProvider: CodaApiProvider?,
    codaEventEnqueueService: CodaEventEnqueueService,
    codaPagePersistenceService: CodaPagePersistenceService,
    codaTokenProvider: CodaTokenProvider,
    config: GlobalConfig = GlobalConfig.INSTANCE,
    confluenceAtlassianTokenProvider: AtlassianTokenProvider,
    confluenceCloudApiProvider: ConfluenceCloudApiProvider,
    confluenceDataCenterApiProvider: ConfluenceDataCenterApiProvider,
    confluenceEventEnqueueService: ConfluenceEventEnqueueService,
    conversationAnalysisService: ConversationAnalysisService,
    dataSourcePresetConfigurationService: DataSourcePresetConfigurationService,
    embeddingEncoding: EmbeddingEncoding,
    embeddingEventEnqueueService: EmbeddingEventEnqueueService,
    embeddingExperimentService: EmbeddingExperimentService,
    embeddingStatsFacade: EmbeddingStatsFacade,
    embeddingStoreFacade: EmbeddingStoreFacade,
    enableTestBillingActions: Boolean,
    googleApiProvider: GoogleApiProvider,
    googleCredentialProvider: GoogleCredentialProvider?,
    googleVerifier: GoogleVerifier?,
    identityMaintenance: IdentityMaintenance,
    indexingAndEmbeddingService: IndexingAndEmbeddingService,
    inferenceService: MLInferenceService,
    inferenceTemplateService: MLInferenceTemplateService,
    ingestionDisablementService: IngestionDisablementService,
    insiderService: InsiderServiceInterface,
    installationDeletionService: StandardInstallationDeletionService,
    isQuestionAnswerRegressionRunService: IsQuestionAnswerRegressionRunService,
    isQuestionAnswerRegressionService: IsQuestionAnswerRegressionService,
    isQuestionRegressionService: IsQuestionRegressionService,
    jiraApiProvider: JiraApiProvider,
    jiraAtlassianTokenProvider: AtlassianTokenProvider,
    jiraIssuePersistenceService: JiraIssuePersistenceService,
    linearEventEnqueueService: LinearEventEnqueueService,
    linkProcessorFactory: LinkProcessorFactory,
    logFocusService: LogFocusService,
    machineLearningApiProvider: MachineLearningApiProvider,
    migrationEventEnqueueService: MigrationEventEnqueueService,
    mlFunctionService: MLFunctionService,
    notificationEventEnqueueService: NotificationEventEnqueueService,
    notionApiProvider: NotionApiProvider?,
    onboardingEnablementService: OnboardingEnablementService,
    orgDeletionService: OrgDeletionService,
    passwordVerifier: PasswordVerifier?,
    peerInviteSuggestionService: PeerInviteSuggestionService,
    personMcpTemplateOverrideService: PersonMcpTemplateOverrideService,
    personMcpToolOverrideService: PersonMcpToolOverrideService,
    productFeedbackService: ProductFeedbackService,
    profileService: ProfileService,
    pullRequestEventEnqueueService: PullRequestEventEnqueueService,
    pullRequestIngestionStateMachineProvider: PullRequestIngestionStateMachineProvider,
    rapidQueryService: RapidQueryService,
    regressionTestService: RegressionTestService,
    repoCodeIngestionService: RepoCodeIngestionService,
    repoComputeService: RepoComputeService,
    repoFocusService: RepoFocusService,
    repoPullRequestSummaryService: RepoPullRequestSummaryService,
    repoTopicIngestionService: RepoTopicIngestionService,
    sampleQuestionGenerator: SampleQuestionGeneratorImpl,
    sampleQuestionGeneratorService: SampleQuestionGeneratorService,
    scmAppApiFactory: ScmAppApiFactory,
    scmCloneUrlProvider: ScmCloneUrlProvider,
    scmEventProducer: ScmEventProducer,
    scmRepoApiFactory: ScmRepoApiFactory,
    scmTeamApiFactory: ScmTeamApiFactory,
    scmTeamLifecycleMaintenance: ScmTeamLifecycleMaintenance,
    scmUserApiFactory: ScmUserApiFactory,
    searchPriorityEventEnqueueService: SearchPriorityEventEnqueueService,
    semanticDocumentRetriever: SemanticDocumentRetriever,
    semanticSearchQueryService: SemanticSearchQueryService,
    serviceLifecycle: ServiceLifecycle,
    stackOverflowApiProvider: StackOverflowTeamsApiProvider,
    stackOverflowAuthProvider: StackOverflowTeamsAuthProvider,
    summarizationEventEnqueueService: SummarizationEventEnqueueService,
    topicEventEnqueueService: TopicEventEnqueueService,
    topicExpertService: TopicExpertService,
    topicMappingService: TopicMappingService,
    triageEventEnqueueService: TriageEventEnqueueService,
    userSecretServiceResolver: UserSecretServiceResolver,
    versionService: VersionService,
    webIngestionService: WebIngestionService,
) {
    val healthApiDelegateImpl by lazy {
        HealthApiDelegateImpl(ServiceHealthApi(serviceLifecycle = serviceLifecycle))
    }

    val repoAccessService by lazy {
        RepoAccessService(
            repoComputeService = repoComputeService,
        )
    }

    fun Route.tampermonkeyRoutes() {
        if (config.featureFlags.enableCiJobSearchByHtmlUrl) {
            post("/refreshTriageBuildJob") {
                triageRequestEventFromJobURL()
            }
        }
    }

    @Suppress("LongMethod")
    fun Route.authenticatedRoutes() {
        get("") { renderHomePage(page = AdminPage.Home, ingestionDisablementService = ingestionDisablementService) }
        get("/search") {
            renderSearchPage(
                page = AdminPage.Search,
                versionService = versionService,
                insiderService = insiderService,
                jiraApiProvider = jiraApiProvider,
                jiraAtlassianTokenProvider = jiraAtlassianTokenProvider,
            )
        }
        get("/visualize") { renderVisualizePage(page = AdminPage.Visualize) }
        get("/models") { renderModelsPage(page = AdminPage.Models) }
        route("/tampermonkey") {
            tampermonkeyRoutes()
        }

        databaseDeletionsRoutes()
        impersonateRoutes()
        orgRoutes(
            atlassianDataCenterAuthProvider = atlassianDataCenterAuthProvider,
            billingEventEnqueueService = billingEventEnqueueService,
            billingService = billingService,
            ciLicenseService = ciLicenseService,
            ciProjectApiFactory = ciProjectApiFactory,
            clientConfigService = clientConfigService,
            codaApiProvider = codaApiProvider,
            codaTokenProvider = codaTokenProvider,
            codaPagePersistenceService = codaPagePersistenceService,
            codaEventEnqueueService = codaEventEnqueueService,
            confluenceCloudApiProvider = confluenceCloudApiProvider,
            confluenceAtlassianTokenProvider = confluenceAtlassianTokenProvider,
            confluenceDataCenterApiProvider = confluenceDataCenterApiProvider,
            confluenceEventEnqueueService = confluenceEventEnqueueService,
            conversationAnalysisService = conversationAnalysisService,
            dataSourcePresetConfigurationService = dataSourcePresetConfigurationService,
            embeddingEncoding = embeddingEncoding,
            embeddingEventEnqueueService = embeddingEventEnqueueService,
            embeddingExperimentService = embeddingExperimentService,
            embeddingStoreFacade = embeddingStoreFacade,
            embeddingStatsFacade = embeddingStatsFacade,
            enableTestBillingActions = enableTestBillingActions,
            googleApiProvider = googleApiProvider,
            googleCredentialProvider = googleCredentialProvider,
            indexingAndEmbeddingService = indexingAndEmbeddingService,
            inferenceService = inferenceService,
            inferenceTemplateService = inferenceTemplateService,
            ingestionDisablementService = ingestionDisablementService,
            insiderService = insiderService,
            jiraApiProvider = jiraApiProvider,
            jiraAtlassianTokenProvider = jiraAtlassianTokenProvider,
            jiraIssuePersistenceService = jiraIssuePersistenceService,
            linearEventEnqueueService = linearEventEnqueueService,
            linkProcessorFactory = linkProcessorFactory,
            logFocusService = logFocusService,
            machineLearningApiProvider = machineLearningApiProvider,
            migrationEventEnqueueService = migrationEventEnqueueService,
            mlFunctionService = mlFunctionService,
            notificationEventEnqueueService = notificationEventEnqueueService,
            notionApiProvider = notionApiProvider,
            onboardingEnablementService = onboardingEnablementService,
            orgDeletionService = orgDeletionService,
            peerInviteSuggestionService = peerInviteSuggestionService,
            productFeedbackService = productFeedbackService,
            profileService = profileService,
            pullRequestEventEnqueueService = pullRequestEventEnqueueService,
            pullRequestIngestionStateMachineProvider = pullRequestIngestionStateMachineProvider,
            rapidQueryService = rapidQueryService,
            repoAccessService = repoAccessService,
            repoCodeIngestionService = repoCodeIngestionService,
            repoFocusService = repoFocusService,
            repoPullRequestSummaryService = repoPullRequestSummaryService,
            repoTopicIngestionService = repoTopicIngestionService,
            sampleQuestionGenerator = sampleQuestionGenerator,
            sampleQuestionGeneratorService = sampleQuestionGeneratorService,
            scmCloneUrlProvider = scmCloneUrlProvider,
            scmEventProducer = scmEventProducer,
            scmRepoApiFactory = scmRepoApiFactory,
            scmTeamApiFactory = scmTeamApiFactory,
            scmTeamLifecycleMaintenance = scmTeamLifecycleMaintenance,
            searchPriorityEventEnqueueService = searchPriorityEventEnqueueService,
            semanticDocumentRetriever = semanticDocumentRetriever,
            semanticSearchQueryService = semanticSearchQueryService,
            stackOverflowApiProvider = stackOverflowApiProvider,
            stackOverflowAuthProvider = stackOverflowAuthProvider,
            summarizationEventEnqueueService = summarizationEventEnqueueService,
            topicEventEnqueueService = topicEventEnqueueService,
            topicExpertService = topicExpertService,
            topicMappingService = topicMappingService,
            triageEventEnqueueService = triageEventEnqueueService,
            installationDeletionService = installationDeletionService,
            userSecretServiceResolver = userSecretServiceResolver,
            webIngestionService = webIngestionService,
        )
        ciTriagesRoutes(
            ciProjectApiFactory = ciProjectApiFactory,
            logFocusService = logFocusService,
            triageEventEnqueueService = triageEventEnqueueService,
        )
        featuresRoutes(clientConfigService)
        identityRoutes(
            profileService = profileService,
            scmUserApiFactory = scmUserApiFactory,
            userSecretServiceResolver = userSecretServiceResolver,
        )
        personRoutes(
            insiderService = insiderService,
            clientConfigService = clientConfigService,
            profileService = profileService,
            identityMaintenance = identityMaintenance,
            notificationEventEnqueueService = notificationEventEnqueueService,
            ingestionDisablementService = ingestionDisablementService,
            personMcpToolOverrideService = personMcpToolOverrideService,
            personMcpTemplateOverrideService = personMcpTemplateOverrideService,
        )
        featuresRoutes(clientConfigService)
        scmAppsRoutes(scmAppApiFactory, insiderService)
        queuesRoutes()
        vectorRoutes(embeddingStatsFacade = embeddingStatsFacade)
        versionRoutes(versionService, insiderService)
        metricsRoutes()
        analyticsRoutes(insiderService, conversationAnalysisService)
        operationsRoutes(
            ingestionDisablementService = ingestionDisablementService,
            profileService = profileService,
            migrators = listOf(
                SchemaMigrator,
            ),
            insiderService = insiderService,
        )
        apiDocRoutes()
        configsRoutes()
        machineLearningRoutes(
            mlInferenceTemplateService = inferenceTemplateService,
            inferenceService = inferenceService,
            regressionTestService = regressionTestService,
            isQuestionRegressionService = isQuestionRegressionService,
            isQuestionAnswerRegressionRunService = isQuestionAnswerRegressionRunService,
            isQuestionAnswerRegressionService = isQuestionAnswerRegressionService,
            insiderService = insiderService,
            personMcpToolOverrideService = personMcpToolOverrideService,
        )
        staticResources(remotePath = "/apis", basePackage = "apis")
    }

    routing {
        route("/api") {
            HealthApi(healthApiDelegateImpl)
        }

        // Path used for exposing health endpoint to Kubernetes
        route("/api/health/adminwebservice") {
            HealthApi(healthApiDelegateImpl)
        }

        route(WEB_ROOT) {
            staticPublicRoutes()
            loginRoutes(adminAuthenticationProvider, googleVerifier, passwordVerifier)
            authenticate(adminAuthenticationProvider.adminSessionCookieName) {
                authenticatedRoutes()
                get("{...}") { handleNotFound() }
            }
        }
    }
}

private suspend fun RoutingContext.handleNotFound() {
    call.respondHtmlTemplate(WideContentTemplate(AdminPage.Error, call.getAdminIdentity())) {
        content {
            alert(
                bootstrapStyle = BootstrapStyle.Danger,
                title = "Not found",
                description = "Page not found.",
            )

            h2(classes = "mt-5") { +"Url" }
            a(href = call.request.uri) { +call.request.uri }
        }
    }
}

private fun Route.staticPublicRoutes() {
    staticResources(remotePath = "css", basePackage = "public/css")
    staticResources(remotePath = "images", basePackage = "public/images")
    staticResources(remotePath = "pages", basePackage = "public/pages")
    staticResources(remotePath = "scripts", basePackage = "public/scripts")
}

private fun Route.configsRoutes() {
    if (AdminConfig.INSTANCE.adminFeatureFlags.enableConfigsPage) {
        route("/configs") {
            get {
                renderConfigsPage(page = AdminPage.Configs)
            }

            get("/getFullConfig") {
                getFullConfig()
            }

            post("/exportConfigs") {
                exportConfigs()
            }
        }
    }
}

@Suppress("LongMethod")
private fun Route.machineLearningRoutes(
    mlInferenceTemplateService: MLInferenceTemplateService,
    inferenceService: MLInferenceService,
    regressionTestService: RegressionTestService,
    isQuestionRegressionService: IsQuestionRegressionService,
    isQuestionAnswerRegressionRunService: IsQuestionAnswerRegressionRunService,
    isQuestionAnswerRegressionService: IsQuestionAnswerRegressionService,
    insiderService: InsiderServiceInterface,
    personMcpToolOverrideService: PersonMcpToolOverrideService,
) {
    route("/ML") {
        get { call.respondRedirect("templates") }

        feedbackRoutes(insiderService)

        route("/regressions") {
            get {
                renderGlobalRegressionInferencesPage(
                    page = AdminPage.MLRegressions,
                    inferenceService = inferenceService,
                )
            }
        }

        route("/isQuestionRegressions") {
            val isQuestionRegressionTestActions: Route.() -> Unit = {
                route("/{regressionTestId}") {
                    postAction("/delete", redirectPath = "/isQuestionRegressions") {
                        deleteIsQuestionRegressionTest()
                    }
                    postAction("/update") {
                        updateIsQuestionRegressionTest()
                    }
                }
            }
            get {
                renderIsQuestionTemplateRegressionTestingPage(
                    page = AdminPage.MLIsQuestionRegressions,
                    templateService = mlInferenceTemplateService,
                )
            }
            postAction("/add") { addIsQuestionRegressionTest() }
            route("/run") {
                isQuestionRegressionTestActions()
                get {
                    runIsQuestionRegressionTest(
                        page = AdminPage.MLIsQuestionRegressions,
                        templateService = mlInferenceTemplateService,
                        isQuestionRegressionService = isQuestionRegressionService,
                    )
                }
                route("/{regressionTestId}") {
                    get {
                        runSingleIsQuestionRegressionTest(
                            page = AdminPage.MLIsQuestionRegressions,
                            templateService = mlInferenceTemplateService,
                            isQuestionRegressionService = isQuestionRegressionService,
                        )
                    }
                }
            }
            isQuestionRegressionTestActions()
        }

        route("isQuestionAnswerRegressions") {
            get {
                renderIsQuestionAnswerTemplateRegressionTestingPage(
                    page = AdminPage.MLIsQuestionAnswerRegressions,
                    templateService = mlInferenceTemplateService,
                )
            }
            postAction("/run") {
                runIsQuestionAnswerRegressionTest(
                    isQuestionAnswerRegressionRunService = isQuestionAnswerRegressionRunService,
                )
            }
            route("/run") {
                route("/{regressionTestId}") {
                    get {
                        runSingleIsQuestionAnswerRegressionTest(
                            page = AdminPage.MLIsQuestionAnswerRegressions,
                            templateService = mlInferenceTemplateService,
                            isQuestionAnswerRegressionService = isQuestionAnswerRegressionService,
                        )
                    }
                }
            }
            route("/{regressionTestId}") {
                route("/delete") {
                    get {
                        deleteIsQuestionAnswerRegressionTest()
                    }
                }
            }
        }

        route("/templates") {
            get {
                renderMLTemplatesPage(
                    page = AdminPage.MLTemplates,
                    mlInferenceTemplateService = mlInferenceTemplateService,
                )
            }

            postAction("/exportAllGlobalTemplates") {
                exportAllGlobalMLTemplates(mlInferenceTemplateService)
            }

            route("/{templateId}") {
                get {
                    renderMLTemplatePage(
                        page = AdminPage.MLTemplates,
                        templateService = mlInferenceTemplateService,
                    )
                }
                route("/regression-tests") {
                    get {
                        renderTemplateRegressionTestingPage(
                            page = AdminPage.MLTemplates,
                            regressionTestService = regressionTestService,
                        )
                    }
                    postAction("/runRegressionTest") {
                        runRegressionTest(
                            regressionTestService = regressionTestService,
                        )
                    }
                    route("/{testId}") {
                        route("/delete") {
                            get {
                                deleteRegressionTest(
                                    regressionTestService = regressionTestService,
                                )
                            }
                        }
                        get {
                            renderTemplateRegressionTestDetailsPage(
                                regressionTestService = regressionTestService,
                                page = AdminPage.MLTemplates,
                            )
                        }
                        route("/evals") {
                            route("/{evalId}") {
                                get {
                                    renderRegressionEvalDetailsPage(
                                        regressionTestService = regressionTestService,
                                        page = AdminPage.MLTemplates,
                                    )
                                }
                            }
                        }
                    }
                }
                postAction("/clone") { cloneMLTemplate(mlInferenceTemplateService) }
                postAction("/delete") { deleteMLTemplate(mlInferenceTemplateService) }
                postAction("/make-global") { makeMLTemplateGlobal(mlInferenceTemplateService) }
                postAction("/make-demo") { makeMLTemplateDemo(mlInferenceTemplateService) }
                postAction("/updateTemplate") { updateMLTemplate(mlInferenceTemplateService) }
                postAction("/export") { exportMLTemplate() }
            }
            postAction("/import") { importMLTemplate(mlInferenceTemplateService) }
        }

        route("mcp-tools") {
            get {
                renderMcpToolsPage(
                    page = AdminPage.McpTools,
                    mcpToolInfoService = McpToolInfoService(personMcpToolOverrideService = personMcpToolOverrideService),
                )
            }
            postAction("/save") {
                handleMcpToolEdit(
                    mcpToolInfoService = McpToolInfoService(personMcpToolOverrideService = personMcpToolOverrideService),
                )
            }
        }
    }
}

private fun Route.featuresRoutes(clientConfigService: ClientConfigService) {
    route("/features") {
        get { renderConfigPage(AdminPage.Features) }
        postAction("/updateGlobalCapability") { updateGlobalCapability(clientConfigService) }
        postAction("/updateGlobalOrgCapability/{orgId}") { updateGlobalOrgCapability(clientConfigService) }
        postAction("/updateGlobalPersonCapability/{personId}") { updateGlobalPersonCapability(clientConfigService) }
    }
}

private fun Route.scmAppsRoutes(
    scmAppApiFactory: ScmAppApiFactory,
    insiderService: InsiderServiceInterface,
) {
    route("/scm-apps") {
        get { renderScmAppsPage(AdminPage.ScmApps, scmAppApiFactory) }

        Provider.GitHub.also { provider ->
            route("/${provider.name}") {
                get { renderIntegrationPage(AdminPage.ScmApps, scmAppApiFactory, provider, insiderService) }
                scmInstallationsRoutes(scmAppApiFactory, provider)
            }
        }

        Provider.scmProviders.filter { it.isEnterprise }.forEach { provider ->
            route(provider.name) {
                get { call.respondRedirect("$WEB_ROOT/scm-apps") }
                route("new") {
                    get { renderNewIntegrationPage(AdminPage.ScmApps, provider) }
                    post { createNewApp(provider = provider) }
                }
                route("{enterpriseId}") {
                    get { renderIntegrationPage(AdminPage.ScmApps, scmAppApiFactory, provider, insiderService) }
                    scmInstallationsRoutes(scmAppApiFactory, provider)
                    postAction("/deleteScmApp") { deleteScmApp() }
                    postAction("/updatePinnedIdentityId") { updatePinnedIdentityId() }
                }
            }
        }
    }
}

private fun Route.scmInstallationsRoutes(scmAppApiFactory: ScmAppApiFactory, provider: Provider) {
    route("/installations") {
        get { renderInstallationsPage(AdminPage.ScmInstallations, scmAppApiFactory, provider) }
        route("/{installationId}") {
            get { renderInstallationPage(AdminPage.ScmInstallation, scmAppApiFactory, provider) }
            postAction("/installationSuspend") { installationSuspend(scmAppApiFactory, provider) }
            postAction("/installationUnsuspend") { installationUnsuspend(scmAppApiFactory, provider) }
            postAction("/installationUninstall", redirectPath = "/installations") { installationUninstall(scmAppApiFactory, provider) }
        }
    }
    route("/installationRequests") {
        get { renderInstallationRequestsPage(AdminPage.ScmInstallationRequests, provider) }
    }
}

private fun Route.metricsRoutes() {
    route("/metrics") {
        get { call.respondRedirect(AdminNavigationTab.GrowthMonthly.route) }
        route("/activity") {
            get { call.respondRedirect(AdminNavigationTab.ActivityAll.route) }
            get("/${Cohort.Team.name}") { renderMetricsPage(AdminPage.ActivityTeam, Cohort.Team) }
            get("/${Cohort.All.name}") { renderMetricsPage(AdminPage.ActivityAll, Cohort.All) }
        }
        route("/growth") {
            get { call.respondRedirect(AdminNavigationTab.GrowthMonthly.route) }
            get("/monthly") { renderGrowthPage(AdminPage.GrowthMonthly, Cohort.Team) }
            get("/weekly") { renderGrowthPage(AdminPage.GrowthWeekly, Cohort.Team) }
        }
        get("/triages") { renderMetricsTriagePage(AdminPage.MetricsTriages) }
    }
}

private fun Route.analyticsRoutes(
    insiderService: InsiderServiceInterface,
    conversationAnalysisService: ConversationAnalysisService,
) {
    route("/analytics") {
        get { call.respondRedirect(AdminNavigationTab.AnalyticsAnswers.route) }
        get("/answers") { renderAnalyticsAnswersPage(AdminPage.AnalyticsAnswers) }
        get("/ci") { renderAnalyticsCiPage(AdminPage.AnalyticsCi) }
        route("/members") {
            get { renderAnalyticsMembersPage(AdminPage.AnalyticsMembers) }
            postAction("/allSessionActions") { downloadSessionActions() }
        }
        get("/onboarding") { renderAnalyticsOnboardingPage(AdminPage.AnalyticsOnboarding) }
        get("/dataPipelines") { renderAnalyticsDataPipelinePage(AdminPage.AnalyticsDataPipelines) }
        get("/answerPreferences") { renderAnswerPreferencesPage(AdminPage.AnalyticsAnswerPreferences, insiderService) }
        get("/incognitoPreferences") { renderIncognitoPreferencesPage(AdminPage.AnalyticsIncognitoPreferences) }
        get("/slackChannelPreferences") { renderSlackChannelPreferencesPage(AdminPage.AnalyticsSlackChannelPreferences) }
        get("/slackAutoAnswers") { renderSlackAutoAnswerPage(AdminPage.AnalyticsSlackAutoAnswers) }
        get("/ideFeatureUsage") { renderIdeFeatureUsagePage(AdminPage.AnalyticsIdeFeatureUsage, insiderService) }
        get("/productFeedback") { renderProductFeedbackPage(AdminPage.AnalyticsProductFeedback) }
        get("/macNotificationPreferences") { renderMacNotificationPreferencesPage(AdminPage.AnalyticsMacNotificationPreferences) }
        route("/mcpUsage") {
            get {
                renderMcpInferencesPage(
                    page = AdminPage.AnalyticsMcpUsage,
                    insiderService = insiderService,
                )
            }
        }
        get("/fileUploads") { renderFileUploadUsagePage(AdminPage.AnalyticsFileUploadUsage) }
        route("/slackConnectConversion") {
            get { renderSlackConnectConversionPage(AdminPage.AnalyticsSlackConnectConversion) }
        }
        conversationAnalysisRoutes(conversationAnalysisService = conversationAnalysisService)
    }
}

private fun Route.mlRoutes(
    conversationAnalysisService: ConversationAnalysisService,
    dataSourcePresetConfigurationService: DataSourcePresetConfigurationService,
    embeddingEncoding: EmbeddingEncoding,
    embeddingStoreFacade: EmbeddingStoreFacade,
    embeddingStatsFacade: EmbeddingStatsFacade,
    inferenceService: MLInferenceService,
    inferenceTemplateService: MLInferenceTemplateService,
    insiderService: InsiderServiceInterface,
    linkProcessorFactory: LinkProcessorFactory,
    machineLearningApiProvider: MachineLearningApiProvider,
    mlFunctionService: MLFunctionService,
    searchPriorityEventEnqueueService: SearchPriorityEventEnqueueService,
    semanticDocumentRetriever: SemanticDocumentRetriever,
    semanticSearchQueryService: SemanticSearchQueryService,
    topicEventEnqueueService: TopicEventEnqueueService,
    topicExpertService: TopicExpertService,
) {
    route("/machineLearning") {
        postAction("/regenerateSampleQuestionsForOrg") { regenerateSampleQuestionsForOrg(searchPriorityEventEnqueueService) }
        postAction("/updateOrgMLSettings") { updateOrgMLSettings() }

        get {
            renderOrgMachineLearningPage(
                page = AdminPage.OrgMachineLearning,
                inferenceTemplateService = inferenceTemplateService,
            )
        }
        get("/functions") {
            renderFunctionRetrievalPage(
                page = AdminPage.OrgMLFunctionRetrieval,
                mlFunctionService = mlFunctionService,
            )
        }
        conversationAnalysisRoutes(
            conversationAnalysisService = conversationAnalysisService,
        )
        semanticSearchRoutes(
            semanticSearchQueryService = semanticSearchQueryService,
            inferenceTemplateService = inferenceTemplateService,
            dataSourcePresetConfigurationService = dataSourcePresetConfigurationService,
        )
        documentRetrievalRoutes(
            embeddingStoreFacade = embeddingStoreFacade,
            embeddingEncoding = embeddingEncoding,
            templateService = inferenceTemplateService,
            semanticDocumentRetriever = semanticDocumentRetriever,
            dataSourcePresetConfigurationService = dataSourcePresetConfigurationService,
        )
        linkRetrievalRoutes(
            linkProcessorFactory = linkProcessorFactory,
        )
        feedbackRoutes(
            insiderService = insiderService,
        )
        inferenceExampleRoutes(
            inferenceService = inferenceService,
            inferenceTemplateService = inferenceTemplateService,
            embeddingStoreFacade = embeddingStoreFacade,
            embeddingEncoding = embeddingEncoding,
            machineLearningApiProvider = machineLearningApiProvider,
        )
        mlRoutersRoute()
        topicsRoutes(
            topicEventEnqueueService = topicEventEnqueueService,
            topicExpertService = topicExpertService,
        )
        vectorRoutes(embeddingStatsFacade = embeddingStatsFacade)
    }
}

private fun Route.orgInstallationRoutes(
    atlassianDataCenterAuthProvider: AtlassianDataCenterAuthProvider,
    ciLicenseService: CILicenseService,
    ciProjectApiFactory: CIProjectApiFactory,
    codaApiProvider: CodaApiProvider?,
    codaTokenProvider: CodaTokenProvider,
    codaPagePersistenceService: CodaPagePersistenceService,
    codaEventEnqueueService: CodaEventEnqueueService,
    confluenceCloudApiProvider: ConfluenceCloudApiProvider,
    confluenceAtlassianTokenProvider: AtlassianTokenProvider,
    confluenceDataCenterApiProvider: ConfluenceDataCenterApiProvider,
    confluenceEventEnqueueService: ConfluenceEventEnqueueService,
    embeddingEventEnqueueService: EmbeddingEventEnqueueService,
    googleApiProvider: GoogleApiProvider,
    googleCredentialProvider: GoogleCredentialProvider?,
    indexingAndEmbeddingService: IndexingAndEmbeddingService,
    inferenceTemplateService: MLInferenceTemplateService,
    jiraApiProvider: JiraApiProvider,
    jiraAtlassianTokenProvider: AtlassianTokenProvider,
    jiraIssuePersistenceService: JiraIssuePersistenceService,
    linearEventEnqueueService: LinearEventEnqueueService,
    logFocusService: LogFocusService,
    notificationEventEnqueueService: NotificationEventEnqueueService,
    notionApiProvider: NotionApiProvider?,
    peerInviteSuggestionService: PeerInviteSuggestionService,
    productFeedbackService: ProductFeedbackService,
    profileService: ProfileService,
    pullRequestEventEnqueueService: PullRequestEventEnqueueService,
    pullRequestIngestionStateMachineProvider: PullRequestIngestionStateMachineProvider,
    rapidQueryService: RapidQueryService,
    repoAccessService: RepoAccessService,
    repoCodeIngestionService: RepoCodeIngestionService,
    repoFocusService: RepoFocusService,
    repoPullRequestSummaryService: RepoPullRequestSummaryService,
    repoTopicIngestionService: RepoTopicIngestionService,
    sampleQuestionGenerator: SampleQuestionGeneratorImpl,
    sampleQuestionGeneratorService: SampleQuestionGeneratorService,
    scmCloneUrlProvider: ScmCloneUrlProvider,
    scmRepoApiFactory: ScmRepoApiFactory,
    stackOverflowApiProvider: StackOverflowTeamsApiProvider,
    stackOverflowAuthProvider: StackOverflowTeamsAuthProvider,
    summarizationEventEnqueueService: SummarizationEventEnqueueService,
    topicEventEnqueueService: TopicEventEnqueueService,
    topicExpertService: TopicExpertService,
    topicMappingService: TopicMappingService,
    triageEventEnqueueService: TriageEventEnqueueService,
    userSecretServiceResolver: UserSecretServiceResolver,
    webIngestionService: WebIngestionService,
) {
    route("/installations") {
        get {
            renderOrgInstallationsPage(page = AdminPage.OrgInstallations)
        }
        route("/{installationId}") {
            get {
                // FIXME: remove `provider` from the route
                val installation = call.parameters.optionalInstallation() ?: return@get call.respond(HttpStatusCode.NotFound)
                call.respondRedirect("$WEB_ROOT/orgs/${installation.orgId}/installations/${installation.provider}/${installation.id}")
            }
            memberRoutes(
                ciLicenseService = ciLicenseService,
                ciProjectApiFactory = ciProjectApiFactory,
                embeddingEventEnqueueService = embeddingEventEnqueueService,
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                logFocusService = logFocusService,
                notificationEventEnqueueService = notificationEventEnqueueService,
                peerInviteSuggestionService = peerInviteSuggestionService,
                productFeedbackService = productFeedbackService,
                profileService = profileService,
                pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                pullRequestIngestionStateMachineProvider = pullRequestIngestionStateMachineProvider,
                repoAccessService = repoAccessService,
                repoCodeIngestionService = repoCodeIngestionService,
                repoFocusService = repoFocusService,
                repoPullRequestSummaryService = repoPullRequestSummaryService,
                repoTopicIngestionService = repoTopicIngestionService,
                sampleQuestionGenerator = sampleQuestionGenerator,
                sampleQuestionGeneratorService = sampleQuestionGeneratorService,
                scmCloneUrlProvider = scmCloneUrlProvider,
                scmRepoApiFactory = scmRepoApiFactory,
                summarizationEventEnqueueService = summarizationEventEnqueueService,
                templateService = inferenceTemplateService,
                topicEventEnqueueService = topicEventEnqueueService,
                topicExpertService = topicExpertService,
                topicMappingService = topicMappingService,
                triageEventEnqueueService = triageEventEnqueueService,
            )
        }
        awsRoutes()
        scmInstallationsRoutes()
        ciInstallationsRoutes(
            ciProjectApiFactory = ciProjectApiFactory,
            logFocusService = logFocusService,
            triageEventEnqueueService = triageEventEnqueueService,
        )
        confluenceSiteRoutes(
            confluenceCloudApiProvider,
            confluenceAtlassianTokenProvider,
            atlassianDataCenterAuthProvider,
            confluenceDataCenterApiProvider,
            confluenceEventEnqueueService,
        )
        googleRoutes(googleApiProvider = googleApiProvider, googleCredentialProvider = googleCredentialProvider)
        asanaWorkspaceRoutes()
        jiraSiteRoutes(jiraApiProvider, jiraAtlassianTokenProvider, atlassianDataCenterAuthProvider, jiraIssuePersistenceService, rapidQueryService)
        linearOrganizationRoutes(linearEventEnqueueService)
        notionRoutes(
            notionApiProvider = notionApiProvider,
            cache = RedisNotionIngestionCache(),
        )
        slackRoutes(userSecretServiceResolver = userSecretServiceResolver)
        stackOverflowRoutes(stackOverflowApiProvider, stackOverflowAuthProvider)
        webIngestionRoutes(
            embeddingEventEnqueueService = embeddingEventEnqueueService,
            webIngestionService = webIngestionService,
        )
        codaRoutes(
            codaApiProvider = codaApiProvider,
            tokenProvider = codaTokenProvider,
            codaPagePersistenceService = codaPagePersistenceService,
            rapidQueryService = rapidQueryService,
            codaEventEnqueueService = codaEventEnqueueService,
        )
        unblockedRoutes()
    }
}

private fun Route.apiDocRoutes() {
    route("api-private-docs") {
        get {
            val page = AdminPage.ApiPrivateDocs
            val adminIdentity = call.getAdminIdentity()
            call.respondHtmlTemplate(BlankTemplate()) {
                header { renderHeader(page) }
                navbar { renderNavbar(page = page, showSearch = false, identity = adminIdentity.identity) }
                content {
                    autoResizeIframe {
                        src = "pages/private-api.html"
                    }
                }
            }
        }
    }
}

private fun Route.operationsRoutes(
    ingestionDisablementService: IngestionDisablementService,
    profileService: ProfileService,
    migrators: List<Migrator>,
    insiderService: InsiderServiceInterface,
) {
    route("/operations") {
        get { renderOperationsPage(AdminPage.Operations, ingestionDisablementService) }

        // metrics
        postAction("/backfillEngagementMetrics") { backfillEngagementMetrics() }
        postAction("/backfillGrowthMetrics") { backfillGrowthMetrics() }
        postAction("/backfillGrowthSlackMetrics") { backfillGrowthSlackMetrics() }
        postAction("/backfillGrowthCIMetrics") { backfillGrowthCIMetrics() }
        postAction("/clearEngagementMetrics") { clearEngagementMetrics() }
        postAction("/clearGrowthMetrics") { clearGrowthMetrics() }
        postAction("/clearGrowthSlackMetrics") { clearGrowthSlackMetrics() }
        postAction("/clearGrowthCIMetrics") { clearGrowthCIMetrics() }
        postAction("/migrateDeferralDates") { migrateDeferralDates() }
        postAction("/deduplicateFeedbackResponses") { deduplicateFeedbackResponses() }

        postAction("/backfillLastQuestionAskedAt") { backfillLastQuestionAskedAt() }
        postAction("/backfillGoogleConnectingTeamMember") { backfillGoogleConnectingMember() }
        postAction("/backfillSocialNetworks") { backfillSocialNetworks() }
        postAction("/migrateHackOrgMember") { migrateHackOrgMember() }
        postAction("/migrateMembers") { migrateMembers() }
        postAction("/refreshBotProfile") { refreshBotProfile() }
        postAction("/refreshProfilesMissingPrimaryEmail") { refreshProfilesMissingPrimaryEmail(profileService) }
        postAction("/reingestAllConfluenceSites") { reingestAllConfluenceSites() }
        postAction("/reingestAllGitHubIssues") { reingestAllGitHubIssues() }
        postAction("/reingestAllJiraSites") { reingestAllJiraSites() }
        postAction("/resetDataIngestionForTeamsNeedingReprocessing") { resetDataIngestionForTeamsNeedingReprocessing() }
        postAction("/retryErroredNotionPageIngestions") { retryErroredNotionPageIngestions() }
        postAction("/setLastSyncedForJiraIngestions") { setLastSyncedForJiraIngestions() }
        postAction("/toggleIngestion") { toggleIngestion(ingestionDisablementService) }
        postAction("/downloadBusinessPlanOrgsCsv") { downloadBusinessPlanOrgsCsv(insiderService = insiderService) }
        postAction("/setAuditLogSearchableText") { setAuditLogSearchableText() }
        postAction("/mergeDuplicatePeople") { mergeDuplicatePeople() }

        migrators.forEach { migrator ->
            val path = migrator::class.simpleName.required()
            postAction("/$path") { executeMigrator(migrator) }
        }
    }
}

private fun Route.identityRoutes(
    profileService: ProfileService,
    scmUserApiFactory: ScmUserApiFactory,
    userSecretServiceResolver: UserSecretServiceResolver,
) {
    route("/identities") {
        get { call.respondRedirect("$WEB_ROOT/people") }
        route("/{identityId}") {
            get { renderIdentityPage(AdminPage.Identity) }
            get("getAccessToken") { getAccessToken(userSecretServiceResolver = userSecretServiceResolver) }
            postAction("/promptIdentityToReconnect") { promptIdentityToReconnect() }
            postAction("/refreshProfile") { refreshProfileForIdentity(profileService) }
            postAction("/unlinkPersonFromIdentity") { unlinkPersonFromIdentity() }
            postAction("/revokeIdentity") { revokeIdentity() }

            route("/raw") {
                get("/scmAccounts") { rawScmAccounts(scmUserApiFactory = scmUserApiFactory) }
                get("/scmUser") { rawScmUser(scmUserApiFactory = scmUserApiFactory) }
            }
        }
    }
}

private fun Route.personRoutes(
    insiderService: InsiderServiceInterface,
    clientConfigService: ClientConfigService,
    profileService: ProfileService,
    identityMaintenance: IdentityMaintenance,
    notificationEventEnqueueService: NotificationEventEnqueueService,
    ingestionDisablementService: IngestionDisablementService,
    personMcpToolOverrideService: PersonMcpToolOverrideService,
    personMcpTemplateOverrideService: PersonMcpTemplateOverrideService,
) {
    route("/people") {
        get {
            renderPeoplePage(
                page = AdminPage.People,
                insiderService = insiderService,
                ingestionDisablementService = ingestionDisablementService,
            )
        }

        route("/{personId}") {
            get {
                renderPersonPage(
                    page = AdminPage.Person,
                    insiderService = insiderService,
                )
            }
            route("/mcp-tool-overrides") {
                get {
                    renderPersonMcpToolOverridesPage(
                        page = AdminPage.PersonMcpToolOverrides,
                        mcpToolInfoService = McpToolInfoService(personMcpToolOverrideService = personMcpToolOverrideService),
                        personMcpToolOverrideService = personMcpToolOverrideService,
                        personMcpTemplateOverrideService = personMcpTemplateOverrideService,
                    )
                }
                postAction("/save") {
                    handlePersonMcpToolOverridesEdit(
                        personMcpToolOverrideService = personMcpToolOverrideService,
                        personMcpTemplateOverrideService = personMcpTemplateOverrideService,
                        mcpToolInfoService = McpToolInfoService(personMcpToolOverrideService = personMcpToolOverrideService),
                    )
                }
            }
            postAction("/resetVSCodeOnboardingStates") { resetVSCodeOnboardingStates() }
            postAction("/resetIntellijOnboardingStates") { resetIntellijOnboardingStates() }
            postAction("/resetHubOnboardingStates") { resetHubOnboardingStates() }
            postAction("/updateOnboardingStates") { updateOnboardingStates() }
            postAction("/updatePersonEmailPreferences") { updatePersonEmailPreferences() }
            postAction("/updatePersonCapability") { updatePersonCapability(clientConfigService) }
            postAction("/refreshProfile") { refreshProfileForPerson(profileService) }
            postAction("/deauthorizePerson") { deauthorizePerson(identityMaintenance) }
            postAction("/deletePerson", redirectPath = "/people") { deletePerson(identityMaintenance) }
            postAction("/resetPersonPreferences") { resetPersonPreferences() }
            postAction("/updatePrimaryEmail") { updatePrimaryEmail() }
            postAction("/sendWelcomeEmail") { sendWelcomeEmail(notificationEventEnqueueService) }
            postAction("/updateSubscribedReleaseChannel") { updatePersonSubscribedReleaseChannel() }
            postAction("/deleteEmailEvent") { deleteEmailEvent() }
            postAction("/removeFromInactiveFollowupLists") { removeFromInactiveFollowupLists(notificationEventEnqueueService) }
        }
    }
}

@Suppress("LongMethod")
private fun Route.teamRoutes(
    ciLicenseService: CILicenseService,
    ciProjectApiFactory: CIProjectApiFactory,
    embeddingEventEnqueueService: EmbeddingEventEnqueueService,
    embeddingExperimentService: EmbeddingExperimentService,
    indexingAndEmbeddingService: IndexingAndEmbeddingService,
    inferenceTemplateService: MLInferenceTemplateService,
    ingestionDisablementService: IngestionDisablementService,
    insiderService: InsiderServiceInterface,
    logFocusService: LogFocusService,
    migrationEventEnqueueService: MigrationEventEnqueueService,
    notificationEventEnqueueService: NotificationEventEnqueueService,
    peerInviteSuggestionService: PeerInviteSuggestionService,
    productFeedbackService: ProductFeedbackService,
    profileService: ProfileService,
    pullRequestEventEnqueueService: PullRequestEventEnqueueService,
    pullRequestIngestionStateMachineProvider: PullRequestIngestionStateMachineProvider,
    repoAccessService: RepoAccessService,
    repoCodeIngestionService: RepoCodeIngestionService,
    repoFocusService: RepoFocusService,
    repoPullRequestSummaryService: RepoPullRequestSummaryService,
    repoTopicIngestionService: RepoTopicIngestionService,
    sampleQuestionGenerator: SampleQuestionGeneratorImpl,
    sampleQuestionGeneratorService: SampleQuestionGeneratorService,
    scmCloneUrlProvider: ScmCloneUrlProvider,
    scmEventProducer: ScmEventProducer,
    scmRepoApiFactory: ScmRepoApiFactory,
    scmTeamApiFactory: ScmTeamApiFactory,
    scmTeamLifecycleMaintenance: ScmTeamLifecycleMaintenance,
    sourceMarkStore: SourceMarkStore = Stores.sourceMarkStore,
    summarizationEventEnqueueService: SummarizationEventEnqueueService,
    topicEventEnqueueService: TopicEventEnqueueService,
    topicExpertService: TopicExpertService,
    topicMappingService: TopicMappingService,
    triageEventEnqueueService: TriageEventEnqueueService,
    installationDeletionService: StandardInstallationDeletionService,
) {
    route("/teams") {
        get {
            renderScmTeamsPage(
                page = AdminPage.OrgScmTeams,
                insiderService = insiderService,
                ingestionDisablementService = ingestionDisablementService,
            )
        }
        route("/{teamId}") {
            get { renderScmTeamPage(page = AdminPage.OrgScmTeam, insiderService = insiderService) }
            get("/getRateLimit") { getRateLimit(scmTeamApiFactory) }
            get("/getApiToken") { getApiToken(scmTeamApiFactory) }
            postAction("/runBulkPRIngestionForAllRepos") { runBulkPRIngestionForAllRepos() }
            postAction("/runLinearIngestionForAllTeams") { runLinearIngestionForAllTeams() }
            postAction("/archiveLowRelevanceThreads") { archiveLowRelevanceThreads(migrationEventEnqueueService) }
            postAction("/backfillSourceMarkArchived") { backfillSourceMarkArchived(sourceMarkStore) }
            postAction("/buildMemberAssociations") { buildMemberAssociations() }
            postAction("/clearMemberAssociations") { clearMemberAssociations() }
            postAction("/deleteTeam") { deleteTeam(installationDeletionService) }
            postAction("/uninstallScmOrg") { uninstallScmOrg(scmTeamLifecycleMaintenance) }
            postAction("/deleteTeamPermanently") { deleteTeamPermanently(scmTeamLifecycleMaintenance) }
            postAction("/unmarkPullRequestIngestForTeam") { unmarkPullRequestIngestForTeam(repoPullRequestSummaryService) }
            postAction("/unmarkTopicIngestForTeam") { unmarkTopicIngestForTeam(repoTopicIngestionService) }
            postAction("/undeleteTeam") { undeleteTeam() }
            postAction("/updateTeamSettings") { updateTeamSettings() }
            postAction("/archivePullRequests") { archivePullRequests(pullRequestEventEnqueueService) }
            postAction("/regenerateRecommendedTopicExperts") { regenerateRecommendedTopicExperts() }
            postAction("/runEmbeddingExperiment") { runEmbeddingExperiment(embeddingExperimentService) }
            postAction("/resetDataIngestion") { resetDataIngestion() }
            postAction("/setInitialIngestionCompletedAtForAllIntegrations") { setInitialIngestionCompletedAtForAllIntegrations() }
            postAction("/refreshScmResources") { refreshScmResources(scmEventProducer) }
            postAction("/pausePRIngestion") { pausePRIngestion() }
            postAction("/unpausePRIngestion") { unpausePRIngestion() }

            ciTriagesRoutes(
                ciProjectApiFactory = ciProjectApiFactory,
                logFocusService = logFocusService,
                triageEventEnqueueService = triageEventEnqueueService,
            )
            memberRoutes(
                ciLicenseService = ciLicenseService,
                ciProjectApiFactory = ciProjectApiFactory,
                embeddingEventEnqueueService = embeddingEventEnqueueService,
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                logFocusService = logFocusService,
                notificationEventEnqueueService = notificationEventEnqueueService,
                peerInviteSuggestionService = peerInviteSuggestionService,
                profileService = profileService,
                pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                pullRequestIngestionStateMachineProvider = pullRequestIngestionStateMachineProvider,
                repoAccessService = repoAccessService,
                repoCodeIngestionService = repoCodeIngestionService,
                repoFocusService = repoFocusService,
                repoPullRequestSummaryService = repoPullRequestSummaryService,
                repoTopicIngestionService = repoTopicIngestionService,
                sampleQuestionGenerator = sampleQuestionGenerator,
                sampleQuestionGeneratorService = sampleQuestionGeneratorService,
                scmCloneUrlProvider = scmCloneUrlProvider,
                scmRepoApiFactory = scmRepoApiFactory,
                summarizationEventEnqueueService = summarizationEventEnqueueService,
                templateService = inferenceTemplateService,
                topicEventEnqueueService = topicEventEnqueueService,
                topicExpertService = topicExpertService,
                topicMappingService = topicMappingService,
                triageEventEnqueueService = triageEventEnqueueService,
                productFeedbackService = productFeedbackService,
            )
            repoRoutes(
                ciProjectApiFactory = ciProjectApiFactory,
                embeddingEventEnqueueService = embeddingEventEnqueueService,
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                logFocusService = logFocusService,
                pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                pullRequestIngestionStateMachineProvider = pullRequestIngestionStateMachineProvider,
                repoAccessService = repoAccessService,
                repoCodeIngestionService = repoCodeIngestionService,
                repoPullRequestSummaryService = repoPullRequestSummaryService,
                repoTopicIngestionService = repoTopicIngestionService,
                scmCloneUrlProvider = scmCloneUrlProvider,
                scmRepoApiFactory = scmRepoApiFactory,
                summarizationEventEnqueueService = summarizationEventEnqueueService,
                topicEventEnqueueService = topicEventEnqueueService,
                topicExpertService = topicExpertService,
                topicMappingService = topicMappingService,
                triageEventEnqueueService = triageEventEnqueueService,
            )
            threadRoutes(
                summarizationEventEnqueueService = summarizationEventEnqueueService,
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                embeddingEventEnqueueService = embeddingEventEnqueueService,
                topicMappingService = topicMappingService,
            )
            pullRequestRoutes(
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                pullRequestIngestionStateMachineProvider = pullRequestIngestionStateMachineProvider,
                scmRepoApiFactory = scmRepoApiFactory,
                triageEventEnqueueService = triageEventEnqueueService,
            )
            redirectFromTeamToOrgRoutes()
        }
    }
}

@Suppress("LongMethod")
private fun Route.orgRoutes(
    atlassianDataCenterAuthProvider: AtlassianDataCenterAuthProvider,
    billingEventEnqueueService: BillingEventEnqueueService,
    billingService: BillingService,
    ciLicenseService: CILicenseService,
    ciProjectApiFactory: CIProjectApiFactory,
    clientConfigService: ClientConfigService,
    codaApiProvider: CodaApiProvider?,
    codaTokenProvider: CodaTokenProvider,
    codaPagePersistenceService: CodaPagePersistenceService,
    codaEventEnqueueService: CodaEventEnqueueService,
    confluenceCloudApiProvider: ConfluenceCloudApiProvider,
    confluenceAtlassianTokenProvider: AtlassianTokenProvider,
    confluenceDataCenterApiProvider: ConfluenceDataCenterApiProvider,
    confluenceEventEnqueueService: ConfluenceEventEnqueueService,
    conversationAnalysisService: ConversationAnalysisService,
    dataSourcePresetConfigurationService: DataSourcePresetConfigurationService,
    embeddingEncoding: EmbeddingEncoding,
    embeddingEventEnqueueService: EmbeddingEventEnqueueService,
    embeddingExperimentService: EmbeddingExperimentService,
    embeddingStoreFacade: EmbeddingStoreFacade,
    embeddingStatsFacade: EmbeddingStatsFacade,
    enableTestBillingActions: Boolean,
    googleApiProvider: GoogleApiProvider,
    googleCredentialProvider: GoogleCredentialProvider?,
    indexingAndEmbeddingService: IndexingAndEmbeddingService,
    inferenceService: MLInferenceService,
    inferenceTemplateService: MLInferenceTemplateService,
    ingestionDisablementService: IngestionDisablementService,
    insiderService: InsiderServiceInterface,
    jiraApiProvider: JiraApiProvider,
    jiraAtlassianTokenProvider: AtlassianTokenProvider,
    jiraIssuePersistenceService: JiraIssuePersistenceService,
    linearEventEnqueueService: LinearEventEnqueueService,
    linkProcessorFactory: LinkProcessorFactory,
    logFocusService: LogFocusService,
    machineLearningApiProvider: MachineLearningApiProvider,
    migrationEventEnqueueService: MigrationEventEnqueueService,
    mlFunctionService: MLFunctionService,
    notificationEventEnqueueService: NotificationEventEnqueueService,
    notionApiProvider: NotionApiProvider?,
    onboardingEnablementService: OnboardingEnablementService,
    orgDeletionService: OrgDeletionService,
    peerInviteSuggestionService: PeerInviteSuggestionService,
    productFeedbackService: ProductFeedbackService,
    profileService: ProfileService,
    pullRequestEventEnqueueService: PullRequestEventEnqueueService,
    pullRequestIngestionStateMachineProvider: PullRequestIngestionStateMachineProvider,
    rapidQueryService: RapidQueryService,
    repoAccessService: RepoAccessService,
    repoCodeIngestionService: RepoCodeIngestionService,
    repoFocusService: RepoFocusService,
    repoPullRequestSummaryService: RepoPullRequestSummaryService,
    repoTopicIngestionService: RepoTopicIngestionService,
    sampleQuestionGenerator: SampleQuestionGeneratorImpl,
    sampleQuestionGeneratorService: SampleQuestionGeneratorService,
    scmCloneUrlProvider: ScmCloneUrlProvider,
    scmEventProducer: ScmEventProducer,
    scmRepoApiFactory: ScmRepoApiFactory,
    scmTeamApiFactory: ScmTeamApiFactory,
    scmTeamLifecycleMaintenance: ScmTeamLifecycleMaintenance,
    searchPriorityEventEnqueueService: SearchPriorityEventEnqueueService,
    semanticDocumentRetriever: SemanticDocumentRetriever,
    semanticSearchQueryService: SemanticSearchQueryService,
    stackOverflowApiProvider: StackOverflowTeamsApiProvider,
    stackOverflowAuthProvider: StackOverflowTeamsAuthProvider,
    summarizationEventEnqueueService: SummarizationEventEnqueueService,
    topicEventEnqueueService: TopicEventEnqueueService,
    topicExpertService: TopicExpertService,
    topicMappingService: TopicMappingService,
    triageEventEnqueueService: TriageEventEnqueueService,
    installationDeletionService: StandardInstallationDeletionService,
    userSecretServiceResolver: UserSecretServiceResolver,
    webIngestionService: WebIngestionService,
) {
    route("/orgs") {
        get {
            renderOrgsPage(
                page = AdminPage.Orgs,
                insiderService = insiderService,
                ingestionDisablementService = ingestionDisablementService,
            )
        }
        route("/{orgId}") {
            get {
                renderOrgPage(
                    page = AdminPage.Org,
                    enableTestBillingActions = enableTestBillingActions,
                )
            }
            get("/getVectorCounts") { getVectorCounts(embeddingStatsFacade = embeddingStatsFacade) }
            get("/getActiveUsersInRange") { getActiveUsersInRange() }
            get("/getOrgStats") { getOrgStats() }
            get("/getOrgActivity") { getOrgActivity() }

            postAction("/updateSubscribedReleaseChannel") { updateSubscribedReleaseChannel() }
            postAction("/updateOrgCapability") {
                updateOrgCapability(
                    clientConfigService = clientConfigService,
                )
            }
            postAction("/enableOrg") { enableOrg(onboardingEnablementService) }
            postAction("/disableOrg") { disableOrg(onboardingEnablementService) }
            postAction("/updateOrgSettings") { updateOrgSettings() }
            postAction("/updateOrgOnboardingStates") { updateOrgOnboardingStates() }
            postAction("/updateSlackSettings") { updateSlackSettings() }
            postAction("/updateOrgProxy") { updateOrgProxy() }
            postAction("/deleteOrgProxy") { deleteOrgProxy() }
            postAction("/deleteOrg") { deleteOrg(orgDeletionService) }
            postAction("/deleteOrgPermanently") { deleteOrgPermanently(orgDeletionService) }
            postAction("/undeleteOrg") { undeleteOrg() }
            postAction("/updateTrialEndDate") { updateTrialEndDate() }
            postAction("/setQuarterlyStartDate") { setQuarterlyStartDate() }
            postAction("/resetBilling") { resetBilling(billingService) }
            postAction("/clearLastExtendedOn") { clearLastExtendedOn() }
            postAction("/downgradeCapabilities") { downgradeCapabilities(billingEventEnqueueService) }
            postAction("/sendBusinessPlanConfirmationEmail") { sendBusinessPlanConfirmationEmail(notificationEventEnqueueService) }
            postAction("/sendAssignLicensesEmail") { sendAssignLicensesEmail(notificationEventEnqueueService) }
            postAction("/scheduleSeatUpgrade") { scheduleSeatUpgrade(billingService, billingEventEnqueueService) }
            postAction("/scheduleSeatDowngrade") { scheduleSeatDowngrade(billingService, billingEventEnqueueService) }
            postAction("/scheduleSubscriptionCancellation") { scheduleSubscriptionCancellation(billingService) }
            postAction("/updatePlan") { updatePlan(billingService) }
            postAction("/regenerateSocialNetwork") { regenerateSocialNetwork() }
            postAction("/generateUserActivityReport") { generateUserActivityReport() }

            route("/analytics") {
                get { renderOrgAnalyticsPage(AdminPage.OrgAnalytics) }
            }
            route("/slackConnectConversion") {
                get { renderSlackConnectConversionPage(AdminPage.AnalyticsSlackConnectConversion) }
            }
            route("/answers") {
                get { renderOrgAnswersPage(AdminPage.OrgAnswers) }
            }
            route("/machineLearning") {
                route("/slackAutoAnswers") {
                    get { renderSlackAutoAnswerPage(AdminPage.OrgAutoAnswers) }
                    route("/details") {
                        get { renderSlackAutoAnswerDetailsPage(AdminPage.OrgAutoAnswers) }
                        postAction("/addToDataset") {
                            addToRegressionDataset()
                        }
                    }
                }
            }

            route("/productFeedback") {
                get { renderOrgProductFeedbackPage(AdminPage.OrgProductFeedback) }
                postAction("/download") { downloadOrgProductFeedback(productFeedbackService = productFeedbackService) }
                route("/{productFeedbackResponseId}") {
                    postAction("/delete", redirectPath = "/productFeedback") { deleteProductFeedbackResponse() }
                }
                route("/member/{orgMemberId}") {
                    postAction("/resetDeferral", redirectPath = "/productFeedback") { resetProductFeedbackDeferral() }
                }
            }

            ciTriagesRoutes(
                ciProjectApiFactory = ciProjectApiFactory,
                logFocusService = logFocusService,
                triageEventEnqueueService = triageEventEnqueueService,
            )
            mlRoutes(
                conversationAnalysisService = conversationAnalysisService,
                dataSourcePresetConfigurationService = dataSourcePresetConfigurationService,
                embeddingEncoding = embeddingEncoding,
                embeddingStoreFacade = embeddingStoreFacade,
                inferenceService = inferenceService,
                inferenceTemplateService = inferenceTemplateService,
                insiderService = insiderService,
                linkProcessorFactory = linkProcessorFactory,
                machineLearningApiProvider = machineLearningApiProvider,
                mlFunctionService = mlFunctionService,
                searchPriorityEventEnqueueService = searchPriorityEventEnqueueService,
                semanticDocumentRetriever = semanticDocumentRetriever,
                semanticSearchQueryService = semanticSearchQueryService,
                topicEventEnqueueService = topicEventEnqueueService,
                topicExpertService = topicExpertService,
                embeddingStatsFacade = embeddingStatsFacade,
            )
            orgInstallationRoutes(
                atlassianDataCenterAuthProvider = atlassianDataCenterAuthProvider,
                ciLicenseService = ciLicenseService,
                ciProjectApiFactory = ciProjectApiFactory,
                codaApiProvider = codaApiProvider,
                codaTokenProvider = codaTokenProvider,
                codaPagePersistenceService = codaPagePersistenceService,
                codaEventEnqueueService = codaEventEnqueueService,
                confluenceCloudApiProvider = confluenceCloudApiProvider,
                confluenceAtlassianTokenProvider = confluenceAtlassianTokenProvider,
                confluenceDataCenterApiProvider = confluenceDataCenterApiProvider,
                confluenceEventEnqueueService = confluenceEventEnqueueService,
                embeddingEventEnqueueService = embeddingEventEnqueueService,
                googleApiProvider = googleApiProvider,
                googleCredentialProvider = googleCredentialProvider,
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                inferenceTemplateService = inferenceTemplateService,
                jiraApiProvider = jiraApiProvider,
                jiraAtlassianTokenProvider = jiraAtlassianTokenProvider,
                jiraIssuePersistenceService = jiraIssuePersistenceService,
                linearEventEnqueueService = linearEventEnqueueService,
                logFocusService = logFocusService,
                notificationEventEnqueueService = notificationEventEnqueueService,
                notionApiProvider = notionApiProvider,
                peerInviteSuggestionService = peerInviteSuggestionService,
                productFeedbackService = productFeedbackService,
                profileService = profileService,
                pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                pullRequestIngestionStateMachineProvider = pullRequestIngestionStateMachineProvider,
                rapidQueryService = rapidQueryService,
                repoAccessService = repoAccessService,
                repoCodeIngestionService = repoCodeIngestionService,
                repoFocusService = repoFocusService,
                repoPullRequestSummaryService = repoPullRequestSummaryService,
                repoTopicIngestionService = repoTopicIngestionService,
                sampleQuestionGenerator = sampleQuestionGenerator,
                sampleQuestionGeneratorService = sampleQuestionGeneratorService,
                scmCloneUrlProvider = scmCloneUrlProvider,
                scmRepoApiFactory = scmRepoApiFactory,
                stackOverflowApiProvider = stackOverflowApiProvider,
                stackOverflowAuthProvider = stackOverflowAuthProvider,
                summarizationEventEnqueueService = summarizationEventEnqueueService,
                topicEventEnqueueService = topicEventEnqueueService,
                topicExpertService = topicExpertService,
                topicMappingService = topicMappingService,
                triageEventEnqueueService = triageEventEnqueueService,
                userSecretServiceResolver = userSecretServiceResolver,
                webIngestionService = webIngestionService,
            )
            orgMemberRoutes(
                ciLicenseService = ciLicenseService,
                ciProjectApiFactory = ciProjectApiFactory,
                embeddingEventEnqueueService = embeddingEventEnqueueService,
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                logFocusService = logFocusService,
                notificationEventEnqueueService = notificationEventEnqueueService,
                peerInviteSuggestionService = peerInviteSuggestionService,
                profileService = profileService,
                productFeedbackService = productFeedbackService,
                pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                pullRequestIngestionStateMachineProvider = pullRequestIngestionStateMachineProvider,
                repoAccessService = repoAccessService,
                repoCodeIngestionService = repoCodeIngestionService,
                repoFocusService = repoFocusService,
                repoPullRequestSummaryService = repoPullRequestSummaryService,
                repoTopicIngestionService = repoTopicIngestionService,
                sampleQuestionGenerator = sampleQuestionGenerator,
                sampleQuestionGeneratorService = sampleQuestionGeneratorService,
                scmCloneUrlProvider = scmCloneUrlProvider,
                scmRepoApiFactory = scmRepoApiFactory,
                summarizationEventEnqueueService = summarizationEventEnqueueService,
                templateService = inferenceTemplateService,
                topicEventEnqueueService = topicEventEnqueueService,
                topicExpertService = topicExpertService,
                topicMappingService = topicMappingService,
                triageEventEnqueueService = triageEventEnqueueService,
            )
            vectorRoutes(embeddingStatsFacade = embeddingStatsFacade)
            socialNetworkRoutes()
            threadRoutes(
                summarizationEventEnqueueService = summarizationEventEnqueueService,
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                embeddingEventEnqueueService = embeddingEventEnqueueService,
                topicMappingService = topicMappingService,
            )
            ssoRoutes()
            teamRoutes(
                ciLicenseService = ciLicenseService,
                ciProjectApiFactory = ciProjectApiFactory,
                embeddingEventEnqueueService = embeddingEventEnqueueService,
                embeddingExperimentService = embeddingExperimentService,
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                inferenceTemplateService = inferenceTemplateService,
                ingestionDisablementService = ingestionDisablementService,
                insiderService = insiderService,
                logFocusService = logFocusService,
                migrationEventEnqueueService = migrationEventEnqueueService,
                notificationEventEnqueueService = notificationEventEnqueueService,
                peerInviteSuggestionService = peerInviteSuggestionService,
                productFeedbackService = productFeedbackService,
                profileService = profileService,
                pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                pullRequestIngestionStateMachineProvider = pullRequestIngestionStateMachineProvider,
                repoAccessService = repoAccessService,
                repoCodeIngestionService = repoCodeIngestionService,
                repoFocusService = repoFocusService,
                repoPullRequestSummaryService = repoPullRequestSummaryService,
                repoTopicIngestionService = repoTopicIngestionService,
                sampleQuestionGenerator = sampleQuestionGenerator,
                sampleQuestionGeneratorService = sampleQuestionGeneratorService,
                scmCloneUrlProvider = scmCloneUrlProvider,
                scmEventProducer = scmEventProducer,
                scmRepoApiFactory = scmRepoApiFactory,
                scmTeamApiFactory = scmTeamApiFactory,
                scmTeamLifecycleMaintenance = scmTeamLifecycleMaintenance,
                summarizationEventEnqueueService = summarizationEventEnqueueService,
                topicEventEnqueueService = topicEventEnqueueService,
                topicExpertService = topicExpertService,
                topicMappingService = topicMappingService,
                triageEventEnqueueService = triageEventEnqueueService,
                installationDeletionService = installationDeletionService,
            )
        }
    }
}

private fun Route.loginRoutes(
    adminAuthenticationProvider: AdminAuthenticationProvider,
    googleVerifier: GoogleVerifier?,
    passwordVerifier: PasswordVerifier?,
) {
    route("/login") {
        get { renderLoginPage(AdminPage.Login, googleVerifier, passwordVerifier) }
        googleVerifier?.also {
            route("/authorize") {
                post { handleLoginCallback(googleVerifier, adminAuthenticationProvider) }
            }
        }
        passwordVerifier?.also {
            route("/password") {
                post { handlePasswordLogin(passwordVerifier, adminAuthenticationProvider) }
            }
        }
    }
}

private fun Route.impersonateRoutes() {
    route("/impersonate") {
        get { renderImpersonatePage() }
        postAction("/start") { startImpersonating() }
        postAction("/stop") { stopImpersonating() }
    }
}

private fun Route.queuesRoutes() {
    route("/queues") {
        get {
            renderQueuesPage(
                page = AdminPage.Queues,
            )
        }
        get("/flushDLQ") { flushDLQ() }
    }
}

private fun Route.databaseDeletionsRoutes() {
    route("/databaseDeletions") {
        get {
            renderDatabaseDeletionsPage(
                page = AdminPage.DatabaseDeletions,
            )
        }
        get("/columnsForModel") { columnsForModel() }
        sse("/events") { events() }
        postAction("/start") { startDelete() }
    }
}

private fun Route.vectorRoutes(embeddingStatsFacade: EmbeddingStatsFacade) {
    route("/vectors") {
        get { renderVectorsPage(page = AdminPage.Vectors, embeddingStatsFacade = embeddingStatsFacade) }
    }
}

private fun Route.versionRoutes(
    versionService: VersionService,
    insiderService: InsiderServiceInterface,
) {
    route("/versions") {
        get {
            renderVersionsPage(
                page = AdminPage.Versions,
                versionService = versionService,
                insiderService = insiderService,
            )
        }
        get("/ingestNewBuild") { ingestNewBuild(versionService) }
        route("/{versionId}") {
            get { renderVersionPage(AdminPage.Version, versionService) }
            postAction("/updateReleaseChannels") { updateReleaseChannels(versionService) }
            postAction("/makeObsolete") { makeVersionObsolete(versionService) }
            postAction("/downloadInstaller") { downloadInstaller(versionService) }
            postAction("/downloadJetbrains") { downloadJetbrains(versionService) }
            postAction("/updateDescription") { updateVersionDescription(versionService) }
        }
        postAction("/updateSubscribedReleaseChannelForPerson/{personId}") { updateSubscribedReleaseChannelForPerson() }
        postAction("/updateSubscribedReleaseChannelForOrg/{orgId}") { updateSubscribedReleaseChannelForOrg() }
    }
}

private fun Route.socialNetworkRoutes() {
    route("/socialNetwork") {
        get { renderSocialNetworkPage(AdminPage.OrgSocialNetwork) }
    }
}

private fun Route.redirectFromTeamToOrgRoutes() {
    route("/inferences") {
        route("{inferenceId}") {
            get {
                val teamId = call.parameters.optionalScmTeamId() ?: return@get call.respond(HttpStatusCode.NotFound)
                val org = scmTeamStore.getOrgIdOrNull(teamId) ?: return@get call.respond(HttpStatusCode.NotFound)

                val inferenceId = call.parameters.optionalInferenceId() ?: return@get call.respond(HttpStatusCode.NotFound)
                call.respondRedirect("$WEB_ROOT/orgs/$org/machineLearning/inferences/$inferenceId")
            }
        }
    }
    route("/threads") {
        route("{threadId}") {
            get {
                val teamId = call.parameters.optionalScmTeamId() ?: return@get call.respond(HttpStatusCode.NotFound)
                val org = scmTeamStore.getOrgIdOrNull(teamId) ?: return@get call.respond(HttpStatusCode.NotFound)

                val threadId = call.parameters["threadId"] ?: return@get call.respond(HttpStatusCode.NotFound)
                call.respondRedirect("$WEB_ROOT/orgs/$org/threads/$threadId")
            }
        }
    }
}

private fun Route.memberRoutes(
    ciLicenseService: CILicenseService,
    ciProjectApiFactory: CIProjectApiFactory,
    embeddingEventEnqueueService: EmbeddingEventEnqueueService,
    indexingAndEmbeddingService: IndexingAndEmbeddingService,
    logFocusService: LogFocusService,
    notificationEventEnqueueService: NotificationEventEnqueueService,
    peerInviteSuggestionService: PeerInviteSuggestionService,
    productFeedbackService: ProductFeedbackService,
    profileService: ProfileService,
    pullRequestEventEnqueueService: PullRequestEventEnqueueService,
    pullRequestIngestionStateMachineProvider: PullRequestIngestionStateMachineProvider,
    repoAccessService: RepoAccessService,
    repoCodeIngestionService: RepoCodeIngestionService,
    repoFocusService: RepoFocusService,
    repoPullRequestSummaryService: RepoPullRequestSummaryService,
    repoTopicIngestionService: RepoTopicIngestionService,
    sampleQuestionGenerator: SampleQuestionGeneratorImpl,
    sampleQuestionGeneratorService: SampleQuestionGeneratorService,
    scmCloneUrlProvider: ScmCloneUrlProvider,
    scmRepoApiFactory: ScmRepoApiFactory,
    summarizationEventEnqueueService: SummarizationEventEnqueueService,
    templateService: MLInferenceTemplateService,
    topicEventEnqueueService: TopicEventEnqueueService,
    topicExpertService: TopicExpertService,
    topicMappingService: TopicMappingService,
    triageEventEnqueueService: TriageEventEnqueueService,
) {
    route("/members") {
        get { renderMembersPage(page = AdminPage.OrgMembers) }
        route("/{memberId}") {
            get {
                renderMemberPage(
                    page = AdminPage.OrgMember,
                    repoFocusService = repoFocusService,
                    productFeedbackService = productFeedbackService,
                    ciLicenseService = ciLicenseService,
                )
            }
            get("/mcpToken") { getMcpToken() }
            postAction("/alignMemberForPerson") { alignMemberForPerson() }
            postAction("/alignOrgMemberForPerson") { alignOrgMemberForPerson() }
            postAction("/archiveLowRelevanceThreads") { archiveLowRelevanceThreadsForMember() }
            postAction("/clearMemberInstallationSuppressions") { clearMemberInstallationSuppressions() }
            postAction("/clearSenderInvites") { clearSenderInvites() }
            postAction("/clearUserConnectPromptHistory") { clearUserConnectPromptHistory() }
            postAction("/createUserEngagement") { createUserEngagement() }
            postAction("/disassociateIdentityPerson") { disassociateIdentityPerson() }
            postAction("/disassociatePrimaryMember") { disassociatePrimaryMember() }
            postAction("/promptToReconnect") { promptToReconnect() }
            postAction("/refreshEmailForMemberIfNeeded") { refreshEmailForMemberIfNeeded(profileService) }
            postAction("/removeFromTeamInviteFollowUpEmail") { removeFromTeamInviteFollowUpEmail(notificationEventEnqueueService) }
            postAction("/resetQuestionsAsked") { resetQuestionsAsked() }
            postAction("/sendConnectIntegrationsReminderEmail") { sendConnectIntegrationsReminderEmail(notificationEventEnqueueService) }
            postAction("/sendFirstQuestionReminderEmail") { sendFirstQuestionReminderEmail(notificationEventEnqueueService) }
            postAction("/sendInviteEmail") { sendInviteEmail(notificationEventEnqueueService) }
            postAction("/sendInviteTeamReminderEmail") { sendInviteTeamReminderEmail(notificationEventEnqueueService) }
            postAction("/sendNthMemberStartEmail") { sendNthMemberStartEmail(notificationEventEnqueueService) }
            postAction("/sendPitchEmail") { sendPitchEmail(notificationEventEnqueueService) }
            postAction("/sendProcessingCompleteEmail") { sendProcessingCompleteEmail(notificationEventEnqueueService) }
            postAction("/sendRepoFollowupEmail") { sendRepoFollowupEmail(notificationEventEnqueueService) }
            postAction("/sendSuggestedPeersInvitee") { sendSuggestedPeersInviteeEmail(peerInviteSuggestionService) }
            postAction("/sendTeamInviteEmail") { sendTeamInviteEmail(notificationEventEnqueueService) }
            postAction("/sendTeamInviteFollowUpEmail") { sendTeamInviteFollowUpEmail(notificationEventEnqueueService) }
            postAction("/sendThreadInviteEmail") { sendThreadInviteEmail(notificationEventEnqueueService) }
            postAction("/sendThreadInviteJoinEmail") { sendThreadInviteJoinEmail(notificationEventEnqueueService) }
            postAction("/sendTrialOneDayEmail") { sendTrialOneDayEmail(notificationEventEnqueueService) }
            postAction("/sendTrialOneWeekEmail") { sendTrialOneWeekEmail(notificationEventEnqueueService) }
            postAction("/sendTrialStartEmail") { sendTrialStartEmail(notificationEventEnqueueService) }
            postAction("/sendTrialSurveyEmail") { sendTrialSurveyEmail(notificationEventEnqueueService) }
            postAction("/sendTrialThreeDaysEmail") { sendTrialThreeDaysEmail(notificationEventEnqueueService) }
            postAction("/sendTrialTwoWeeksEmail") { sendTrialTwoWeeksEmail(notificationEventEnqueueService) }
            postAction("/sendUserAddedConfirmationEmail") { sendUserAddedConfirmationEmail(notificationEventEnqueueService) }
            postAction("/sendLicenseRequestedEmail") { sendLicenseRequestedEmail(notificationEventEnqueueService) }
            postAction("/startOnboardedCampaign") { startOnboardedCampaign(notificationEventEnqueueService) }
            postAction("/toggleIgnoreThreads") { toggleIgnoreThreads() }
            postAction("/updateOrgMemberOnboardingStates") { updateOrgMemberOnboardingStates() }
            postAction("/updateOrgMemberRole") { updateOrgMemberRole() }
            postAction("/updateProviderStates") { updateProviderStates() }

            route("/answers") {
                get { renderOrgAnswersPage(AdminPage.OrgAnswers) }
            }

            threadRoutes(
                summarizationEventEnqueueService = summarizationEventEnqueueService,
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                embeddingEventEnqueueService = embeddingEventEnqueueService,
                topicMappingService = topicMappingService,
            )
            unreadRoutes()
            repoRoutes(
                ciProjectApiFactory = ciProjectApiFactory,
                embeddingEventEnqueueService = embeddingEventEnqueueService,
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                logFocusService = logFocusService,
                pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                pullRequestIngestionStateMachineProvider = pullRequestIngestionStateMachineProvider,
                repoAccessService = repoAccessService,
                repoCodeIngestionService = repoCodeIngestionService,
                repoPullRequestSummaryService = repoPullRequestSummaryService,
                repoTopicIngestionService = repoTopicIngestionService,
                scmCloneUrlProvider = scmCloneUrlProvider,
                scmRepoApiFactory = scmRepoApiFactory,
                summarizationEventEnqueueService = summarizationEventEnqueueService,
                topicEventEnqueueService = topicEventEnqueueService,
                topicExpertService = topicExpertService,
                topicMappingService = topicMappingService,
                triageEventEnqueueService = triageEventEnqueueService,
            )
            pullRequestRoutes(
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                pullRequestIngestionStateMachineProvider = pullRequestIngestionStateMachineProvider,
                scmRepoApiFactory = scmRepoApiFactory,
                triageEventEnqueueService = triageEventEnqueueService,
            )
            sampleQuestionsGeneratorRoutes(
                sampleQuestionGenerator = sampleQuestionGenerator,
                templateService = templateService,
            )
            sampleQuestionRoutes(
                sampleQuestionGeneratorService = sampleQuestionGeneratorService,
            )
        }
    }
}

private fun Route.orgMemberRoutes(
    ciLicenseService: CILicenseService,
    ciProjectApiFactory: CIProjectApiFactory,
    embeddingEventEnqueueService: EmbeddingEventEnqueueService,
    indexingAndEmbeddingService: IndexingAndEmbeddingService,
    logFocusService: LogFocusService,
    notificationEventEnqueueService: NotificationEventEnqueueService,
    peerInviteSuggestionService: PeerInviteSuggestionService,
    profileService: ProfileService,
    productFeedbackService: ProductFeedbackService,
    pullRequestEventEnqueueService: PullRequestEventEnqueueService,
    pullRequestIngestionStateMachineProvider: PullRequestIngestionStateMachineProvider,
    repoAccessService: RepoAccessService,
    repoCodeIngestionService: RepoCodeIngestionService,
    repoFocusService: RepoFocusService,
    repoPullRequestSummaryService: RepoPullRequestSummaryService,
    repoTopicIngestionService: RepoTopicIngestionService,
    sampleQuestionGenerator: SampleQuestionGeneratorImpl,
    sampleQuestionGeneratorService: SampleQuestionGeneratorService,
    scmCloneUrlProvider: ScmCloneUrlProvider,
    scmRepoApiFactory: ScmRepoApiFactory,
    summarizationEventEnqueueService: SummarizationEventEnqueueService,
    templateService: MLInferenceTemplateService,
    topicEventEnqueueService: TopicEventEnqueueService,
    topicExpertService: TopicExpertService,
    topicMappingService: TopicMappingService,
    triageEventEnqueueService: TriageEventEnqueueService,
) {
    route("/orgMembers") {
        get { renderOrgMembersPage(page = AdminPage.OrgMembers) }

        route("/{memberId}") {
            get {
                renderMemberPage(
                    page = AdminPage.OrgMember,
                    repoFocusService = repoFocusService,
                    productFeedbackService = productFeedbackService,
                    ciLicenseService = ciLicenseService,
                )
            }
            get("/mcpToken") { getMcpToken() }
            postAction("/archiveLowRelevanceThreads") { archiveLowRelevanceThreadsForMember() }
            postAction("/clearMemberInstallationSuppressions") { clearMemberInstallationSuppressions() }
            postAction("/clearSenderInvites") { clearSenderInvites() }
            postAction("/clearUserConnectPromptHistory") { clearUserConnectPromptHistory() }
            postAction("/createUserEngagement") { createUserEngagement() }
            postAction("/disassociatePrimaryMember") { disassociatePrimaryMember() }
            postAction("/promptToReconnect") { promptToReconnect() }
            postAction("/refreshEmailForMemberIfNeeded") { refreshEmailForMemberIfNeeded(profileService) }
            postAction("/removeFromTeamInviteFollowUpEmail") { removeFromTeamInviteFollowUpEmail(notificationEventEnqueueService) }
            postAction("/sendInviteEmail") { sendInviteEmail(notificationEventEnqueueService) }
            postAction("/sendProcessingCompleteEmail") { sendProcessingCompleteEmail(notificationEventEnqueueService) }
            postAction("/sendRepoFollowupEmail") { sendRepoFollowupEmail(notificationEventEnqueueService) }
            postAction("/sendSuggestedPeersInvitee") { sendSuggestedPeersInviteeEmail(peerInviteSuggestionService) }
            postAction("/sendTeamInviteEmail") { sendTeamInviteEmail(notificationEventEnqueueService) }
            postAction("/sendTeamInviteFollowUpEmail") { sendTeamInviteFollowUpEmail(notificationEventEnqueueService) }
            postAction("/sendThreadInviteEmail") { sendThreadInviteEmail(notificationEventEnqueueService) }
            postAction("/sendThreadInviteJoinEmail") { sendThreadInviteJoinEmail(notificationEventEnqueueService) }
            postAction("/startOnboardedCampaign") { startOnboardedCampaign(notificationEventEnqueueService) }
            postAction("/toggleIgnoreThreads") { toggleIgnoreThreads() }
            postAction("/updateOrgMemberOnboardingStates") { updateOrgMemberOnboardingStates() }
            postAction("/updateOrgMemberRole") { updateOrgMemberRole() }
            postAction("/updateProviderStates") { updateProviderStates() }

            threadRoutes(
                summarizationEventEnqueueService = summarizationEventEnqueueService,
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                embeddingEventEnqueueService = embeddingEventEnqueueService,
                topicMappingService = topicMappingService,
            )
            unreadRoutes()
            repoRoutes(
                ciProjectApiFactory = ciProjectApiFactory,
                embeddingEventEnqueueService = embeddingEventEnqueueService,
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                logFocusService = logFocusService,
                pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                pullRequestIngestionStateMachineProvider = pullRequestIngestionStateMachineProvider,
                repoAccessService = repoAccessService,
                repoCodeIngestionService = repoCodeIngestionService,
                repoPullRequestSummaryService = repoPullRequestSummaryService,
                repoTopicIngestionService = repoTopicIngestionService,
                scmCloneUrlProvider = scmCloneUrlProvider,
                scmRepoApiFactory = scmRepoApiFactory,
                summarizationEventEnqueueService = summarizationEventEnqueueService,
                topicEventEnqueueService = topicEventEnqueueService,
                topicExpertService = topicExpertService,
                topicMappingService = topicMappingService,
                triageEventEnqueueService = triageEventEnqueueService,
            )
            sampleQuestionsGeneratorRoutes(
                sampleQuestionGenerator = sampleQuestionGenerator,
                templateService = templateService,
            )
            sampleQuestionRoutes(
                sampleQuestionGeneratorService = sampleQuestionGeneratorService,
            )
        }
    }
}

private fun Route.unreadRoutes() {
    route("/unreads") {
        get { renderUnreadsPage(AdminPage.OrgUnreads) }
        route("/{unreadId}") {
            postAction("/delete") { deleteThreadUnread() }
        }
    }
}

private fun Route.repoRoutes(
    ciProjectApiFactory: CIProjectApiFactory,
    embeddingEventEnqueueService: EmbeddingEventEnqueueService,
    indexingAndEmbeddingService: IndexingAndEmbeddingService,
    logFocusService: LogFocusService,
    pullRequestEventEnqueueService: PullRequestEventEnqueueService,
    pullRequestIngestionStateMachineProvider: PullRequestIngestionStateMachineProvider,
    repoAccessService: RepoAccessService,
    repoCodeIngestionService: RepoCodeIngestionService,
    repoPullRequestSummaryService: RepoPullRequestSummaryService,
    repoTopicIngestionService: RepoTopicIngestionService,
    scmCloneUrlProvider: ScmCloneUrlProvider,
    scmRepoApiFactory: ScmRepoApiFactory,
    sourceMarkStore: SourceMarkStore = Stores.sourceMarkStore,
    summarizationEventEnqueueService: SummarizationEventEnqueueService,
    topicEventEnqueueService: TopicEventEnqueueService,
    topicExpertService: TopicExpertService,
    topicMappingService: TopicMappingService,
    triageEventEnqueueService: TriageEventEnqueueService,
) {
    route("/repos") {
        get { renderReposPage(AdminPage.ScmRepos, repoAccessService = repoAccessService) }
        route("/{repoId}") {
            get { renderRepoPage(AdminPage.ScmRepo) }
            get("/browse") { renderRepoBrowsePage(AdminPage.ScmRepoBrowse, scmRepoApiFactory) }
            get("/search") { renderRepoSearchPage(AdminPage.ScmRepoSearch, scmRepoApiFactory) }
            get("/diff") { renderRepoDiffPage(scmRepoApiFactory) }
            route("/commits") {
                get { renderRepoCommitsPage(AdminPage.ScmRepo, scmRepoApiFactory) }
                get("/{sha}") { renderRepoCommitPage(AdminPage.ScmRepo, scmRepoApiFactory) }
            }
            get("/getRepoCloneUrl") { getRepoCloneUrl(scmCloneUrlProvider) }
            get("/getRepoHeadCommit") { getRepoHeadCommit(scmRepoApiFactory) }
            postAction("/clearCalculatedSourcePointsForRepo") { clearCalculatedSourcePointsForRepo(sourceMarkStore) }
            postAction("/reingest") { triggerRepoPrIngestion() }
            postAction("/unmarkCodeIngestForRepo") { unmarkCodeIngestForRepo(repoCodeIngestionService) }
            postAction("/unmarkPullRequestIngestForRepo") { unmarkPullRequestIngestForRepo(repoPullRequestSummaryService) }
            postAction("/unmarkTopicIngestForRepo") { unmarkTopicIngestForRepo(repoTopicIngestionService) }
            postAction("/bulkreingest") { triggerRepoBulkPrIngestion() }
            postAction("/rerunBulkGitHubIssueIngest") { rerunBulkGitHubIssueIngest() }
            postAction("/togglebulkingestFlag") { togglebulkingestFlag() }
            postAction("/toggleIngestionCompleteFlag") { toggleIngestionCompleteFlag() }
            postAction("/toggleslackingestionFlag") { toggleSlackIngestionFlag() }
            postAction("/toggleIsCiEnabledFlag") { toggleIsCiEnabledFlag() }
            postAction("/reingestIngestionIncompletePullRequests") {
                reingestIngestionIncompletePullRequests(
                    pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                )
            }
            postAction("/triggerRepoPullRequestIngestion", redirectPath = "/pullRequests") {
                triggerRepoPullRequestIngestion(
                    scmCloneUrlProvider = scmCloneUrlProvider,
                    pullRequestIngestionStateMachineProvider = pullRequestIngestionStateMachineProvider,
                )
            }
            ciTriagesRoutes(
                ciProjectApiFactory = ciProjectApiFactory,
                logFocusService = logFocusService,
                triageEventEnqueueService = triageEventEnqueueService,
            )
            threadRoutes(
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                embeddingEventEnqueueService = embeddingEventEnqueueService,
                summarizationEventEnqueueService = summarizationEventEnqueueService,
                topicMappingService = topicMappingService,
            )
            sourcemarkRoutes()
            pullRequestRoutes(
                indexingAndEmbeddingService = indexingAndEmbeddingService,
                pullRequestEventEnqueueService = pullRequestEventEnqueueService,
                pullRequestIngestionStateMachineProvider = pullRequestIngestionStateMachineProvider,
                scmRepoApiFactory = scmRepoApiFactory,
                triageEventEnqueueService = triageEventEnqueueService,
            )
            pullRequestRawRoutes(
                scmRepoApiFactory = scmRepoApiFactory,
            )
            topicsRoutes(
                topicEventEnqueueService = topicEventEnqueueService,
                topicExpertService = topicExpertService,
            )
        }
    }
}

private fun Route.ciTriagesRoutes(
    ciProjectApiFactory: CIProjectApiFactory,
    logFocusService: LogFocusService,
    triageEventEnqueueService: TriageEventEnqueueService,
) {
    fun Route.ciPostActions() {
        postAction("/ciFetch") {
            ciFetch(
                ciProjectApiFactory = ciProjectApiFactory,
            )
        }
        postAction("/buildGetLogs") {
            buildGetLogs(
                ciProjectApiFactory = ciProjectApiFactory,
            )
        }
        postAction("/buildJobGetLogs") {
            buildJobGetLogs(
                ciProjectApiFactory = ciProjectApiFactory,
            )
        }
        postAction("/buildJobGetFocusLogs") {
            buildJobGetFocusLogs(
                ciProjectApiFactory = ciProjectApiFactory,
                logFocusService = logFocusService,
            )
        }
        postAction("/buildJobGetAnnotations") {
            buildJobGetAnnotations(
                ciProjectApiFactory = ciProjectApiFactory,
            )
        }
        postAction("/triageRefresh") {
            triageRefresh(
                triageEventEnqueueService = triageEventEnqueueService,
            )
        }
        postAction("/triageRerun") { triageRerun() }
        postAction("/triageRender") { triageRender() }
    }

    get("/ciAnalytics") { renderAnalyticsCiPage(AdminPage.AnalyticsCi) }

    route("/builds") {
        get { renderCiBuildsPage(AdminPage.CiBuilds) }
        route("{buildId}") {
            get { renderCiBuildPage(AdminPage.CiBuild) }
            ciPostActions()
        }
    }

    route("/triages") {
        get { renderCiTriagesPage(AdminPage.CiTriages) }
        route("{triageId}") {
            get { renderCiTriagePage(AdminPage.CiTriage) }
            route("/triageEphemeralReRun") {
                handle { renderCiTriageEphemeralReRunPage(AdminPage.CiTriage) }
                get("/run") { triageEphemeralReRun() }
            }
            ciPostActions()
            postAction("/triageReview") { triageReview() }
        }
    }
}

private fun Route.scmInstallationsRoutes() {
    Provider.scmProviders.forEach { provider ->
        route("/$provider") {
            get { redirectToParent() }
            route("{installationId}") {
                get {
                    val orgId = call.parameters.orgId()
                    val scmTeam = scmTeamStore.findByInstallationId(
                        orgId = orgId,
                        installationId = call.parameters.optionalInstallationId().required(),
                    )
                        ?: return@get call.respond(HttpStatusCode.NotFound)

                    call.respondRedirect(
                        "$WEB_ROOT/orgs/$orgId/teams/${scmTeam.id}",
                    )
                }
            }
        }
    }
}

private fun Route.ciInstallationsRoutes(
    ciProjectApiFactory: CIProjectApiFactory,
    logFocusService: LogFocusService,
    triageEventEnqueueService: TriageEventEnqueueService,
) {
    Provider.ciProviders.forEach { provider ->
        route("/$provider") {
            get { redirectToParent() }
            route("{ciInstallationId}") {
                get { renderCiProviderPage(AdminPage.CiProvider) }
                get("/token") { getCiProviderToken() }
                ciTriagesRoutes(
                    ciProjectApiFactory = ciProjectApiFactory,
                    logFocusService = logFocusService,
                    triageEventEnqueueService = triageEventEnqueueService,
                )
                redirectFromCiInstallationToScmTeamRoutes()

                postAction("/ciScmModeUpdate") {
                    ciScmModeUpdate()
                }
            }
        }
    }
}

private fun Route.redirectFromCiInstallationToScmTeamRoutes() {
    route("/pullRequests") {
        get("{pullRequestId}") {
            val orgId = call.parameters.optionalOrgId() ?: return@get call.respond(HttpStatusCode.NotFound)
            val pullRequest = call.parameters.optionalPullRequest() ?: return@get call.respond(HttpStatusCode.NotFound)
            val repo = pullRequest.repo() ?: return@get call.respond(HttpStatusCode.NotFound)

            call.respondRedirect(
                "$WEB_ROOT/orgs/$orgId/teams/${repo.teamId}/repos/${repo.id}/pullRequests/${pullRequest.id}",
            )
        }
    }
}

private fun Route.mlRoutersRoute(
    mlRouterStore: MLRouterStore = Stores.mlRouterStore,
) {
    route("/MLRouters") {
        get { renderMLRoutersPage(AdminPage.OrgMLRouters, mlRouterStore) }
        postAction("/createMLRouter") { createNewMLRouter(mlRouterStore) }
        route("{mlRouterId}") {
            get { renderMLRouterPage(AdminPage.OrgMLRouters) }
            postAction("/updateMLRouter") { updateMLRouter(mlRouterStore) }
            postAction("/deleteMLRouter") { deleteMLRouter(mlRouterStore) }
        }
    }
}

private fun Route.pullRequestRoutes(
    indexingAndEmbeddingService: IndexingAndEmbeddingService,
    pullRequestEventEnqueueService: PullRequestEventEnqueueService,
    pullRequestIngestionStateMachineProvider: PullRequestIngestionStateMachineProvider,
    scmRepoApiFactory: ScmRepoApiFactory,
    triageEventEnqueueService: TriageEventEnqueueService,
) {
    route("/pullRequests") {
        get { renderPullRequests(AdminPage.OrgPullRequests, pullRequestIngestionStateMachineProvider) }
        route("{pullRequestId}") {
            get { renderPullRequestPage(AdminPage.OrgPullRequest) }
            get("/diff") { renderPullRequestDiffPage(scmRepoApiFactory) }
            route("/commits") {
                get { renderPullRequestCommitsPage(scmRepoApiFactory) }
                get("/{sha}") { renderPullRequestCommitPage(scmRepoApiFactory) }
            }
            postAction("/reindex") { reindexForPullRequest(indexingAndEmbeddingService) }
            postAction("/reingest") { reingestForPullRequest(pullRequestEventEnqueueService) }
            postAction("/triageRefresh") {
                triageRefresh(
                    triageEventEnqueueService = triageEventEnqueueService,
                )
            }
            postAction("/triageRender") { triageRender() }
            postAction("/triageRerun") { triageRerun() }
            postAction("/triageDelete") {
                triageDelete(
                    triageEventEnqueueService = triageEventEnqueueService,
                )
            }
            postAction("/triageMuteToggle") {
                triageMuteToggle()
            }
        }
    }
}

private fun Route.pullRequestRawRoutes(
    scmRepoApiFactory: ScmRepoApiFactory,
) {
    route("/pullRequests") {
        route("/raw") {
            get { renderPullRequestsRawPage(scmRepoApiFactory) }
            route("{prNumber}") {
                get { renderPullRequestRawPage(scmRepoApiFactory) }
                route("/reviews") {
                    get { renderPullRequestRawReviewsPage(scmRepoApiFactory) }
                }
                route("/comments") {
                    get { renderPullRequestRawReviewCommentsPage(scmRepoApiFactory) }
                }
                route("/files") {
                    get { renderPullRequestRawFilesPage(scmRepoApiFactory) }
                }
                route("/diff") {
                    get { renderPullRequestRawDiffPage(scmRepoApiFactory) }
                }
            }
            route("/latest") {
                get { renderPullRequestRawLatestPage(scmRepoApiFactory) }
            }
        }
    }
}

private fun Route.threadRoutes(
    summarizationEventEnqueueService: SummarizationEventEnqueueService,
    indexingAndEmbeddingService: IndexingAndEmbeddingService,
    embeddingEventEnqueueService: EmbeddingEventEnqueueService,
    topicMappingService: TopicMappingService,
) {
    route("/threads") {
        get { renderThreadsPage(AdminPage.OrgThreads) }
        route("{threadId}") {
            get { renderThreadPage(AdminPage.OrgThread) }
            postAction("/summarizeThread") { summarizeThread(summarizationEventEnqueueService) }
            postAction("/reindexThread") { reindexThread(indexingAndEmbeddingService) }
            postAction("/generateThreadTopics") { generateThreadTopics(topicMappingService = topicMappingService) }
            postAction("/deleteThreadTopics") { deleteThreadTopics() }
            postAction("/deleteThread") { deleteThread(embeddingEventEnqueueService) }
            messageRoutes()
            sourcemarkRoutes()
            unreadRoutes()
        }
        route("thread") {
            get { renderThreadPage(AdminPage.OrgThread) }
        }
    }
}

private fun Route.semanticSearchRoutes(
    semanticSearchQueryService: SemanticSearchQueryService,
    inferenceTemplateService: MLInferenceTemplateService,
    dataSourcePresetConfigurationService: DataSourcePresetConfigurationService,
) {
    route("/semanticSearch") {
        get {
            renderSemanticSearchPage(
                page = AdminPage.OrgAskQuestion,
                semanticSearchQueryService = semanticSearchQueryService,
                templateService = inferenceTemplateService,
                dataSourcePresetConfigurationService = dataSourcePresetConfigurationService,
            )
        }
    }
}

private fun Route.documentRetrievalRoutes(
    templateService: MLInferenceTemplateService,
    embeddingStoreFacade: EmbeddingStoreFacade,
    embeddingEncoding: EmbeddingEncoding,
    semanticDocumentRetriever: SemanticDocumentRetriever,
    dataSourcePresetConfigurationService: DataSourcePresetConfigurationService,
) {
    route("/documents") {
        get { renderDocumentRetrievalPage(page = AdminPage.OrgDocumentRetrieval, templateService = templateService) }
        get("/queryDocuments") {
            retrieveDocuments(
                templateService = templateService,
                semanticDocumentRetriever = semanticDocumentRetriever,
                dataSourcePresetConfigurationService = dataSourcePresetConfigurationService,
            )
        }
        get("{documentKey}") {
            renderDocumentPage(page = AdminPage.OrgDocument, embeddingStoreFacade = embeddingStoreFacade, embeddingEncoding = embeddingEncoding)
        }
    }
}

private fun Route.linkRetrievalRoutes(
    linkProcessorFactory: LinkProcessorFactory,
) {
    route("/links") {
        get { renderLinkRetrievalPage(page = AdminPage.OrgLinkRetrieval) }
        get("/queryDocuments") {
            retrieveLinkedDocuments(linkProcessorFactory = linkProcessorFactory)
        }
    }
}

private fun Route.conversationAnalysisRoutes(conversationAnalysisService: ConversationAnalysisService) {
    route("/conversationAnalysis") {
        get { renderConversationAnalysisPage(AdminPage.ConversationAnalysisAdminPage) }
        postAction("/analyze", redirectPath = "/conversationAnalysis") {
            analyzeFeedback(conversationAnalysisService = conversationAnalysisService)
        }
        route("{analysisId}") {
            get {
                renderConversationAnalysisDetailsPage(AdminPage.ConversationAnalysisDetailsAdminPage)
            }
            postAction("/downloadFeedbackAnalysis") {
                downloadFeedbackAnalysis()
            }
            postAction("/triggerClustering") {
                triggerClustering(conversationAnalysisService = conversationAnalysisService)
            }
            postAction("/editAnalysis") {
                editAnalysis()
            }
            postAction("/createExecutiveSummary") {
                createExecutiveSummary(conversationAnalysisService = conversationAnalysisService)
            }
            postAction("/delete", redirectPath = "/conversationAnalysis") {
                deleteAnalysis(conversationAnalysisService = conversationAnalysisService)
            }
        }
    }
}

private fun Route.feedbackRoutes(
    insiderService: InsiderServiceInterface,
) {
    route("/feedback") {
        get {
            renderMessageFeedbackPage(
                page = AdminPage.MLFeedback,
                insiderService = insiderService,
            )
        }
        postAction("/downloadFeedback") {
            downloadFeedback(
                insiderService = insiderService,
            )
        }
    }
}

private fun Route.sampleQuestionsGeneratorRoutes(
    sampleQuestionGenerator: SampleQuestionGeneratorImpl,
    templateService: MLInferenceTemplateService,
) {
    route("/sampleQuestionGenerator") {
        get { renderSampleQuestionGeneratorPage(page = AdminPage.OrgSampleQuestionGenerator, templateService = templateService) }
        get("/generate") { generateSampleQuestions(sampleQuestionGenerator, templateService = templateService) }
    }
}

private fun Route.sampleQuestionRoutes(
    sampleQuestionGeneratorService: SampleQuestionGeneratorService,
) {
    route("/sampleQuestions") {
        get { renderSampleQuestionPage(page = AdminPage.OrgSampleQuestionGenerator) }
        get("/generate") { regenerateSampleQuestionsForMember(sampleQuestionGeneratorService = sampleQuestionGeneratorService) }
    }
}

private fun Route.messageRoutes() {
    route("/messages") {
        get { renderMessagesPage(AdminPage.OrgMessages) }
        route("{messageId}") {
            get { renderMessagePage(AdminPage.OrgMessage) }
        }
    }
}

private fun Route.sourcemarkRoutes() {
    route("/sourcemarks") {
        get { renderSourceMarksPage(AdminPage.OrgSourceMarks) }
        route("{sourcemarkId}") {
            get { renderSourceMarkPage(AdminPage.OrgSourceMark) }
        }
    }
}

@Suppress("LongMethod")
private fun Route.topicsRoutes(
    topicEventEnqueueService: TopicEventEnqueueService,
    topicExpertService: TopicExpertService,
) {
    route("/topics") {
        get { renderTopicsPage(AdminPage.OrgTopics) }

        route("{topicId}") {
            get { renderTopicPage(AdminPage.OrgTopic) }
            postAction("/approveTopic") {
                approveExistingTopic(topicEventEnqueueService = topicEventEnqueueService)
            }
            postAction("/updateDescription") {
                updateDescription()
            }
            postAction("/triggerTopicExpertMapping") {
                triggerTopicExpertMapping(topicExpertService = topicExpertService)
            }
            postAction("/triggerTopicMapping") {
                triggerTopicMapping(topicEventEnqueueService = topicEventEnqueueService)
            }
        }
    }
}

private fun Route.inferenceExampleRoutes(
    inferenceService: MLInferenceService,
    inferenceTemplateService: MLInferenceTemplateService,
    embeddingStoreFacade: EmbeddingStoreFacade,
    embeddingEncoding: EmbeddingEncoding,
    machineLearningApiProvider: MachineLearningApiProvider,
) {
    route("/inferences") {
        get { renderInferencesPage(AdminPage.OrgInferenceExamples) }
        postAction("/moveToGolden") { moveToGolden(inferenceService) }
        postAction("/moveToValidation") { moveToValidation(inferenceService) }
        postAction("/moveToUnlabeled") { moveToUnlabeled(inferenceService) }
        route("{inferenceId}") {
            get {
                renderInferenceExamplePage(
                    page = AdminPage.OrgInferenceExample,
                    templateService = inferenceTemplateService,
                )
            }
            route("/evals") {
                get {
                    renderEvalsPage(
                        templateService = inferenceTemplateService,
                        machineLearningApiProvider = machineLearningApiProvider,
                        page = AdminPage.OrgEvals,
                    )
                }
            }
            route("/documents") {
                get {
                    call.respondRedirect(call.request.uri.removeSuffix("/documents"))
                }
                get("{reference}") {
                    renderDocumentPage(
                        page = AdminPage.OrgDocument,
                        embeddingStoreFacade = embeddingStoreFacade,
                        embeddingEncoding = embeddingEncoding,
                    )
                }
            }
            route("/removeFromGlobal") {
                get {
                    removeInferenceFromGlobalList(inferenceService)
                }
            }

            postAction("/moveToGolden") { moveToGolden(inferenceService) }
            postAction("/moveToValidation") { moveToValidation(inferenceService) }
            postAction("/moveToUnlabeled") { moveToUnlabeled(inferenceService) }
            postAction("/updateResponse") { updateInferenceResponse(inferenceService) }
            postAction("/updateFeedback") { updateInferenceFeedback(inferenceService) }
            postAction("/updateInferenceLabel") { updateInferenceLabel(inferenceService) }
            postAction("/updateIsGlobalRegressionInference") { updateIsGlobalRegressionInference(inferenceService) }
            postAction("/createOrUpdateIsQuestionAnswerRegressionTest") { createOrUpdateIsQuestionAnswerRegressionTest() }
            postAction("/createOrUpdateIsQuestionRegressionTest") { createOrUpdateIsQuestionRegressionTest() }
        }
    }

    // Maintain backwards compatibility with old route
    route("/inferenceExamples") {
        route("{inferenceId}") {
            get {
                val orgId = call.parameters.orgId()
                val inferenceId = call.parameters.optionalInferenceId() ?: return@get call.respond(HttpStatusCode.NotFound)
                call.respondRedirect("$WEB_ROOT/orgs/$orgId/machineLearning/inferences/$inferenceId")
            }
        }
    }
}

private fun Route.linearOrganizationRoutes(
    linearEventEnqueueService: LinearEventEnqueueService,
) {
    route("/Linear") {
        get { renderLinearOrganizationsPage(AdminPage.OrgLinearOrganizations) }
        route("{linearInstallationId}") {
            get { renderLinearOrganizationPage(AdminPage.OrgLinearOrganization) }
            route("/linearIngestions") {
                get { renderLinearIngestionsPage(AdminPage.OrgLinearIngestions) }
            }
            route("/linearTeams") {
                get { renderLinearTeamsPage(AdminPage.OrgLinearTeams) }
                route("{linearTeamId}") {
                    get { renderLinearTeamPage(AdminPage.OrgLinearTeam) }
                    route("/linearTeamIngestions") {
                        get { renderLinearTeamIngestionsPage(AdminPage.OrgLinearTeamIngestions) }
                        postAction("/reingestLinearTeam") { reingestLinearTeam() }
                        postAction("/reembedLinearTeam") { reembedLinearTeam(linearEventEnqueueService) }
                    }
                }
            }
        }
    }
}

private fun Route.awsRoutes() {
    route("/aws") {
        get { renderAWSAccountInstallationsPage(AdminPage.OrgAWSAccountInstallations) }
        postAction("/createDemoAWSAccountInstallation") { createDemoAWSAccountInstallation() }
        route("{awsAccountInstallationId}") {
            get { renderAWSAccountInstallationPage(AdminPage.OrgAWSAccountInstallation) }
            postAction(actionPath = "/deleteAWSAccountInstallation", redirectPath = "/aws") { deleteAWSAccountInstallation() }
            postAction("/updateAWSAccountInstallation") { updateAWSAccountInstallation() }
        }
    }
}

private fun Route.ssoRoutes() {
    route("/saml") {
        get { renderSingleSignOnPage(AdminPage.OrgSingleSignOn) }
        route("/domains") {
            postAction(actionPath = "/createDomain") { createDomain() }
            route("{domainId}") {
                postAction(actionPath = "/verifyDomain") { verifyDomain() }
                postAction(actionPath = "/unverifyDomain") { unverifyDomain() }
                postAction(actionPath = "/deleteDomain") { deleteDomain() }
            }
        }
        route("{samlIdpMetadataId}") {
            get { renderSamlIdpMetadataPage(AdminPage.OrgSamlMetadata) }
            postAction(actionPath = "/deleteSamlIdpMetadata", redirectPath = "/saml") { deleteSamlIdpMetadata() }
            postAction(actionPath = "/updateSamlIdpMetadataSettings") { updateSamlIdpMetadataSettings() }
        }
    }
}

private fun Route.unblockedRoutes() {
    route("/unblocked") {
        route("{unblockedInstallationId}") {
            get { renderUnblockedInstallationPage(AdminPage.OrgUnblocked) }
        }
    }
}

private fun Route.slackRoutes(userSecretServiceResolver: UserSecretServiceResolver) {
    route("/Slack") {
        get { redirectToParent() }
        route("{slackInstallationId}") {
            get { renderSlackInstallationPage(AdminPage.OrgSlack) }
            postAction("/runSlackIngestionForAllChannels") { runSlackIngestionForAllChannels() }
            slackTeamRoutes(userSecretServiceResolver = userSecretServiceResolver)
        }
    }
}

private fun Route.asanaWorkspaceRoutes() {
    route("/Asana") {
        get { renderAsanaWorkspacesPage(AdminPage.OrgAsanaInstallations) }
        route("/{asanaInstallationId}") {
            postAction("/forceIngestProjects") { forceReIngestAsanaProjects() }
            get { renderAsanaWorkspacePage(AdminPage.OrgAsanaInstallation) }
        }
    }
}

private fun Route.slackTeamRoutes(
    userSecretServiceResolver: UserSecretServiceResolver,
) {
    route("/slackTeams") {
        get { renderSlackTeamsPage(AdminPage.OrgSlackTeams) }
        route("{slackTeamId}") {
            get { renderSlackTeamPage(AdminPage.OrgSlackTeam) }
            get("getAuthedUserToken") { getAuthedUserToken(userSecretServiceResolver = userSecretServiceResolver) }
            get("getBotToken") { getBotToken(userSecretServiceResolver = userSecretServiceResolver) }

            postAction("/discardSlackPrivateUserScopes") { discardSlackPrivateUserScopes() }
            postAction("/discardSlackCommandScopes") { discardSlackCommandScopes() }

            route("/slackChannels") {
                get { renderSlackChannelsPage(AdminPage.OrgSlackChannels) }
                route("{slackChannelId}") {
                    get { renderSlackChannelPage(AdminPage.OrgSlackChannel) }
                    route("/slackChannelMembers") {
                        get { renderSlackChannelMembersPage(AdminPage.OrgSlackChannelMembers) }
                    }
                }
            }
            route("/slackIngestions") {
                get { renderSlackIngestionsPage(AdminPage.OrgSlackIngestions) }

                route("{slackIngestionId}") {
                    get { renderSlackIngestionPage(AdminPage.OrgSlackIngestion) }
                    route("/slackChannelIngestions") {
                        get { renderSlackChannelIngestionsPage(AdminPage.OrgSlackChannelIngestions) }
                    }
                }
            }
        }
    }
}

private fun Route.jiraSiteRoutes(
    jiraApiProvider: JiraApiProvider,
    jiraAtlassianTokenProvider: AtlassianTokenProvider,
    atlassianDataCenterAuthProvider: AtlassianDataCenterAuthProvider,
    jiraIssuePersistenceService: JiraIssuePersistenceService,
    rapidQueryService: RapidQueryService,
) {
    val build: Route.() -> Unit = {
        get { renderJiraSitesPage(AdminPage.OrgJiraSites) }
        route("{jiraInstallationId}") {
            route("/issues") {
                get("{jiraIssueId}") {
                    renderJiraIssuePage(
                        page = AdminPage.OrgJiraIssue,
                        jiraIssuePersistenceService = jiraIssuePersistenceService,
                        rapidQueryService = rapidQueryService,
                    )
                }
            }
            get("/getJiraApiToken") {
                getJiraApiToken(
                    jiraAtlassianTokenProvider = jiraAtlassianTokenProvider,
                    dataCenterAuthProvider = atlassianDataCenterAuthProvider,
                )
            }
            get { renderJiraSitePage(AdminPage.OrgJiraSite) }
            postAction("/reingestJiraSite") { reingestJiraSite() }
            route("/getProjects") { get { getProjects(jiraApiProvider, jiraAtlassianTokenProvider) } }
        }
    }

    route("/Jira", build)
    route("/JiraDataCenter", build)
}

private fun Route.confluenceSiteRoutes(
    confluenceCloudApiProvider: ConfluenceCloudApiProvider,
    confluenceAtlassianTokenProvider: AtlassianTokenProvider,
    atlassianDataCenterAuthProvider: AtlassianDataCenterAuthProvider,
    confluenceDataCenterApiProvider: ConfluenceDataCenterApiProvider,
    confluenceEventEnqueueService: ConfluenceEventEnqueueService,
) {
    val build: Route.() -> Unit = {
        get { renderConfluenceSitesPage(AdminPage.OrgConfluence) }
        route("{installationId}") {
            get { renderConfluenceSitePage(AdminPage.OrgConfluence) }
            postAction("/reingestConfluenceSite") { reingestConfluenceSite() }
            postAction("/reingestConfluenceSiteFromCreatedAt") { reingestConfluenceSiteFromCreatedAt() }
            postAction("/clearNextUrls") { clearNextUrls() }
            postAction("/updateIgnoredTitleTags") { updateIgnoredTitleTags() }
            postAction("/toggleIngestPersonalSpaces") { toggleIngestPersonalSpaces() }
            postAction("/syncSpaceContent") { syncSpaceContent(confluenceEventEnqueueService) }
            postAction("/upsertConfluenceMembers") { upsertConfluenceMembers() }
            route("/getSpaces") { get { getSpaces(confluenceCloudApiProvider, confluenceAtlassianTokenProvider) } }
            route("/getConfluencePage") {
                get {
                    getConfluencePage(confluenceCloudApiProvider, confluenceAtlassianTokenProvider, confluenceDataCenterApiProvider)
                }
            }
            route("/ingestPages") { get { ingestPages(confluenceEventEnqueueService) } }
            get("/getConfluenceApiToken") { getConfluenceApiToken(confluenceAtlassianTokenProvider, atlassianDataCenterAuthProvider) }
        }
    }

    route("/Confluence", build)
    route("/ConfluenceDataCenter", build)
}

private fun Route.stackOverflowRoutes(
    stackOverflowApiProvider: StackOverflowTeamsApiProvider,
    stackOverflowAuthProvider: StackOverflowTeamsAuthProvider,
) {
    route("/StackOverflowTeams") {
        get { renderStackOverflowInstallationsPage(AdminPage.OrgStackOverflow) }
        route("{stackOverflowInstallationId}") {
            get { renderStackOverflowInstallationPage(AdminPage.OrgStackOverflow) }
            postAction("/reingestStackOverflow") { reingestStackOverflow() }
            route("/getQuestions") { get { getQuestions(stackOverflowApiProvider, stackOverflowAuthProvider) } }
        }
    }
}

private fun Route.notionRoutes(
    notionApiProvider: NotionApiProvider?,
    cache: NotionIngestionCache,
) {
    route("/Notion") {
        get { renderNotionInstallationsPage(AdminPage.OrgNotion) }
        route("{installationId}") {
            get { renderNotionInstallationPage(AdminPage.OrgNotion) }
            postAction("/reingestNotion") { reingestNotion(cache) }
            postAction("/clearNotionNextCursor") { clearNotionNextCursor() }
            postAction("/updateIgnoredTitleTags") { updateIgnoredTitleTags() }
            route("/ingestNotionPage") { get { ingestNotionPage(cache) } }
            notionApiProvider?.let {
                get("/getNotionApiToken") { getNotionApiToken(notionApiProvider) }
                route("/searchNotionPages") { get { searchNotionPages(notionApiProvider) } }
                route("/searchNotionDatabases") { get { searchNotionDatabases(notionApiProvider) } }
                route("/getNotionDatabase") { get { getNotionDatabase(notionApiProvider) } }
                route("/queryNotionDatabase") { get { queryNotionDatabase(notionApiProvider) } }
                route("/getNotionPage") { get { getNotionPage(notionApiProvider) } }
                route("/getBlock") { get { getBlock(notionApiProvider) } }
                route("/getBlockChildren") { get { getBlockChildren(notionApiProvider) } }
                route("/getNotionUser") { get { getNotionUser(notionApiProvider) } }
                route("/getBlockAndChildren") { get { getBlockAndChildren(notionApiProvider) } }
            }
        }
    }
}

private fun Route.googleRoutes(
    googleApiProvider: GoogleApiProvider,
    googleCredentialProvider: GoogleCredentialProvider?,
) {
    val build: Route.() -> Unit = {
        get { renderGoogleDriveInstallationsPage(AdminPage.OrgGoogle) }
        route("{googleInstallationId}") {
            get { renderGoogleDriveInstallationPage(AdminPage.OrgGoogle) }
            postAction("/reingestGoogle") { reingestGoogle() }
            if (googleCredentialProvider != null) {
                route("/getSharedDrives") { get { getSharedDrives(googleApiProvider, googleCredentialProvider) } }
                route("/getAccessibleFiles") { get { getAccessibleFiles(googleApiProvider, googleCredentialProvider) } }
                route("/getFile") { get { getFile(googleApiProvider, googleCredentialProvider) } }
            }
        }
    }

    route("/GoogleDrive", build)
    route("/GoogleDriveWorkspace", build)
}

private fun Route.codaRoutes(
    codaApiProvider: CodaApiProvider?,
    tokenProvider: CodaTokenProvider,
    codaPagePersistenceService: CodaPagePersistenceService,
    rapidQueryService: RapidQueryService,
    codaEventEnqueueService: CodaEventEnqueueService,
    ingestionProgressServiceProvider: IngestionProgressServiceProvider = StandardIngestionProgressServiceProvider(),
) {
    route("/Coda") {
        get { redirectToParent() }
        route("{codaInstallationId}") {
            route("/pages") {
                get("{codaPageId}") {
                    renderCodaPagePage(
                        page = AdminPage.OrgCodaPage,
                        codaPagePersistenceService = codaPagePersistenceService,
                        rapidQueryService = rapidQueryService,
                    )
                }
            }
            get { renderCodaInstallationPage(AdminPage.OrgCoda, ingestionProgressServiceProvider) }
            postAction("/reingestCoda") { reingestCoda() }
            postAction("/extractCoda") { extractCoda(codaEventEnqueueService) }
            get("/getCodaApiToken") { getCodaApiToken(tokenProvider = tokenProvider) }
            codaApiProvider?.let {
                route("/listPagesForDoc") { get { listPagesForDoc(tokenProvider, codaApiProvider) } }
            }
        }
    }
}

@Suppress("UnusedPrivateMember")
private fun Route.webIngestionRoutes(
    embeddingEventEnqueueService: EmbeddingEventEnqueueService,
    webIngestionService: WebIngestionService,
    ingestionProgressServiceProvider: IngestionProgressServiceProvider = StandardIngestionProgressServiceProvider(),
) {
    route("/Web") {
        get { renderWebIngestionInstallationsPage(AdminPage.OrgWebIngestion, ingestionProgressServiceProvider) }
        route("{webInstallationId}") {
            get { renderWebIngestionSitesPage(AdminPage.OrgWebIngestion) }
            postAction("/reingestSites") {
                reingestSites(
                    webIngestionService = webIngestionService,
                )
            }
            postAction("/addWebIngestionSite") {
                addWebIngestionSite(
                    webIngestionService = webIngestionService,
                )
            }

            route("/site/{webIngestionId}") {
                get { renderWebIngestionSitePage(AdminPage.OrgWebIngestion) }
                postAction("/reingestSite") { reingestSite(webIngestionService) }
                postAction("/dropExcludedUrlsForSite") {
                    dropExcludedUrlsForSite(
                        embeddingEventEnqueueService = embeddingEventEnqueueService,
                        webIngestionConfig = webIngestionService.webIngestionConfig,
                    )
                }
                postAction("/toggleDisableSite") { toggleDisableSite() }
            }
        }
    }
}

private suspend fun RoutingContext.redirectToParent() {
    call.respondRedirect(
        call.request.path().substringBeforeLast("/"),
    )
}

private fun Route.postAction(
    actionPath: String,
    redirectPath: String? = null,
    block: suspend RoutingContext.() -> Unit,
) {
    post(actionPath) {
        block()

        if (call.response.isCommitted) {
            return@post
        }

        val path = call.request.path()

        val location = redirectPath?.let { path.substringBefore(it) + it }
            ?: call.request.referrer()
            ?: path.removeSuffix(actionPath)

        call.respondRedirect(location)
    }
}
