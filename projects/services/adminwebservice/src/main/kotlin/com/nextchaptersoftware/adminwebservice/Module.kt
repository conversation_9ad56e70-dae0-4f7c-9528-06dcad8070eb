package com.nextchaptersoftware.adminwebservice

import com.aallam.openai.client.OpenAIHost
import com.nextchaptersoftware.access.RestrictedAccessServiceFactory
import com.nextchaptersoftware.activemq.ActiveMQConsumer
import com.nextchaptersoftware.activemq.ActiveMQProducer
import com.nextchaptersoftware.adminwebservice.auth.AdminAuthenticationProvider
import com.nextchaptersoftware.adminwebservice.auth.AdminSessionIdentityResolver
import com.nextchaptersoftware.adminwebservice.auth.google.GoogleVerifier
import com.nextchaptersoftware.adminwebservice.auth.password.PasswordVerifier
import com.nextchaptersoftware.adminwebservice.config.AdminConfig
import com.nextchaptersoftware.adminwebservice.jobs.BootstrappingPromotionJob
import com.nextchaptersoftware.adminwebservice.jobs.BuildIngestionJob
import com.nextchaptersoftware.adminwebservice.jobs.GrowthMetricsJob
import com.nextchaptersoftware.adminwebservice.jobs.IngestionProgressCheckerJob
import com.nextchaptersoftware.adminwebservice.jobs.IntercomInternalUserAttributeJob
import com.nextchaptersoftware.adminwebservice.jobs.MigrationEventProcessingJob
import com.nextchaptersoftware.adminwebservice.jobs.OnboardingAlarmJob
import com.nextchaptersoftware.adminwebservice.jobs.OrgBillingJob
import com.nextchaptersoftware.adminwebservice.jobs.PersonPreferencesMetricsJob
import com.nextchaptersoftware.adminwebservice.jobs.PullRequestIngestionStatusJob
import com.nextchaptersoftware.adminwebservice.jobs.RepoCleanupJob
import com.nextchaptersoftware.adminwebservice.jobs.SlackChannelPreferencesMetricsJob
import com.nextchaptersoftware.adminwebservice.jobs.UserEngagementMetricsJob
import com.nextchaptersoftware.adminwebservice.plugins.StatusPages.configureStatusPages
import com.nextchaptersoftware.adminwebservice.plugins.configureDatabaseData
import com.nextchaptersoftware.adminwebservice.plugins.configureRouting
import com.nextchaptersoftware.adminwebservice.plugins.configureSecurity
import com.nextchaptersoftware.adminwebservice.plugins.configureTracing
import com.nextchaptersoftware.anthropic.api.AnthropicApiConfiguration
import com.nextchaptersoftware.anthropic.api.AnthropicApiProvider
import com.nextchaptersoftware.atlassian.api.AtlassianAuthApiImpl
import com.nextchaptersoftware.atlassian.api.AtlassianDataCenterAuthProvider
import com.nextchaptersoftware.atlassian.api.NoopAtlassianAuthApi
import com.nextchaptersoftware.atlassian.services.AtlassianTokenProvider
import com.nextchaptersoftware.auth.oauth.NoopOAuthTokenRefresher
import com.nextchaptersoftware.auth.secret.oauth.EncryptedTokenPersistence
import com.nextchaptersoftware.auth.secret.oauth.UnencryptedTokenPersistence
import com.nextchaptersoftware.auth.secret.oauth.UserSecretOAuthRefreshService
import com.nextchaptersoftware.aws.bedrock.anthropic.api.BedrockAnthropicCompletionsApi
import com.nextchaptersoftware.aws.bedrock.cohere.api.BedrockCohereRerankApi
import com.nextchaptersoftware.aws.bedrockruntime.BedrockRuntimeAsyncProviderFactory
import com.nextchaptersoftware.aws.bedrockruntime.BedrockRuntimeProviderFactory
import com.nextchaptersoftware.aws.bedrockruntime.StandardBedrockRuntimeAsyncProviderFactory
import com.nextchaptersoftware.aws.bedrockruntime.StandardBedrockRuntimeProviderFactory
import com.nextchaptersoftware.aws.client.AWSClientProvider
import com.nextchaptersoftware.aws.extensions.StringExtensions.toRegion
import com.nextchaptersoftware.aws.lambda.LambdaProviderFactory
import com.nextchaptersoftware.aws.lambda.StandardLambdaProviderFactory
import com.nextchaptersoftware.aws.rpc.StsProviderViaRpc
import com.nextchaptersoftware.aws.s3.StandardS3ProviderFactory
import com.nextchaptersoftware.aws.sfn.SfnProviderFactory
import com.nextchaptersoftware.aws.sfn.StandardSfnProviderFactory
import com.nextchaptersoftware.aws.sfn.statemachine.StandardStateMachineProvider
import com.nextchaptersoftware.aws.sqs.SqsMessageSizeValidator
import com.nextchaptersoftware.billing.events.queue.enqueue.BillingEventEnqueueService
import com.nextchaptersoftware.billing.services.BillingService
import com.nextchaptersoftware.billing.utils.OrgBillingAccessService
import com.nextchaptersoftware.bot.services.InstallationBotAccountService
import com.nextchaptersoftware.ci.CILicenseService
import com.nextchaptersoftware.ci.CIProjectApiFactory
import com.nextchaptersoftware.ci.config.CIConfig
import com.nextchaptersoftware.ci.config.CISecretsConfig
import com.nextchaptersoftware.ci.enqueue.TriageEventEnqueueService
import com.nextchaptersoftware.ci.logging.LogFocusService
import com.nextchaptersoftware.coda.api.CodaApiProvider
import com.nextchaptersoftware.coda.data.services.CodaPagePersistenceService
import com.nextchaptersoftware.coda.events.queue.enqueue.CodaEventEnqueueService
import com.nextchaptersoftware.coda.services.CodaTokenProvider
import com.nextchaptersoftware.cohere.api.CohereApiConfiguration
import com.nextchaptersoftware.cohere.api.CohereApiProvider
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.config.ServiceInitializer
import com.nextchaptersoftware.confluence.api.ConfluenceCloudApiProvider
import com.nextchaptersoftware.confluence.api.ConfluenceDataCenterApiProvider
import com.nextchaptersoftware.confluence.enqueue.ConfluenceEventEnqueueService
import com.nextchaptersoftware.conversationanalysis.ConversationAnalysisService
import com.nextchaptersoftware.crypto.AESCryptoSystem
import com.nextchaptersoftware.crypto.RSAClientServerCryptoSystem
import com.nextchaptersoftware.crypto.RSACryptoSystem
import com.nextchaptersoftware.data.preset.DataSourcePresetConfigurationServiceImpl
import com.nextchaptersoftware.datasources.ConfluenceAccessService
import com.nextchaptersoftware.datasources.JiraAccessService
import com.nextchaptersoftware.datasources.ThreadAccessService
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.diff.utils.DiffChunker
import com.nextchaptersoftware.documents.encryption.DocumentDataEncryption
import com.nextchaptersoftware.documents.encryption.config.DocumentEncryptionConfig
import com.nextchaptersoftware.dsac.filter.DataSourceAccessControlFilterFactory
import com.nextchaptersoftware.dsac.provider.CodaDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.ConfluenceDataCenterDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.ConfluenceDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.GoogleDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.JiraAccessComputeService
import com.nextchaptersoftware.dsac.provider.JiraDataCenterDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.JiraDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.LinearAccessComputeService
import com.nextchaptersoftware.dsac.provider.NotionDocumentAccessProvider
import com.nextchaptersoftware.embedding.config.EmbeddingSecretConfig
import com.nextchaptersoftware.embedding.encoding.EmbeddingEncoding
import com.nextchaptersoftware.embedding.events.queue.enqueue.EmbeddingEventEnqueueService
import com.nextchaptersoftware.embedding.service.stats.EmbeddingStatsFacade
import com.nextchaptersoftware.embedding.service.store.EmbeddingStoreFacade
import com.nextchaptersoftware.event.queue.dequeue.SequentialBatchEventDequeue
import com.nextchaptersoftware.event.queue.dequeue.StandardEventMessageProcessor
import com.nextchaptersoftware.event.queue.enqueue.StandardEventEnqueueService
import com.nextchaptersoftware.event.queue.payloads.EventPayloadCompressor
import com.nextchaptersoftware.experts.TopicExpertService
import com.nextchaptersoftware.gemini.api.GeminiApiProvider
import com.nextchaptersoftware.gemini.config.GeminiApiConfig
import com.nextchaptersoftware.google.api.GoogleApiProvider
import com.nextchaptersoftware.google.services.GoogleCredentialProvider
import com.nextchaptersoftware.google.services.GoogleWorkspaceServiceAccountKeyProvider
import com.nextchaptersoftware.ingestion.pipeline.config.IngestionPipelineConfig
import com.nextchaptersoftware.ingestion.redis.IngestionDisablementService
import com.nextchaptersoftware.insider.InsiderService
import com.nextchaptersoftware.insight.index.PullRequestInsightIndexContentService
import com.nextchaptersoftware.insight.index.ThreadInsightIndexContentService
import com.nextchaptersoftware.insight.refresh.InsightRefreshService
import com.nextchaptersoftware.integration.data.config.IntegrationDataConfig
import com.nextchaptersoftware.integration.data.store.IntegrationDataStore
import com.nextchaptersoftware.integration.queue.redis.cache.StandardIngestionProgressServiceProvider
import com.nextchaptersoftware.intercom.api.IntercomApiProvider
import com.nextchaptersoftware.intercom.service.IntercomInternalUserAttributionService
import com.nextchaptersoftware.jira.api.JiraApiProvider
import com.nextchaptersoftware.jira.data.services.JiraIssuePersistenceService
import com.nextchaptersoftware.ktor.serialization.KtorSerialization.installSerializer
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.linear.api.LinearApiProvider
import com.nextchaptersoftware.linear.events.queue.enqueue.LinearEventEnqueueService
import com.nextchaptersoftware.linear.services.LinearTokenProvider
import com.nextchaptersoftware.links.LinkInstallationResolverFactory
import com.nextchaptersoftware.links.LinkProcessorFactory
import com.nextchaptersoftware.maintenance.IdentityMaintenance
import com.nextchaptersoftware.maintenance.OrgMaintenance
import com.nextchaptersoftware.maintenance.events.queue.enqueue.MaintenanceEventEnqueueService
import com.nextchaptersoftware.maintenance.installation.ProviderInstallationDeletionService
import com.nextchaptersoftware.maintenance.installation.StandardInstallationDeletionService
import com.nextchaptersoftware.maintenance.org.OrgDeletionService
import com.nextchaptersoftware.maintenance.scm.ScmInstallationMaintenance
import com.nextchaptersoftware.maintenance.scm.ScmTeamLifecycleMaintenance
import com.nextchaptersoftware.mcp.execution.PersonMcpToolOverrideService
import com.nextchaptersoftware.membership.MemberService
import com.nextchaptersoftware.migration.events.queue.enqueue.MigrationEventEnqueueService
import com.nextchaptersoftware.migration.events.queue.handlers.ArchiveLowRelevanceThreadsForPullRequestEventHandler
import com.nextchaptersoftware.migration.events.queue.handlers.ArchiveLowRelevanceThreadsForRepoEventHandler
import com.nextchaptersoftware.migration.events.queue.handlers.MigrationEventHandler
import com.nextchaptersoftware.ml.api.delegate.MachineLearningApiProviderDelegate
import com.nextchaptersoftware.ml.completion.AnthropicCompletionService
import com.nextchaptersoftware.ml.completion.BedrockAnthropicCompletionService
import com.nextchaptersoftware.ml.completion.BedrockConverseCompletionService
import com.nextchaptersoftware.ml.completion.CohereCompletionService
import com.nextchaptersoftware.ml.completion.DecisionCompletionService
import com.nextchaptersoftware.ml.completion.GeminiCompletionService
import com.nextchaptersoftware.ml.completion.MachineLearningCompletionService
import com.nextchaptersoftware.ml.completion.OpenAICompletionService
import com.nextchaptersoftware.ml.completion.RoundRobinCompletionService
import com.nextchaptersoftware.ml.doc.converter.AsanaDocConverter
import com.nextchaptersoftware.ml.doc.converter.CodaDocConverter
import com.nextchaptersoftware.ml.doc.converter.ConfluenceDocConverter
import com.nextchaptersoftware.ml.doc.converter.JiraDocConverter
import com.nextchaptersoftware.ml.doc.converter.PullRequestDocConverter
import com.nextchaptersoftware.ml.doc.converter.ThreadDocConverter
import com.nextchaptersoftware.ml.doc.rerank.services.BedrockCohereDocumentRerankService
import com.nextchaptersoftware.ml.doc.rerank.services.CohereDocumentRerankService
import com.nextchaptersoftware.ml.doc.rerank.services.DecisionDocumentRerankService
import com.nextchaptersoftware.ml.doc.rerank.services.RoundRobinDocumentRerankService
import com.nextchaptersoftware.ml.embedding.opensearch.stats.OpenSearchEmbeddingStats
import com.nextchaptersoftware.ml.embedding.opensearch.store.OpenSearchEmbeddingStore
import com.nextchaptersoftware.ml.embedding.pinecone.stats.PineconeEmbeddingStats
import com.nextchaptersoftware.ml.embedding.pinecone.store.PineconeEmbeddingStore
import com.nextchaptersoftware.ml.embedding.query.services.StandardEmbeddingQueryService
import com.nextchaptersoftware.ml.embedding.query.services.filter.EmbeddingQueryFilterBuilder
import com.nextchaptersoftware.ml.embedding.query.services.fusion.ReciprocalRankFusionDecorator
import com.nextchaptersoftware.ml.embedding.query.services.fusion.ReciprocalRankFusionService
import com.nextchaptersoftware.ml.embedding.services.EmbeddingService
import com.nextchaptersoftware.ml.embedding.services.RoundRobinEmbeddingService
import com.nextchaptersoftware.ml.embedding.services.similarity.CosineSimilarityCalculator
import com.nextchaptersoftware.ml.functions.AWSLiveQueryMLFunction
import com.nextchaptersoftware.ml.functions.AsanaMLFunction
import com.nextchaptersoftware.ml.functions.CIMLFunctions
import com.nextchaptersoftware.ml.functions.CommitsMLFunctions
import com.nextchaptersoftware.ml.functions.DataSourceAccessMLFunctions
import com.nextchaptersoftware.ml.functions.EngagementMetricFunctions
import com.nextchaptersoftware.ml.functions.ExpertsMLFunctions
import com.nextchaptersoftware.ml.functions.FileMLFunctions
import com.nextchaptersoftware.ml.functions.GitHubForkedRepoMLFunctions
import com.nextchaptersoftware.ml.functions.GitHubIssuesMLFunctions
import com.nextchaptersoftware.ml.functions.GraphRagMLFunctions
import com.nextchaptersoftware.ml.functions.JiraMLFunctions
import com.nextchaptersoftware.ml.functions.LinearMLFunctions
import com.nextchaptersoftware.ml.functions.MemberSummaryMLFunctions
import com.nextchaptersoftware.ml.functions.PRCodeReviewMLFunctions
import com.nextchaptersoftware.ml.functions.PRSummaryMLFunctions
import com.nextchaptersoftware.ml.functions.RepoSummaryMLFunction
import com.nextchaptersoftware.ml.functions.SlackMLFunctions
import com.nextchaptersoftware.ml.functions.SuggestedQuestionsMLFunctions
import com.nextchaptersoftware.ml.functions.services.ScmCommitService
import com.nextchaptersoftware.ml.functions.services.SlackContentFilterService
import com.nextchaptersoftware.ml.inference.extractors.PrIdExtractor
import com.nextchaptersoftware.ml.inference.services.PersonMcpTemplateOverrideService
import com.nextchaptersoftware.ml.inference.services.inferences.MLInferenceService
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import com.nextchaptersoftware.ml.prompt.services.PromptCompilerService
import com.nextchaptersoftware.ml.services.MLFunctionMemberService
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.notification.services.InactiveFollowupService
import com.nextchaptersoftware.notification.services.PeerInviteSuggestionService
import com.nextchaptersoftware.notion.api.NotionApiProvider
import com.nextchaptersoftware.notion.api.NotionOauthTokenProvider
import com.nextchaptersoftware.openai.api.OpenAIApiConfiguration
import com.nextchaptersoftware.openai.api.OpenAIApiProvider
import com.nextchaptersoftware.openai.api.delegates.AzureOpenAIApiProviderDelegate
import com.nextchaptersoftware.opensearch.api.OpenSearchApiConfiguration
import com.nextchaptersoftware.opensearch.api.OpenSearchApiProvider
import com.nextchaptersoftware.opensearch.config.OpenSearchConfig
import com.nextchaptersoftware.opensearch.index.OpenSearchIndexLoader
import com.nextchaptersoftware.opensearch.plugins.configureOpenSearch
import com.nextchaptersoftware.orchestration.enablement.BootstrappingPromotionService
import com.nextchaptersoftware.orchestration.enablement.CompositePromotionCriterion
import com.nextchaptersoftware.orchestration.enablement.IntegrationsIngestionCriterion
import com.nextchaptersoftware.orchestration.enablement.OnboardingEnablementService
import com.nextchaptersoftware.orchestration.enablement.PullRequestIngestionCriterion
import com.nextchaptersoftware.orchestration.enablement.RepoCodeIngestionCriterion
import com.nextchaptersoftware.pinecone.api.PineconeApiConfiguration
import com.nextchaptersoftware.pinecone.api.PineconeApiProvider
import com.nextchaptersoftware.pinecone.api.PineconeControlPlaneApiProvider
import com.nextchaptersoftware.pinecone.config.PineconeConfig
import com.nextchaptersoftware.pinecone.index.PineconeIndexLoader
import com.nextchaptersoftware.pinecone.plugins.configurePinecone
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesServiceProvider
import com.nextchaptersoftware.pr.config.PullRequestConfig
import com.nextchaptersoftware.pr.ingestion.queue.enqueue.PullRequestEventEnqueueService
import com.nextchaptersoftware.pr.summary.ingestion.pipeline.RepoPullRequestSummaryService
import com.nextchaptersoftware.pr.summary.ingestion.services.internal.PullRequestIngestionInputPayloadService
import com.nextchaptersoftware.pr.summary.ingestion.services.internal.PullRequestIngestionStateMachineProvider
import com.nextchaptersoftware.product.feedback.ProductFeedbackService
import com.nextchaptersoftware.rapid.services.RapidServiceProvider
import com.nextchaptersoftware.redis.lock.LockProvider
import com.nextchaptersoftware.redis.lock.LockType
import com.nextchaptersoftware.regression.testing.jobs.RegressionEventJob
import com.nextchaptersoftware.regression.testing.queue.enqueue.RegressionEventEnqueueService
import com.nextchaptersoftware.regression.testing.queue.handlers.IsQuestionAnswerRegressionRunEvalHandler
import com.nextchaptersoftware.regression.testing.queue.handlers.RegressionTestingEventHandler
import com.nextchaptersoftware.regression.testing.services.questionanswer.IsQuestionAnswerRegressionRunService
import com.nextchaptersoftware.regression.testing.services.questionanswer.IsQuestionAnswerRegressionService
import com.nextchaptersoftware.regression.testing.services.questionanswer.IsQuestionRegressionService
import com.nextchaptersoftware.repo.LocalRepoComputeService
import com.nextchaptersoftware.repo.RepoAccessService
import com.nextchaptersoftware.repo.RepoFocusService
import com.nextchaptersoftware.rpc.RpcFacade
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.ScmAuthApiFactory
import com.nextchaptersoftware.scm.ScmCloneUrlProvider
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.scm.ScmTeamApiFactory
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.bitbucket.BitbucketPipelinesClientProvider
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.delegates.ScmAppDelegateImpl
import com.nextchaptersoftware.scm.github.GitHubActionsClientProvider
import com.nextchaptersoftware.scm.gitlab.GitLabPipelinesClientProvider
import com.nextchaptersoftware.scm.queue.enqueue.ScmEventProducer
import com.nextchaptersoftware.scm.services.ProfileService
import com.nextchaptersoftware.scm.services.RepoMaintenance
import com.nextchaptersoftware.scm.utils.DefaultEnterpriseAppConfigOrgIdsResolver
import com.nextchaptersoftware.search.events.queue.enqueue.SearchEventEnqueueService
import com.nextchaptersoftware.search.events.queue.enqueue.SearchPriorityEventEnqueueService
import com.nextchaptersoftware.search.indexing.events.queue.enqueue.SearchIndexingEventEnqueueService
import com.nextchaptersoftware.search.semantic.services.EmbeddingExperimentService
import com.nextchaptersoftware.search.semantic.services.SemanticSearchDocumentService
import com.nextchaptersoftware.search.semantic.services.SemanticSearchQueryService
import com.nextchaptersoftware.search.semantic.services.agents.DocumentEvaluationRetriever
import com.nextchaptersoftware.search.semantic.services.agents.DocumentRelevanceEvaluatorAgent
import com.nextchaptersoftware.search.semantic.services.asset.ScoredAssetContextCollector
import com.nextchaptersoftware.search.semantic.services.defence.MaliciousQueryDetectionService
import com.nextchaptersoftware.search.semantic.services.detection.RAGWorthinessDetectionService
import com.nextchaptersoftware.search.semantic.services.documentation.SlackDocumentRetentionFilterService
import com.nextchaptersoftware.search.semantic.services.documentation.SlackThreadRetentionFilterService
import com.nextchaptersoftware.search.semantic.services.documentation.StandardDocumentFilterService
import com.nextchaptersoftware.search.semantic.services.documentation.StandardDocumentationValidationService
import com.nextchaptersoftware.search.semantic.services.eval.EvalService
import com.nextchaptersoftware.search.semantic.services.eval.RAGRegressionInstanceService
import com.nextchaptersoftware.search.semantic.services.eval.RegressionTestService
import com.nextchaptersoftware.search.semantic.services.functions.MLFunctionExecutor
import com.nextchaptersoftware.search.semantic.services.functions.MLFunctionService
import com.nextchaptersoftware.search.semantic.services.functions.ScmExpertService
import com.nextchaptersoftware.search.semantic.services.functions.TimeFilteredDocumentsMLFunctions
import com.nextchaptersoftware.search.semantic.services.presets.MessageDataSourcePresetService
import com.nextchaptersoftware.search.semantic.services.references.DemoReferenceResolver
import com.nextchaptersoftware.search.semantic.services.references.InPromptReferenceResolver
import com.nextchaptersoftware.search.semantic.services.references.InlineReferencesCleanupService
import com.nextchaptersoftware.search.semantic.services.references.InlineReferencesResolverService
import com.nextchaptersoftware.search.semantic.services.references.InlineReferencesToMarkdownLinksService
import com.nextchaptersoftware.search.semantic.services.references.MessageReferencesService
import com.nextchaptersoftware.search.semantic.services.references.ReferencesExpansionService
import com.nextchaptersoftware.search.semantic.services.references.ResponseReferenceResolver
import com.nextchaptersoftware.search.semantic.services.retrieval.ChatCompressionService
import com.nextchaptersoftware.search.semantic.services.retrieval.RepoExtractorService
import com.nextchaptersoftware.search.semantic.services.retrieval.SemanticDocumentRetriever
import com.nextchaptersoftware.search.semantic.services.retrieval.filters.GleanFilter
import com.nextchaptersoftware.search.semantic.services.samplequestions.SampleQuestionGeneratorImpl
import com.nextchaptersoftware.search.semantic.services.samplequestions.SampleQuestionGeneratorService
import com.nextchaptersoftware.search.semantic.services.sanitizer.CompositeResponseSanitizer
import com.nextchaptersoftware.search.semantic.services.sanitizer.DevelopedByOpenAIResponseSanitizer
import com.nextchaptersoftware.search.services.index.IndexingAndEmbeddingService
import com.nextchaptersoftware.search.services.query.factory.DocumentInsightContentService
import com.nextchaptersoftware.search.services.query.factory.SearchDecorator
import com.nextchaptersoftware.search.services.query.filter.DocumentSearchResultDecisionServiceProvider
import com.nextchaptersoftware.search.services.query.filter.SlackDecisionService
import com.nextchaptersoftware.search.services.query.filter.StandardSearchResultsFilter
import com.nextchaptersoftware.search.services.query.filter.ThreadSearchResultDecisionServiceProvider
import com.nextchaptersoftware.service.ExclusiveBackgroundJob
import com.nextchaptersoftware.service.PollingBackgroundJob
import com.nextchaptersoftware.service.createBackgroundJob
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import com.nextchaptersoftware.service.plugins.configureBackgroundJobs
import com.nextchaptersoftware.service.plugins.configureCaching
import com.nextchaptersoftware.service.plugins.configureJvmMetrics
import com.nextchaptersoftware.service.plugins.configureMonitoring
import com.nextchaptersoftware.service.plugins.configureSerialization
import com.nextchaptersoftware.slack.bot.events.queue.enqueue.PendingSlackQuestionService
import com.nextchaptersoftware.slack.bot.events.queue.enqueue.SlackBotEventEnqueueService
import com.nextchaptersoftware.slack.bot.services.completion.AnswerEvaluationService
import com.nextchaptersoftware.slack.bot.services.completion.IsQuestionCompletionService
import com.nextchaptersoftware.slack.extractor.utils.SlackConversationSummaryPromptExtractor
import com.nextchaptersoftware.slack.notify.SlackNotifier
import com.nextchaptersoftware.slack.services.AuthorizedSlackChannelResolver
import com.nextchaptersoftware.slack.services.SlackChannelAccessService
import com.nextchaptersoftware.slack.services.SlackChannelResolver
import com.nextchaptersoftware.slack.services.SlackTokenService
import com.nextchaptersoftware.sourcecode.config.SourceCodeConfig
import com.nextchaptersoftware.sourcecode.ingestion.pipeline.RepoCodeIngestionService
import com.nextchaptersoftware.stackoverflowteams.api.StackOverflowTeamsApiProvider
import com.nextchaptersoftware.stackoverflowteams.api.StackOverflowTeamsAuthProvider
import com.nextchaptersoftware.stripe.services.StripeService
import com.nextchaptersoftware.summarization.events.queue.enqueue.SummarizationEventEnqueueService
import com.nextchaptersoftware.topic.config.TopicConfig
import com.nextchaptersoftware.topic.events.queue.enqueue.TopicEventEnqueueService
import com.nextchaptersoftware.topic.ingestion.pipeline.RepoTopicIngestionService
import com.nextchaptersoftware.topic.insight.services.ClusterInsightTopicsService
import com.nextchaptersoftware.topic.insight.services.DecisionInsightTopicsService
import com.nextchaptersoftware.topic.insight.services.TopicExpertMappingService
import com.nextchaptersoftware.topic.insight.services.content.InsightContentModelService
import com.nextchaptersoftware.topic.insight.services.map.DecisionTopicMappingService
import com.nextchaptersoftware.topic.insight.services.map.QuestionAnswerThreadService
import com.nextchaptersoftware.topic.insight.services.map.StandardTopicMappingService
import com.nextchaptersoftware.topic.insight.services.map.TopicMappingRuleService
import com.nextchaptersoftware.user.secret.UserSecretService
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver
import com.nextchaptersoftware.user.secret.config.UserSecretConfig
import com.nextchaptersoftware.userengagement.GrowthService
import com.nextchaptersoftware.userengagement.UserEngagementService
import com.nextchaptersoftware.utils.kilobytes
import com.nextchaptersoftware.version.VersionService
import com.nextchaptersoftware.web.events.queue.enqueue.WebEventEnqueueService
import com.nextchaptersoftware.web.ingestion.WebIngestionService
import com.nextchaptersoftware.web.ingestion.queue.enqueue.WebIngestionEventEnqueueService
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.plugins.compression.Compression
import io.ktor.server.resources.Resources
import io.ktor.server.routing.IgnoreTrailingSlash
import io.ktor.server.sse.SSE
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds
import kotlin.time.Instant

/**
 * The number of concurrent consumers for the regression event queue.
 */
private const val CONSUMER_COUNT = 4

@Suppress("LongMethod", "CyclomaticComplexMethod")
fun Application.module(
    serviceLifecycle: ServiceLifecycle = ServiceLifecycle(healthCheckers = emptyList()),
    config: GlobalConfig = GlobalConfig.INSTANCE,
    pineconeConfig: PineconeConfig = PineconeConfig.INSTANCE,
    ciConfig: CIConfig = CIConfig.INSTANCE,
    scmConfig: ScmConfig = ScmConfig.INSTANCE,
    integrationDataConfig: IntegrationDataConfig = IntegrationDataConfig.INSTANCE,
    sourceCodeConfig: SourceCodeConfig = SourceCodeConfig.INSTANCE,
    adminConfig: AdminConfig = AdminConfig.INSTANCE,
    ingestionPipelineConfig: IngestionPipelineConfig = IngestionPipelineConfig.INSTANCE,
    openSearchConfig: OpenSearchConfig = OpenSearchConfig.INSTANCE,
    pullRequestConfig: PullRequestConfig = PullRequestConfig.INSTANCE,
    topicConfig: TopicConfig = TopicConfig.INSTANCE,
    lambdaProviderFactory: LambdaProviderFactory = StandardLambdaProviderFactory(),
    sfnProviderFactory: SfnProviderFactory = StandardSfnProviderFactory(),
    bedrockRuntimeProviderFactory: BedrockRuntimeProviderFactory = StandardBedrockRuntimeProviderFactory(),
    bedrockRuntimeAsyncProviderFactory: BedrockRuntimeAsyncProviderFactory = StandardBedrockRuntimeAsyncProviderFactory(),
    geminiConfig: GeminiApiConfig = GeminiApiConfig.INSTANCE,
) {
    val ciSecretsConfig by lazy {
        CISecretsConfig.INSTANCE
    }

    val tokenSecretDecryption by lazy {
        RSAClientServerCryptoSystem.RSADecryption(ciSecretsConfig.ci.secretsPrivateKey.value)
    }

    val openSearchApiProvider by lazy {
        OpenSearchApiProvider(
            config = OpenSearchApiConfiguration(
                baseApiUri = config.openSearch.baseApiUri.asUrl,
                timeout = config.openSearch.defaultTimeout,
                userName = config.openSearch.userName,
                password = config.openSearch.password,
            ),
        )
    }

    val openSearchEmbeddingStore by lazy {
        OpenSearchEmbeddingStore(
            indexName = openSearchConfig.openSearchIndex.indexName,
            openSearchApiProvider = openSearchApiProvider,
        )
    }

    val openSearchIndexLoader by lazy {
        OpenSearchIndexLoader(
            openSearchApiProvider = openSearchApiProvider,
        )
    }

    val openSearchLockProvider by lazy {
        LockProvider(type = LockType.OpenSearchLoader)
    }

    val pineconeApiProvider by lazy {
        PineconeApiProvider(
            config = PineconeApiConfiguration(
                indexName = pineconeConfig.pineconeIndex.indexName,
                dimension = pineconeConfig.pineconeIndex.dimension,
                apiKey = config.pinecone.apiKey,
                maxRetries = config.pinecone.maxRetries,
                timeout = config.pinecone.defaultTimeout,
            ),
        )
    }

    val pineconeControlPlaneApiProvider by lazy {
        PineconeControlPlaneApiProvider(
            config = PineconeApiConfiguration(
                indexName = pineconeConfig.pineconeIndex.indexName,
                dimension = pineconeConfig.pineconeIndex.dimension,
                apiKey = config.pinecone.apiKey,
                maxRetries = config.pinecone.maxRetries,
                timeout = config.pinecone.defaultTimeout,
            ),
        )
    }

    val pineconeEmbeddingStore by lazy {
        PineconeEmbeddingStore(pineconeApiProvider = pineconeApiProvider)
    }

    val pineconeIndexLoader by lazy {
        PineconeIndexLoader(
            pineconeControlPlaneApiProvider = pineconeControlPlaneApiProvider,
        )
    }

    val pineconeLockProvider by lazy {
        LockProvider(type = LockType.PineconeLoader)
    }

    val awsClientProvider by lazy {
        AWSClientProvider.from(
            region = ServiceInitializer.REGION.toRegion(),
        )
    }

    val bedrockRuntimeProvider by lazy {
        bedrockRuntimeProviderFactory.generate(
            awsClientProvider = awsClientProvider,
        )
    }

    val bedrockRuntimeAsyncProvider by lazy {
        bedrockRuntimeAsyncProviderFactory.generate(
            awsClientProvider = awsClientProvider,
        )
    }

    val bedrockAnthropicCompletionsApi by lazy {
        BedrockAnthropicCompletionsApi(
            bedrockRuntimeProvider = bedrockRuntimeProvider,
            bedrockRuntimeAsyncProvider = bedrockRuntimeAsyncProvider,
        )
    }

    val bedrockCohereRerankApi by lazy {
        BedrockCohereRerankApi(
            bedrockRuntimeProvider = bedrockRuntimeProvider,
        )
    }

    val azureGPT41OpenAIApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt41DeploymentId,
    )

    val azureGPT41MiniOpenAIApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt41MiniDeploymentId,
    )

    val azureGPT41NanoApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt41NanoDeploymentId,
    )

    val azureGPT5MiniApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt5MiniDeploymentId,
    )

    val azureGPT5NanoApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt5NanoDeploymentId,
    )

    val intercomApiProvider by lazy {
        IntercomApiProvider()
    }

    val versionService by lazy {
        VersionService(
            versionConfig = config.versioning,
        )
    }

    val insiderService by lazy {
        InsiderService()
    }

    val googleVerifier by lazy {
        adminConfig.adminAuthentication.googleVerifier?.let {
            GoogleVerifier(config = it)
        }
    }

    val passwordVerifier by lazy {
        adminConfig.adminAuthentication.passwordVerifier?.let {
            PasswordVerifier(
                config = it,
                insiderService = insiderService,
            )
        }
    }
    val topicEventEnqueueService by lazy {
        TopicEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.topicEventsQueueName,
                ),
            ),
        )
    }
    val adminAuthenticationProvider by lazy {
        AdminAuthenticationProvider(
            adminConfig = adminConfig,
            insiderService = insiderService,
        )
    }
    val internalUserAttributeService by lazy {
        IntercomInternalUserAttributionService(
            intercomApiProvider = intercomApiProvider,
            insiderService = insiderService,
        )
    }

    val repoCodeIngestionService by lazy {
        RepoCodeIngestionService(reingestionCriteria = sourceCodeConfig.codeIngestionDataPipeline.reingestionCriteria)
    }

    val repoPullRequestSummaryService by lazy {
        RepoPullRequestSummaryService(
            ingestionInterval = pullRequestConfig.pullRequestDataPipeline.ingestionInterval,
            lastActiveAtThreshold = pullRequestConfig.pullRequestDataPipeline.lastActiveAtThreshold,
            maxReposPerTeam = pullRequestConfig.pullRequestDataPipeline.maxReposPerTeam,
        )
    }

    val pullRequestIngestionStateMachineProvider by lazy {
        PullRequestIngestionStateMachineProvider(
            config = config,
            scmS3Bucket = integrationDataConfig.integrationData.s3.bucket,
            stateMachineProvider = StandardStateMachineProvider(
                sfnProvider = sfnProviderFactory.generate(
                    awsClientProvider = AWSClientProvider.from(
                        region = ingestionPipelineConfig.ingestionPipeline.s3.region.toRegion(),
                    ),
                ),
            ),
            ingestionPipelineConfig = ingestionPipelineConfig,
            pullRequestConfig = pullRequestConfig,
            pullRequestIngestionInputPayloadService = PullRequestIngestionInputPayloadService(),
            repoPullRequestSummaryService = repoPullRequestSummaryService,
        )
    }

    val notificationEventEnqueueService by lazy {
        NotificationEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.notificationEventsQueueName,
                ),
            ),
        )
    }

    val userSecretConfig by lazy {
        UserSecretConfig.INSTANCE
    }

    val userSecretServiceRSA by lazy {
        UserSecretService(
            encryption = RSACryptoSystem.RSAEncryption(
                publicKey = config.encryption.userSecrets4096PublicKey,
                modulusBitLength = 4096,
            ),
            decryption = RSACryptoSystem.RSADecryption(
                privateKey = userSecretConfig.encryption.userSecrets4096PrivateKey.value,
                modulusBitLength = 4096,
            ),
        )
    }

    val userSecretService by lazy {
        // TODO: remove once all have been migrated to `userSecretServiceRSA`
        userSecretServiceRSA
    }

    val userSecretServiceAES by lazy {
        val key = AESCryptoSystem.importKey(
            userSecretConfig.encryption.userSecretsAesKey.value,
        )
        UserSecretService(
            encryption = AESCryptoSystem.AESEncryption(key),
            decryption = AESCryptoSystem.AESDecryption(key),
        )
    }

    val userSecretServiceResolver by lazy {
        UserSecretServiceResolver(
            userSecretServiceRSA = userSecretServiceRSA,
            userSecretServiceAES = userSecretServiceAES,
        )
    }

    val scmWebFactory by lazy {
        ScmWebFactory(
            scmConfig = scmConfig,
        )
    }

    val enterpriseAppConfigOrgIdsResolver by lazy {
        DefaultEnterpriseAppConfigOrgIdsResolver()
    }

    val scmAuthApiFactory by lazy {
        ScmAuthApiFactory(
            authenticationConfig = config.authentication,
            scmConfig = scmConfig,
            scmWebFactory = scmWebFactory,
            enterpriseAppConfigOrgIdsResolver = enterpriseAppConfigOrgIdsResolver,
        )
    }

    val scmAppApiFactory by lazy {
        ScmAppApiFactory(
            scmConfig = scmConfig,
            enterpriseAppConfigOrgIdsResolver = enterpriseAppConfigOrgIdsResolver,
        )
    }

    val scmRepoApiFactory by lazy {
        ScmRepoApiFactory(
            scmAuthApiFactory = scmAuthApiFactory,
            scmAppApiFactory = scmAppApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val scmUserApiFactory by lazy {
        ScmUserApiFactory(
            scmAuthApiFactory = scmAuthApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
            enterpriseAppConfigOrgIdsResolver = enterpriseAppConfigOrgIdsResolver,
        )
    }

    val profileService by lazy {
        ProfileService(
            scmRepoApiFactory = scmRepoApiFactory,
            scmUserApiFactory = scmUserApiFactory,
        )
    }

    val machineLearningApiProviders by MachineLearningApiProviderDelegate(
        machineLearningConfig = config.machineLearning,
    )

    val embeddingService by lazy {
        RoundRobinEmbeddingService(
            embeddingServices = machineLearningApiProviders.map { machineLearningApiProvider ->
                EmbeddingService(
                    machineLearningApiProvider = machineLearningApiProvider,
                )
            },
        )
    }

    val machineLearningCompletionService by lazy {
        RoundRobinCompletionService(
            completionServices = machineLearningApiProviders.map { machineLearningApiProvider ->
                MachineLearningCompletionService(
                    machineLearningApiProvider = machineLearningApiProvider,
                )
            },
        )
    }

    val openAIApiProvider by lazy {
        OpenAIApiProvider(
            config = OpenAIApiConfiguration(
                timeout = config.openAI.defaultTimeout,
                token = config.openAI.apiKey,
            ),
        )
    }

    val openRouterApiProvider by lazy {
        OpenAIApiProvider(
            config = OpenAIApiConfiguration(
                timeout = config.openRouter.defaultTimeout,
                token = config.openRouter.apiKey,
                host = OpenAIHost(baseUrl = config.openRouter.baseApiUri),
            ),
        )
    }

    val anthropicApiConfiguration by lazy {
        AnthropicApiConfiguration(
            baseApiUri = config.anthropic.baseApiUri.asUrl,
            timeout = config.anthropic.defaultTimeout,
            token = config.anthropic.apiKey,
        )
    }

    val anthropicApiProvider by lazy {
        AnthropicApiProvider(
            config = anthropicApiConfiguration,
        )
    }
    val anthropicCompletionService by lazy {
        AnthropicCompletionService(
            anthropicApiProvider = anthropicApiProvider,
        )
    }

    val bedrockAnthropicCompletionService by lazy {
        BedrockAnthropicCompletionService(
            bedrockAnthropicCompletionsApi = bedrockAnthropicCompletionsApi,
        )
    }

    val bedrockConverseCompletionService by lazy {
        BedrockConverseCompletionService(
            bedrockRuntimeAsyncProvider = bedrockRuntimeAsyncProvider,
        )
    }

    val roundRobinAnthropicCompletionService by lazy {
        RoundRobinCompletionService(
            completionServices = listOf(
                anthropicCompletionService,
                bedrockAnthropicCompletionService,
            ),
        )
    }

    val pineconeEmbeddingStats by lazy {
        PineconeEmbeddingStats(pineconeApiProvider = pineconeApiProvider)
    }

    val openSearchEmbeddingStats by lazy {
        OpenSearchEmbeddingStats(
            indexName = openSearchConfig.openSearchIndex.indexName,
            openSearchApiProvider = openSearchApiProvider,
        )
    }

    val embeddingStoreFacade by lazy {
        EmbeddingStoreFacade(
            openSearchEmbeddingStore = openSearchEmbeddingStore,
            pineconeEmbeddingStore = pineconeEmbeddingStore,
        )
    }

    val embeddingStatsFacade by lazy {
        EmbeddingStatsFacade(
            openSearchEmbeddingStats = openSearchEmbeddingStats,
            pineconeEmbeddingStats = pineconeEmbeddingStats,
        )
    }

    val scmTeamApiFactory by lazy {
        ScmTeamApiFactory(
            scmAuthApiFactory = scmAuthApiFactory,
            scmAppApiFactory = scmAppApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val pullRequestEventEnqueueService by lazy {
        PullRequestEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.pullRequestEventsQueueName,
                ),
            ),
        )
    }

    val searchEventEnqueueService by lazy {
        SearchEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.searchEventsQueueName,
                ),
            ),
        )
    }

    val regressionEventEnqueueService by lazy {
        RegressionEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.regressionTestingEventsQueueName,
                ),
            ),
        )
    }

    val summarizationEventEnqueueService by lazy {
        SummarizationEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.summarizationEventsQueueName,
                ),
            ),
        )
    }

    val inferenceService by lazy {
        MLInferenceService()
    }

    val planCapabilitiesService by lazy {
        PlanCapabilitiesServiceProvider(config = config.billing).get()
    }

    val rapidServiceProvider by lazy {
        RapidServiceProvider(
            config = config.rapid,
        )
    }

    val threadInsightIndexContentService by lazy {
        ThreadInsightIndexContentService()
    }

    val pullRequestInsightIndexContentService by lazy {
        PullRequestInsightIndexContentService()
    }

    val documentInsightContentService by lazy {
        DocumentInsightContentService()
    }

    val slackChannelAccessService by lazy {
        SlackChannelAccessService(
            planCapabilitiesService = planCapabilitiesService,
        )
    }

    val slackDecisionService by lazy {
        SlackDecisionService(slackChannelAccessService = slackChannelAccessService)
    }

    val threadSearchResultDecisionProvider by lazy {
        ThreadSearchResultDecisionServiceProvider(slackDecisionService = slackDecisionService)
    }

    val documentDecisionServiceProvider by lazy {
        DocumentSearchResultDecisionServiceProvider()
    }

    val searchResultsFilter by lazy {
        StandardSearchResultsFilter(
            threadSearchResultDecisionProvider = threadSearchResultDecisionProvider,
            documentDecisionServiceProvider = documentDecisionServiceProvider,
        )
    }

    val botAccountService by lazy {
        InstallationBotAccountService()
    }

    val searchDecorator by lazy {
        SearchDecorator(
            botAccountService = botAccountService,
            threadInsightIndexContentService = threadInsightIndexContentService,
            prInsightIndexContentService = pullRequestInsightIndexContentService,
            documentInsightContentService = documentInsightContentService,
            searchResultsFilter = searchResultsFilter,
        )
    }

    val reciprocalRankFusionService by lazy {
        ReciprocalRankFusionService()
    }

    val reciprocalRankFusionDecorator by lazy {
        ReciprocalRankFusionDecorator(
            reciprocalRankFusionService = reciprocalRankFusionService,
        )
    }

    val embeddingSecretConfig by lazy {
        EmbeddingSecretConfig.INSTANCE
    }

    val embeddingEncoding by lazy {
        EmbeddingEncoding(
            embeddingContentConfig = embeddingSecretConfig.embedding,
        )
    }

    val embeddingQueryFilterBuilder by lazy {
        EmbeddingQueryFilterBuilder()
    }

    val standardEmbeddingQueryService by lazy {
        StandardEmbeddingQueryService(
            embeddingStoreFacade = embeddingStoreFacade,
            embedder = embeddingService,
            embeddingEncoding = embeddingEncoding,
            embeddingQueryFilterBuilder = embeddingQueryFilterBuilder,
            reciprocalRankFusionDecorator = reciprocalRankFusionDecorator,
            searchDecorator = searchDecorator,
        )
    }

    val webEventEnqueueService by lazy {
        WebEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.webEventsQueueName,
                ),
            ),
            progressServiceProvider = StandardIngestionProgressServiceProvider(),
        )
    }

    val documentationValidationService by lazy {
        StandardDocumentationValidationService(
            webEventEnqueueService = webEventEnqueueService,
        )
    }

    val embeddingEventEnqueueService by lazy {
        EmbeddingEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.embeddingEventsQueueName,
                ),
            ),
        )
    }

    val searchIndexingEventEnqueueService by lazy {
        SearchIndexingEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.searchIndexingEventsQueueName,
                ),
            ),
        )
    }

    val indexingAndEmbeddingService by lazy {
        IndexingAndEmbeddingService(
            searchIndexingEventEnqueueService = searchIndexingEventEnqueueService,
            embeddingEventEnqueueService = embeddingEventEnqueueService,
        )
    }

    val slackDocumentRetentionFilterService by lazy {
        SlackDocumentRetentionFilterService(
            embeddingEventEnqueueService = embeddingEventEnqueueService,
        )
    }

    val slackThreadRetentionFilterService by lazy {
        SlackThreadRetentionFilterService(
            indexingAndEmbeddingService = indexingAndEmbeddingService,
        )
    }

    val documentFilterService by lazy {
        StandardDocumentFilterService(
            documentFilters = listOf(
                slackDocumentRetentionFilterService,
                slackThreadRetentionFilterService,
            ),
        )
    }

    val semanticSearchDocumentService by lazy {
        SemanticSearchDocumentService(
            embeddingQueryService = standardEmbeddingQueryService,
            documentationValidationService = documentationValidationService,
            documentFilterService = documentFilterService,
        )
    }

    val cohereApiProvider by lazy {
        CohereApiProvider(
            config = CohereApiConfiguration(config),
        )
    }

    val cohereCompletionService by lazy {
        CohereCompletionService(
            cohereApiProvider = cohereApiProvider,
        )
    }

    val geminiApiProvider by lazy {
        GeminiApiProvider(
            config = geminiConfig,
        )
    }

    val geminiCompletionService by lazy {
        GeminiCompletionService(
            geminiApiProvider = geminiApiProvider,
        )
    }

    val completionService by lazy {
        DecisionCompletionService(
            machineLearningCompletionService = machineLearningCompletionService,
            openAICompletionService = OpenAICompletionService(
                openAIApiProvider = openAIApiProvider,
            ),
            openRouterCompletionService = OpenAICompletionService(
                openAIApiProvider = openRouterApiProvider,
            ),
            bedrockAnthropicCompletionService = bedrockAnthropicCompletionService,
            anthropicCompletionService = anthropicCompletionService,
            roundRobinGPT41CompletionService = RoundRobinCompletionService(
                completionServices = azureGPT41OpenAIApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                    )
                },
            ),
            roundRobinGPT41MiniCompletionService = RoundRobinCompletionService(
                completionServices = azureGPT41MiniOpenAIApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                    )
                },
            ),
            roundRobinGPT41NanoCompletionService = RoundRobinCompletionService(
                completionServices = azureGPT41NanoApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                    )
                },
            ),
            roundRobinGPT5MiniCompletionService = RoundRobinCompletionService(
                completionServices = azureGPT5MiniApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                    )
                },
            ),
            roundRobinGPT5NanoCompletionService = RoundRobinCompletionService(
                completionServices = azureGPT5NanoApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                    )
                },
            ),
            roundRobinAnthropicCompletionService = roundRobinAnthropicCompletionService,
            cohereCompletionService = cohereCompletionService,
            geminiCompletionService = geminiCompletionService,
            bedrockConverseCompletionService = bedrockConverseCompletionService,
        )
    }

    val promptCompilerService by lazy {
        PromptCompilerService(scmWebFactory = scmWebFactory)
    }

    val prIdExtractor by lazy {
        PrIdExtractor()
    }

    val responseReferenceResolver by lazy {
        ResponseReferenceResolver(prIdExtractor)
    }

    val inPromptReferenceResolver by lazy {
        InPromptReferenceResolver()
    }

    val messageReferencesService by lazy {
        MessageReferencesService()
    }

    val messageDataSourcePresetService by lazy {
        MessageDataSourcePresetService()
    }

    val compositeResponseSanitizer by lazy {
        CompositeResponseSanitizer(DevelopedByOpenAIResponseSanitizer())
    }

    val cohereDocumentRerankService by lazy {
        CohereDocumentRerankService(
            cohereApiProvider = cohereApiProvider,
        )
    }

    val bedrockCohereDocumentRerankService by lazy {
        BedrockCohereDocumentRerankService(
            bedrockCohereRerankApi = bedrockCohereRerankApi,
        )
    }

    val documentRerankService by lazy {
        DecisionDocumentRerankService(
            cohereDocumentRerankService = cohereDocumentRerankService,
            bedrockCohereDocumentRerankService = bedrockCohereDocumentRerankService,
            roundRobinCohereDocumentRerankService = RoundRobinDocumentRerankService(
                documentRerankServices = listOf(
                    cohereDocumentRerankService,
                    bedrockCohereDocumentRerankService,
                ),
            ),
        )
    }

    val inferenceTemplateService by lazy {
        MLInferenceTemplateService()
    }

    val chatCompressionService by lazy {
        ChatCompressionService(
            reducerCompletionService = completionService,
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
            inferenceService = inferenceService,
        )
    }

    val inlineReferencesResolverService by lazy {
        InlineReferencesResolverService(
            inlineReferencesResolutionTimeout = config.search.inlineReferencesResolutionTimeout,
            inlineReferencesResolutionCompletionService = completionService,
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
        )
    }

    val inlineReferencesToMarkdownLinksService by lazy {
        InlineReferencesToMarkdownLinksService()
    }

    val inlineReferencesCleanupService by lazy {
        InlineReferencesCleanupService()
    }

    val referencesExpansionService by lazy {
        ReferencesExpansionService()
    }

    val searchPriorityEventEnqueueService by lazy {
        SearchPriorityEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.searchPriorityEventsQueueName,
                ),
            ),
        )
    }

    val memberService by lazy {
        MemberService()
    }

    val repoComputeService by lazy {
        LocalRepoComputeService(
            scmTeamApiFactory = scmTeamApiFactory,
            scmUserApiFactory = scmUserApiFactory,
        )
    }

    val repoAccessService by lazy {
        RepoAccessService(
            repoComputeService = repoComputeService,
        )
    }

    val repoExtractorService by lazy {
        RepoExtractorService(
            templateService = inferenceTemplateService,
            promptCompilerService = promptCompilerService,
            completionService = completionService,
            repoAccessService = repoAccessService,
        )
    }

    val repoFocusService by lazy {
        RepoFocusService()
    }

    val unencryptedTokenPersistence by lazy {
        UnencryptedTokenPersistence(identityStore = Stores.identityStore)
    }

    val confluenceAuthApi by lazy {
        config.providers.confluence?.let { config ->
            AtlassianAuthApiImpl(
                clientId = config.oauth.clientId,
                clientSecret = config.clientSecret.value,
                tokenExchangeUrl = checkNotNull(config.oauth.tokenExchangeUrl),
                oauthCallbackUrl = checkNotNull(config.oauth.oauthCallbackUrl),
            )
        } ?: NoopAtlassianAuthApi()
    }

    val confluenceAtlassianTokenProvider by lazy {
        AtlassianTokenProvider(
            oauthTokenRefreshService = UserSecretOAuthRefreshService(
                tokenRefresher = confluenceAuthApi,
                tokenPersistence = unencryptedTokenPersistence,
            ),
        )
    }

    val confluenceCloudApiProvider by lazy {
        ConfluenceCloudApiProvider()
    }

    val jiraAuthApi by lazy {
        config.providers.jira?.let { config ->
            AtlassianAuthApiImpl(
                clientId = config.oauth.clientId,
                clientSecret = config.clientSecret.value,
                tokenExchangeUrl = checkNotNull(config.oauth.tokenExchangeUrl),
                oauthCallbackUrl = checkNotNull(config.oauth.oauthCallbackUrl),
            )
        } ?: NoopAtlassianAuthApi()
    }

    val jiraAtlassianTokenProvider by lazy {
        AtlassianTokenProvider(
            oauthTokenRefreshService = UserSecretOAuthRefreshService(
                tokenRefresher = jiraAuthApi,
                tokenPersistence = unencryptedTokenPersistence,
            ),
        )
    }

    val jiraApiProvider by lazy {
        JiraApiProvider()
    }

    val googleOAuthRefreshService by lazy {
        UserSecretOAuthRefreshService(
            tokenRefresher = NoopOAuthTokenRefresher(), // Not used for Google OAuth
            tokenPersistence = EncryptedTokenPersistence(userSecretService = userSecretService),
        )
    }

    val googleWorkspaceServiceAccountKeyProvider by lazy {
        GoogleWorkspaceServiceAccountKeyProvider(
            userSecretService = userSecretServiceAES,
        )
    }

    val googleCredentialProvider by lazy {
        config.providers.googleDrive?.let {
            GoogleCredentialProvider(
                config = config.providers.googleDrive,
                oAuthRefreshService = googleOAuthRefreshService,
                googleWorkspaceServiceAccountKeyProvider = googleWorkspaceServiceAccountKeyProvider,
            )
        }
    }

    val googleApiProvider by lazy {
        GoogleApiProvider()
    }

    val linearTokenProvider by lazy {
        LinearTokenProvider(
            userSecretService = userSecretService,
        )
    }

    val linearAccessComputeService by lazy {
        config.providers.linear?.let {
            LinearAccessComputeService(
                apiProvider = LinearApiProvider(
                    config = it,
                ),
                linearTokenProvider = linearTokenProvider,
            )
        }
    }

    val googleDocumentAccessProvider by lazy {
        googleCredentialProvider?.let {
            GoogleDocumentAccessProvider(
                googleCredentialProvider = it,
                googleApiProvider = googleApiProvider,
            )
        }
    }

    val dataSourceAccessControlFilterFactory by lazy {
        DataSourceAccessControlFilterFactory(
            codaDocumentAccessProvider = CodaDocumentAccessProvider(),
            confluenceDataCenterDocumentAccessProvider = ConfluenceDataCenterDocumentAccessProvider(),
            confluenceDocumentAccessProvider = ConfluenceDocumentAccessProvider(
                atlassianTokenProvider = confluenceAtlassianTokenProvider,
                confluenceCloudApiProvider = confluenceCloudApiProvider,
            ),
            jiraDocumentAccessProvider = JiraDocumentAccessProvider(
                atlassianTokenProvider = jiraAtlassianTokenProvider,
                jiraApiProvider = jiraApiProvider,
            ),
            jiraProjectComputeService = JiraAccessComputeService(
                atlassianTokenProvider = jiraAtlassianTokenProvider,
                jiraApiProvider = jiraApiProvider,
            ),
            jiraDataCenterDocumentAccessProvider = JiraDataCenterDocumentAccessProvider(),
            googleDocumentAccessProvider = googleDocumentAccessProvider,
            linearAccessComputeService = linearAccessComputeService,
            notionDocumentAccessProvider = NotionDocumentAccessProvider(),
        )
    }

    val gitHubActionsClientProvider by lazy {
        GitHubActionsClientProvider(
            scmAppApiFactory = scmAppApiFactory,
        )
    }

    val bitbucketPipelinesClientProvider by lazy {
        BitbucketPipelinesClientProvider(
            scmAuthApiFactory = scmAuthApiFactory,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val gitLabPipelinesClientProvider by lazy {
        GitLabPipelinesClientProvider(
            gitLabCloudConfig = scmConfig.gitlabCloud,
            scmAuthApiFactory = scmAuthApiFactory,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val ciProjectApiFactory by lazy {
        CIProjectApiFactory(
            gitHubActionsClientProvider = gitHubActionsClientProvider,
            bitbucketPipelinesClientProvider = bitbucketPipelinesClientProvider,
            gitLabPipelinesClientProvider = gitLabPipelinesClientProvider,
        )
    }

    val logFocusService by lazy {
        LogFocusService()
    }

    val commitService by lazy {
        ScmCommitService(
            scmRepoApiFactory = scmRepoApiFactory,
            repoFocusService = repoFocusService,
            completionService = completionService,
            repoAccessService = repoAccessService,
        )
    }

    val repoSummaryMLFunction by lazy {
        RepoSummaryMLFunction(
            repoFocusService = repoFocusService,
            repoAccessService = repoAccessService,
            commitService = commitService,
        )
    }

    val topicExpertMappingService by lazy {
        TopicExpertMappingService(
            topicConfig = topicConfig,
            machineLearningModelsApi = machineLearningApiProviders.first().machineLearningModelsApi,
        )
    }

    val semanticDocumentRetriever by lazy {
        SemanticDocumentRetriever(
            embedder = embeddingService,
            semanticSearchDocumentService = semanticSearchDocumentService,
            documentRerankService = documentRerankService,
            dataSourceAccessControlFilterFactory = dataSourceAccessControlFilterFactory,
            repoFocusService = repoFocusService,
            repoAccessService = repoAccessService,
            planCapabilitiesService = planCapabilitiesService,
            slackChannelAccessService = slackChannelAccessService,
        )
    }

    val expertService by lazy {
        ScmExpertService(
            semanticDocumentRetriever = semanticDocumentRetriever,
            templateService = inferenceTemplateService,
            scmCommitService = commitService,
            expertFunctionTimeout = config.search.functions.expertsFunctionTimeout,
            useExpertsUsingTopics = config.search.useExpertsUsingTopics,
            topicExpertMappingService = topicExpertMappingService,
        )
    }

    val threadAccessService by lazy {
        ThreadAccessService(
            repoAccessService = repoAccessService,
            dataSourceAccessControlFilterFactory = dataSourceAccessControlFilterFactory,
        )
    }

    val jiraAccessService by lazy {
        JiraAccessService(
            atlassianTokenProvider = jiraAtlassianTokenProvider,
            jiraApiProvider = jiraApiProvider,
        )
    }

    val mlFunctionMemberService by lazy {
        MLFunctionMemberService(
            completionService = completionService,
            memberService = memberService,
        )
    }

    val memberSummaryMLFunctions by lazy {
        MemberSummaryMLFunctions(
            memberService = memberService,
            mlFunctionMemberService = mlFunctionMemberService,
        )
    }

    val awsLiveQueryMLFunction by lazy {
        if (config.featureFlags.enableAWSLiveQuery) {
            AWSLiveQueryMLFunction(
                lambdaProvider = lambdaProviderFactory.generate(
                    awsClientProvider = AWSClientProvider.from(
                        region = ServiceInitializer.REGION.toRegion(),
                    ),
                ),
                stsProvider = StsProviderViaRpc {
                    RpcFacade.withProxyProvider().forSearchService()
                },
                completionService = completionService,
                awsQueryFunctionTimeout = config.search.functions.awsQueryFunctionTimeout,
            )
        } else {
            null
        }
    }

    val graphRagMLFunctions by lazy {
        if (config.featureFlags.enableGraphRag) {
            GraphRagMLFunctions(
                machinelearningApiProvider = machineLearningApiProviders.first(),
                graphRagFunctionTimeout = config.search.functions.graphRagFunctionTimeout,
            )
        } else {
            null
        }
    }

    val jiraDocConverter by lazy {
        JiraDocConverter(rapidQueryService = rapidServiceProvider.queryService)
    }

    val jiraMLFunctions by lazy {
        JiraMLFunctions(
            dsacFilterFactory = dataSourceAccessControlFilterFactory,
            jiraAccessService = jiraAccessService,
            jiraDocConverter = jiraDocConverter,
            mlFunctionMemberService = mlFunctionMemberService,
        )
    }

    val linearMLFunctions by lazy {
        LinearMLFunctions(
            mlFunctionMemberService = mlFunctionMemberService,
            threadAccessService = threadAccessService,
            threadInsightIndexContentService = threadInsightIndexContentService,
        )
    }

    val githubIssuesMLFunctions by lazy {
        GitHubIssuesMLFunctions(
            threadInsightIndexContentService = threadInsightIndexContentService,
            threadAccessService = threadAccessService,
        )
    }

    val gitHubForkedRepoMLFunctions by lazy {
        GitHubForkedRepoMLFunctions(
            scmAppApiFactory = scmAppApiFactory,
            scmRepoApiFactory = scmRepoApiFactory,
        )
    }

    val prSummaryMLFunctions by lazy {
        PRSummaryMLFunctions(
            commitService = commitService,
        )
    }

    val diffChunker by lazy {
        DiffChunker()
    }

    val prCodeReviewMLFunctions by lazy {
        PRCodeReviewMLFunctions(
            completionService = completionService,
            scmRepoApiFactory = scmRepoApiFactory,
            repoAccessService = repoAccessService,
            repoFocusService = repoFocusService,
            diffChunker = diffChunker,
        )
    }

    val ciMLFunctions by lazy {
        CIMLFunctions(
            commitService = commitService,
            ciFunctionTimeout = config.search.functions.ciFunctionTimeout,
        )
    }

    val fileMLFunctions by lazy {
        FileMLFunctions(
            commitService = commitService,
        )
    }

    val commitsMLFunctions by lazy {
        CommitsMLFunctions(
            commitService = commitService,
        )
    }

    val expertsMLFunctions by lazy {
        ExpertsMLFunctions(
            expertService = expertService,
        )
    }

    val engagementMetricFunctions by lazy {
        EngagementMetricFunctions()
    }

    val timeFilteredDocumentsMLFunctions by lazy {
        TimeFilteredDocumentsMLFunctions(
            embeddingQueryService = standardEmbeddingQueryService,
        )
    }

    val slackTokenService by lazy {
        SlackTokenService(
            userSecretService = userSecretService,
        )
    }

    val slackChannelResolver by lazy {
        SlackChannelResolver()
    }

    val authorizedSlackChannelResolver by lazy {
        AuthorizedSlackChannelResolver(
            slackChannelAccessService = slackChannelAccessService,
            slackChannelResolver = slackChannelResolver,
        )
    }

    val slackConversationSummaryPromptExtractor by lazy {
        SlackConversationSummaryPromptExtractor()
    }

    val slackContentFilterService by lazy {
        SlackContentFilterService(
            completionService = completionService,
        )
    }

    val slackMLFunctions by lazy {
        SlackMLFunctions(
            slackTokenService = slackTokenService,
            authorizedSlackChannelResolver = authorizedSlackChannelResolver,
            slackSummaryFunctionTimeout = config.search.functions.slackSummaryFunctionTimeout,
            slackConversationSummaryPromptExtractor = slackConversationSummaryPromptExtractor,
            slackContentFilterService = slackContentFilterService,
        )
    }

    val confluenceAccessService by lazy {
        ConfluenceAccessService(
            atlassianTokenProvider = confluenceAtlassianTokenProvider,
            confluenceCloudApiProvider = confluenceCloudApiProvider,
        )
    }

    val dataSourcePresetConfigurationService by lazy {
        DataSourcePresetConfigurationServiceImpl(
            planCapabilitiesService = planCapabilitiesService,
        )
    }

    val sampleQuestionGenerator by lazy {
        SampleQuestionGeneratorImpl(
            completionService = completionService,
            repoAccessService = repoAccessService,
            repoFocusService = repoFocusService,
            semanticDocumentRetriever = semanticDocumentRetriever,
            pullRequestInsightIndexContentService = pullRequestInsightIndexContentService,
            promptCompilerService = promptCompilerService,
            scmCommitService = commitService,
            documentTimeout = config.search.documentRetrievalTimeout,
        )
    }

    val suggestedQuestionsMLFunctions by lazy {
        SuggestedQuestionsMLFunctions(
            templateService = inferenceTemplateService,
            sampleQuestionGenerator = sampleQuestionGenerator,
        )
    }

    val dataAccessMLFunctions by lazy {
        DataSourceAccessMLFunctions()
    }

    val asanaDocConverter by lazy {
        AsanaDocConverter(rapidQueryService = rapidServiceProvider.queryService)
    }

    val asanaMLFunctions by lazy {
        AsanaMLFunction(
            dsacFilterFactory = dataSourceAccessControlFilterFactory,
            asanaDocConverter = asanaDocConverter,
            mlFunctionMemberService = mlFunctionMemberService,
        )
    }

    val mlFunctionService by lazy {
        MLFunctionService(
            functions = listOfNotNull(
                awsLiveQueryMLFunction,
                asanaMLFunctions,
                ciMLFunctions,
                commitsMLFunctions,
                engagementMetricFunctions,
                expertsMLFunctions,
                fileMLFunctions,
                githubIssuesMLFunctions,
                graphRagMLFunctions,
                jiraMLFunctions,
                linearMLFunctions,
                memberSummaryMLFunctions,
                prSummaryMLFunctions,
                prCodeReviewMLFunctions,
                repoSummaryMLFunction,
                slackMLFunctions,
                timeFilteredDocumentsMLFunctions,
                suggestedQuestionsMLFunctions,
                dataAccessMLFunctions,
                gitHubForkedRepoMLFunctions,
            ),
            templateService = inferenceTemplateService,
            promptCompilerService = promptCompilerService,
            completionService = completionService,
            globalFunctionTimeout = config.search.functions.globalFunctionTimeout,
        )
    }

    val demoReferenceResolver by lazy {
        DemoReferenceResolver()
    }

    val maliciousQueryDetectionService by lazy {
        MaliciousQueryDetectionService(
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
            completionService = completionService,
        )
    }

    val ragWorthinessDetectionService by lazy {
        RAGWorthinessDetectionService(
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
            completionService = completionService,
        )
    }

    val similarityService by lazy {
        CosineSimilarityCalculator(embeddingService)
    }

    val slackNotifier by lazy {
        SlackNotifier(
            internalSlackConfig = config.internalSlack,
            adminWebConfig = config.adminWeb,
            insiderService = insiderService,
        )
    }

    val restrictedAccessService by lazy {
        RestrictedAccessServiceFactory.fromConfig()
    }

    val orgMaintenance by lazy {
        OrgMaintenance()
    }

    val scmInstallationMaintenance by lazy {
        ScmInstallationMaintenance()
    }

    val scmTeamLifecycleMaintenance by lazy {
        ScmTeamLifecycleMaintenance(
            insiderService = insiderService,
            restrictedAccessService = restrictedAccessService,
            scmAppDelegate = ScmAppDelegateImpl(
                scmAppApiFactory = scmAppApiFactory,
            ),
            orgMaintenance = orgMaintenance,
            scmInstallationMaintenance = scmInstallationMaintenance,
        )
    }

    val identityMaintenance by lazy {
        IdentityMaintenance(
            scmAuthApiFactory = scmAuthApiFactory,
            userSecretService = userSecretService,
        )
    }

    val topicExpertService by lazy {
        TopicExpertService()
    }

    val inactiveFollowupService by lazy {
        InactiveFollowupService(
            notificationEventEnqueueService = notificationEventEnqueueService,
        )
    }

    val slackBotEventEnqueueService by lazy {
        SlackBotEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.slackBotEventsQueueName,
                ),
            ),
        )
    }

    val pendingSlackQuestionService by lazy {
        PendingSlackQuestionService(
            slackBotEventEnqueueService = slackBotEventEnqueueService,
        )
    }

    val onboardingEnablementService by lazy {
        OnboardingEnablementService(
            notificationEventEnqueueService = notificationEventEnqueueService,
            inactiveFollowupService = inactiveFollowupService,
            searchPriorityEventEnqueueService = searchPriorityEventEnqueueService,
            pendingQuestionsService = pendingSlackQuestionService,
        )
    }

    val migrationEventEnqueueService by lazy {
        MigrationEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.migrationEventsQueueName,
                ),
            ),
        )
    }

    val evalService by lazy {
        EvalService(
            inferenceService = inferenceService,
            templateService = inferenceTemplateService,
            promptCompilerService = promptCompilerService,
            completionService = completionService,
            machineLearningApiProvider = machineLearningApiProviders.first(),
        )
    }

    val repoMaintenance by lazy {
        RepoMaintenance()
    }

    val repoCleanupJob by lazy {
        ExclusiveBackgroundJob(
            job = RepoCleanupJob(repoMaintenance),
            lockExpiration = 30.minutes,
        )
    }

    val notionTokenProvider by lazy {
        NotionOauthTokenProvider(
            userSecretService = userSecretService,
        )
    }

    val notionApiProvider by lazy {
        NotionApiProvider(
            config = config.providers.notion ?: return@lazy null,
            oauthTokenProvider = notionTokenProvider,
        )
    }

    val atlassianDataCenterAuthProvider by lazy {
        AtlassianDataCenterAuthProvider(tokenSecretDecryption::decrypt)
    }

    val confluenceDataCenterApiProvider by lazy {
        ConfluenceDataCenterApiProvider(atlassianDataCenterAuthProvider)
    }

    val stackOverflowApiProvider by lazy {
        StackOverflowTeamsApiProvider(
            config = config.providers.stackOverflowTeams,
        )
    }

    val stackOverflowAuthProvider by lazy {
        StackOverflowTeamsAuthProvider(
            userSecretService = userSecretService,
        )
    }

    val webIngestionEventEnqueueService by lazy {
        WebIngestionEventEnqueueService(
            webEventEnqueueService = webEventEnqueueService,
        )
    }

    val webIngestionService by lazy {
        WebIngestionService(
            webIngestionEventEnqueueService = webIngestionEventEnqueueService,
            webIngestionConfig = config.webIngestionConfig,
        )
    }

    val onboardingAlarmJob by lazy {
        ExclusiveBackgroundJob(
            job = OnboardingAlarmJob(slackNotifier = slackNotifier),
        )
    }

    val pullRequestIngestionStatusJob by lazy {
        ExclusiveBackgroundJob(
            job = PullRequestIngestionStatusJob(),
        )
    }

    val peerInviteSuggestionService by lazy {
        PeerInviteSuggestionService(
            notificationEventEnqueueService = notificationEventEnqueueService,
        )
    }

    val embeddingExperimentService by lazy {
        EmbeddingExperimentService(
            documentRerankService = documentRerankService,
            semanticSearchDocumentService = semanticSearchDocumentService,
            templateService = inferenceTemplateService,
            embedder = embeddingService,
        )
    }

    val scmCloneUrlProvider by lazy {
        ScmCloneUrlProvider(
            scmRepoApiFactory = scmRepoApiFactory,
        )
    }

    val questionAnswerThreadService by lazy {
        QuestionAnswerThreadService(
            threadInsightIndexContentService = threadInsightIndexContentService,
        )
    }

    val topicMappingRuleService by lazy {
        TopicMappingRuleService(
            questionAnswerThreadService = questionAnswerThreadService,
        )
    }

    val insightContentModelService by lazy {
        InsightContentModelService(
            threadInsightIndexContentService = threadInsightIndexContentService,
            pullRequestInsightIndexContentService = pullRequestInsightIndexContentService,
        )
    }

    val bootstrappingPromotionService by lazy {
        BootstrappingPromotionService(
            criterion = CompositePromotionCriterion(
                criteria = listOf(
                    RepoCodeIngestionCriterion(),
                    PullRequestIngestionCriterion(),
                    IntegrationsIngestionCriterion(),
                ),
            ),
            onboardingEnablementService = onboardingEnablementService,
        )
    }

    val bootstrappingPromotionJob by lazy {
        ExclusiveBackgroundJob(
            job = BootstrappingPromotionJob(
                bootstrappingPromotionService = bootstrappingPromotionService,
            ),
        )
    }

    val documentRelevanceEvaluatorAgent by lazy {
        DocumentRelevanceEvaluatorAgent(
            completionService = completionService,
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
        )
    }

    val mlFunctionExecutor by lazy {
        MLFunctionExecutor(mlFunctionService)
    }

    val pullRequestDocConverter by lazy {
        PullRequestDocConverter(
            pullRequestInsightIndexContentService = pullRequestInsightIndexContentService,
        )
    }

    val threadDocConverter by lazy {
        ThreadDocConverter(
            threadInsightIndexContentService = threadInsightIndexContentService,
        )
    }

    val confluenceDocConverter by lazy {
        ConfluenceDocConverter(rapidQueryService = rapidServiceProvider.queryService)
    }

    val codaDocConverter by lazy {
        CodaDocConverter(rapidQueryService = rapidServiceProvider.queryService)
    }

    val linkProcessorFactory by lazy {
        LinkProcessorFactory(
            installationResolverFactory = LinkInstallationResolverFactory(
                asanaDocConverter = asanaDocConverter,
                confluenceAccessService = confluenceAccessService,
                jiraAccessService = jiraAccessService,
                jiraDocConverter = jiraDocConverter,
                pullRequestDocConverter = pullRequestDocConverter,
                repoAccessService = repoAccessService,
                scmRepoApiFactory = scmRepoApiFactory,
                threadAccessService = threadAccessService,
                threadDocConverter = threadDocConverter,
                confluenceDocConverter = confluenceDocConverter,
                codaDocConverter = codaDocConverter,
        ),
        )
    }

    val documentEvaluationRetriever by lazy {
        DocumentEvaluationRetriever(
            linkProcessorFactory = linkProcessorFactory,
            semanticDocumentRetriever = semanticDocumentRetriever,
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
            documentRelevanceEvaluatorAgent = documentRelevanceEvaluatorAgent,
            mlFunctionExecutor = mlFunctionExecutor,
            embedder = embeddingService,
        )
    }

    val postRetrievalFilters by lazy {
        listOf(
            GleanFilter(),
        )
    }

    val maintenanceEventEnqueueService by lazy {
        MaintenanceEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.maintenanceEventsQueueName,
                ),
            ),
        )
    }

    val scoreAssetContextCollector by lazy {
        ScoredAssetContextCollector(
            machineLearningApiProvider = machineLearningApiProviders.first(),
        )
    }

    val semanticSearchQueryService by lazy {
        SemanticSearchQueryService(
            semanticDocumentRetriever = semanticDocumentRetriever,
            documentEvaluationRetriever = documentEvaluationRetriever,
            completionService = completionService,
            promptCompilerService = promptCompilerService,
            inPromptReferenceResolver = inPromptReferenceResolver,
            messageReferencesService = messageReferencesService,
            messageDataSourcePresetService = messageDataSourcePresetService,
            responseReferenceResolver = responseReferenceResolver,
            demoReferenceResolver = demoReferenceResolver,
            responseSanitizer = compositeResponseSanitizer,
            documentRerankService = documentRerankService,
            chatCompressionService = chatCompressionService,
            inlineReferencesResolverService = inlineReferencesResolverService,
            inlineReferencesToMarkdownLinksService = inlineReferencesToMarkdownLinksService,
            inlineReferencesCleanupService = inlineReferencesCleanupService,
            referencesExpansionService = referencesExpansionService,
            priorityEventEnqueueService = searchPriorityEventEnqueueService,
            repoExtractorService = repoExtractorService,
            maliciousQueryDetectionService = maliciousQueryDetectionService,
            maliciousQueryDetectionTimeout = config.search.maliciousQueryDetectionTimeout,
            ragWorthinessDetectionService = ragWorthinessDetectionService,
            ragWorthinessDetectionTimeout = config.search.ragWorthinessDetectionTimeout,
            embedder = embeddingService,
            documentRetrievalTimeout = config.search.documentRetrievalTimeout,
            repoExtractionTimeout = config.search.repoExtractionTimeout,
            queryCompressionTimeout = config.search.queryCompressionTimeout,
            postRetrievalFilters = postRetrievalFilters,
            maintenanceEventEnqueueService = maintenanceEventEnqueueService,
            templateService = inferenceTemplateService,
            ragSkipDetectionEnabled = config.featureFlags.enableRagSkipDetection,
            scoredAssetContextCollector = scoreAssetContextCollector,
        )
    }

    val ragRegressionInstanceService by lazy {
        RAGRegressionInstanceService(
            lockProvider = LockProvider(type = LockType.RAGRegressionTest),
            lockDuration = config.search.ragRegressionTestingInterval,
        )
    }

    val regressionTestService by lazy {
        RegressionTestService(
            evalService = evalService,
            enqueueService = searchEventEnqueueService,
            semanticSearchQueryService = semanticSearchQueryService,
            inferenceService = inferenceService,
            templateService = inferenceTemplateService,
            similarityCalculator = similarityService,
            slackNotifier = slackNotifier,
            ragRegressionInstanceService = ragRegressionInstanceService,
        )
    }

    val isQuestionCompletionService by lazy {
        IsQuestionCompletionService(
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
            completionService = completionService,
        )
    }

    val isQuestionRegressionService by lazy {
        IsQuestionRegressionService(
            templateService = inferenceTemplateService,
            isQuestionCompletionService = isQuestionCompletionService,
        )
    }

    val answerEvaluationService by lazy {
        AnswerEvaluationService(
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
            completionService = completionService,
        )
    }

    val isQuestionAnswerRegressionService by lazy {
        IsQuestionAnswerRegressionService(
            templateService = inferenceTemplateService,
            answerEvaluationService = answerEvaluationService,
        )
    }

    val isQuestionAnswerRegressionRunService by lazy {
        IsQuestionAnswerRegressionRunService(
            enqueueService = regressionEventEnqueueService,
            answerEvaluationService = answerEvaluationService,
            templateService = inferenceTemplateService,
        )
    }

    val isQuestionAnswerRegressionRunEvalHandler by lazy {
        IsQuestionAnswerRegressionRunEvalHandler(
            isQuestionAnswerRegressionRunService = isQuestionAnswerRegressionRunService,
        )
    }

    val regressionTestingEventHandler by lazy {
        RegressionTestingEventHandler(
            isQuestionAnswerRegressionRunEvalHandler = isQuestionAnswerRegressionRunEvalHandler,
        )
    }

    val clusterInsightTopicsService by lazy {
        ClusterInsightTopicsService(
            insightContentModelService = insightContentModelService,
            machineLearningApiProvider = machineLearningApiProviders.first(),
            topicConfig = topicConfig,
        )
    }

    val insightTopicsService by lazy {
        DecisionInsightTopicsService(
            clusterInsightTopicsService = clusterInsightTopicsService,
        )
    }

    val insightRefreshService by lazy {
        InsightRefreshService()
    }

    val topicMappingService by lazy {
        DecisionTopicMappingService(
            delegate = StandardTopicMappingService(
                topicMappingRuleService = topicMappingRuleService,
                insightTopicsService = insightTopicsService,
                insightRefreshService = insightRefreshService,
            ),
        )
    }

    val repoTopicIngestionService by lazy {
        RepoTopicIngestionService(
            ingestionInterval = topicConfig.topicDataPipeline.ingestionInterval,
            lastActiveAtThreshold = topicConfig.topicDataPipeline.lastActiveAtThreshold,
            maxReposPerTeam = topicConfig.topicDataPipeline.maxReposPerTeam,
        )
    }

    val ingestionDisablementService by lazy {
        IngestionDisablementService()
    }

    val ingestionProgressServiceProvider by lazy {
        StandardIngestionProgressServiceProvider()
    }

    val ingestionProgressCheckerJob by lazy {
        ExclusiveBackgroundJob(
            job = IngestionProgressCheckerJob(
                ingestionProgressServiceProvider = ingestionProgressServiceProvider,
                notificationEventEnqueueService = notificationEventEnqueueService,
            ),
        )
    }

    val linearEventEnqueueService by lazy {
        LinearEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.linearEventsQueueName,
                ),
            ),
        )
    }

    val scmEventProducer by lazy {
        ScmEventProducer(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.scmEventsQueueName,
                ),
            ),
        )
    }

    val confluenceEventEnqueueService by lazy {
        ConfluenceEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.confluenceEventsQueueName,
                ),
            ),
        )
    }

    val sampleQuestionGeneratorService by lazy {
        SampleQuestionGeneratorService(
            sampleQuestionGenerator = sampleQuestionGenerator,
            templateService = inferenceTemplateService,
            searchPriorityEventEnqueueService = searchPriorityEventEnqueueService,
        )
    }

    val providerInstallationDeletionService by lazy {
        ProviderInstallationDeletionService(
            scmTeamLifecycleMaintenance = scmTeamLifecycleMaintenance,
        )
    }

    val installationDeletionService by lazy {
        StandardInstallationDeletionService(
            providerInstallationDeletionService = providerInstallationDeletionService,
            slackNotifier = slackNotifier,
        )
    }

    val orgDeletionService by lazy {
        OrgDeletionService(
            embeddingStoreFacade = embeddingStoreFacade,
            slackNotifier = slackNotifier,
            insiderService = insiderService,
            installationDeletionService = installationDeletionService,
        )
    }

    val stripeService by lazy {
        StripeService()
    }

    val billingService by lazy {
        BillingService(
            stripeService = stripeService,
            planCapabilitiesService = planCapabilitiesService,
            enableTestBillingActions = config.featureFlags.enableTestBillingActions,
            config = config.billing,
        )
    }

    val billingEventEnqueueService by lazy {
        BillingEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.billingEventsQueueName,
                ),
            ),
        )
    }

    val conversationAnalysisService by lazy {
        ConversationAnalysisService(
            completionService = completionService,
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.feedbackAnalysisQueueName,
                ),
            ),
            inferenceTemplateService = inferenceTemplateService,
            embeddingService = embeddingService,
        )
    }

    val triageEventEnqueueService by lazy {
        TriageEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.ciTriageEventsQueueName,
                ),
                eventPayloadCompressor = EventPayloadCompressor(
                    sqsMessageValidator = SqsMessageSizeValidator(
                        maxMessageLength = 512.kilobytes,
                    ),
                ),
            ),
        )
    }

    val documentEncryptionConfig: DocumentEncryptionConfig by lazy {
        DocumentEncryptionConfig.INSTANCE
    }
    val documentDataEncryption by lazy {
        DocumentDataEncryption(config = documentEncryptionConfig)
    }

    val s3ProviderFactory by lazy {
        StandardS3ProviderFactory()
    }

    val integrationDataStore by lazy {
        IntegrationDataStore(
            s3ProviderFactory = s3ProviderFactory,
            awsClientProvider = awsClientProvider,
            bucketName = integrationDataConfig.integrationData.s3.bucket,
            documentDataEncryption = documentDataEncryption,
        )
    }

    val jiraIssuePersistenceService by lazy {
        JiraIssuePersistenceService(
            integrationDataStore = integrationDataStore,
        )
    }

    val productFeedbackService by lazy {
        ProductFeedbackService(slackNotifier = slackNotifier)
    }

    val personMcpToolOverrideService by lazy {
        PersonMcpToolOverrideService()
    }

    val personMcpTemplateOverrideService by lazy {
        PersonMcpTemplateOverrideService(
            personMcpTemplateOverrideStore = Stores.personMcpTemplateOverrideStore,
            templateStore = Stores.mlInferenceTemplateStore,
        )
    }

    val adminSessionIdentityResolver = AdminSessionIdentityResolver(
        adminConfig = adminConfig,
    )

    val codaApiProvider by lazy {
        CodaApiProvider(config = config.providers.coda)
    }

    val codaTokenProvider by lazy {
        CodaTokenProvider(userSecretService = userSecretService)
    }

    val codaPagePersistenceService by lazy {
        CodaPagePersistenceService(integrationDataStore)
    }

    val orgBillingAccessService by lazy {
        OrgBillingAccessService()
    }

    val ciLicenseService by lazy {
        CILicenseService(
            billingEventEnqueueService = billingEventEnqueueService,
            orgBillingAccessService = orgBillingAccessService,
            licensingStartAt = ciConfig.triage.licensingStartAt?.let(Instant::parse),
        )
    }

    val codaEventEnqueueService by lazy {
        CodaEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.codaEventsQueueName,
                ),
            ),
        )
    }

    install(Compression)
    install(IgnoreTrailingSlash)
    install(SSE)
    install(Resources) {
        installSerializer()
    }
    configureOpenSearch(
        lockProvider = openSearchLockProvider,
        config = config,
        openSearchIndexLoader = openSearchIndexLoader,
        openSearchConfig = openSearchConfig,
    )
    configurePinecone(
        lockProvider = pineconeLockProvider,
        pineconeConfig = pineconeConfig,
        pineconeIndexLoader = pineconeIndexLoader,
    )
    configureCaching()
    configureMonitoring(insiderService = insiderService, sessionIdentityResolver = adminSessionIdentityResolver)
    configureSecurity(adminAuthenticationProvider = adminAuthenticationProvider)
    configureDatabaseData()
    configureRouting(
        adminAuthenticationProvider = adminAuthenticationProvider,
        atlassianDataCenterAuthProvider = atlassianDataCenterAuthProvider,
        billingEventEnqueueService = billingEventEnqueueService,
        billingService = billingService,
        ciLicenseService = ciLicenseService,
        ciProjectApiFactory = ciProjectApiFactory,
        codaApiProvider = codaApiProvider,
        codaEventEnqueueService = codaEventEnqueueService,
        codaPagePersistenceService = codaPagePersistenceService,
        codaTokenProvider = codaTokenProvider,
        confluenceAtlassianTokenProvider = confluenceAtlassianTokenProvider,
        confluenceCloudApiProvider = confluenceCloudApiProvider,
        confluenceDataCenterApiProvider = confluenceDataCenterApiProvider,
        confluenceEventEnqueueService = confluenceEventEnqueueService,
        conversationAnalysisService = conversationAnalysisService,
        dataSourcePresetConfigurationService = dataSourcePresetConfigurationService,
        embeddingEncoding = embeddingEncoding,
        embeddingEventEnqueueService = embeddingEventEnqueueService,
        embeddingExperimentService = embeddingExperimentService,
        embeddingStatsFacade = embeddingStatsFacade,
        embeddingStoreFacade = embeddingStoreFacade,
        enableTestBillingActions = config.featureFlags.enableTestBillingActions,
        googleApiProvider = googleApiProvider,
        googleCredentialProvider = googleCredentialProvider,
        googleVerifier = googleVerifier,
        identityMaintenance = identityMaintenance,
        indexingAndEmbeddingService = indexingAndEmbeddingService,
        inferenceService = inferenceService,
        inferenceTemplateService = inferenceTemplateService,
        ingestionDisablementService = ingestionDisablementService,
        insiderService = insiderService,
        installationDeletionService = installationDeletionService,
        isQuestionAnswerRegressionRunService = isQuestionAnswerRegressionRunService,
        isQuestionAnswerRegressionService = isQuestionAnswerRegressionService,
        isQuestionRegressionService = isQuestionRegressionService,
        jiraApiProvider = jiraApiProvider,
        jiraAtlassianTokenProvider = jiraAtlassianTokenProvider,
        jiraIssuePersistenceService = jiraIssuePersistenceService,
        linearEventEnqueueService = linearEventEnqueueService,
        linkProcessorFactory = linkProcessorFactory,
        logFocusService = logFocusService,
        machineLearningApiProvider = machineLearningApiProviders.first(),
        migrationEventEnqueueService = migrationEventEnqueueService,
        mlFunctionService = mlFunctionService,
        notificationEventEnqueueService = notificationEventEnqueueService,
        notionApiProvider = notionApiProvider,
        onboardingEnablementService = onboardingEnablementService,
        orgDeletionService = orgDeletionService,
        passwordVerifier = passwordVerifier,
        peerInviteSuggestionService = peerInviteSuggestionService,
        personMcpTemplateOverrideService = personMcpTemplateOverrideService,
        personMcpToolOverrideService = personMcpToolOverrideService,
        productFeedbackService = productFeedbackService,
        profileService = profileService,
        pullRequestEventEnqueueService = pullRequestEventEnqueueService,
        pullRequestIngestionStateMachineProvider = pullRequestIngestionStateMachineProvider,
        rapidQueryService = rapidServiceProvider.queryService,
        regressionTestService = regressionTestService,
        repoCodeIngestionService = repoCodeIngestionService,
        repoComputeService = repoComputeService,
        repoFocusService = repoFocusService,
        repoPullRequestSummaryService = repoPullRequestSummaryService,
        repoTopicIngestionService = repoTopicIngestionService,
        sampleQuestionGenerator = sampleQuestionGenerator,
        sampleQuestionGeneratorService = sampleQuestionGeneratorService,
        scmAppApiFactory = scmAppApiFactory,
        scmCloneUrlProvider = scmCloneUrlProvider,
        scmEventProducer = scmEventProducer,
        scmRepoApiFactory = scmRepoApiFactory,
        scmTeamApiFactory = scmTeamApiFactory,
        scmTeamLifecycleMaintenance = scmTeamLifecycleMaintenance,
        scmUserApiFactory = scmUserApiFactory,
        searchPriorityEventEnqueueService = searchPriorityEventEnqueueService,
        semanticDocumentRetriever = semanticDocumentRetriever,
        semanticSearchQueryService = semanticSearchQueryService,
        serviceLifecycle = serviceLifecycle,
        stackOverflowApiProvider = stackOverflowApiProvider,
        stackOverflowAuthProvider = stackOverflowAuthProvider,
        summarizationEventEnqueueService = summarizationEventEnqueueService,
        topicEventEnqueueService = topicEventEnqueueService,
        topicExpertService = topicExpertService,
        topicMappingService = topicMappingService,
        triageEventEnqueueService = triageEventEnqueueService,
        userSecretServiceResolver = userSecretServiceResolver,
        versionService = versionService,
        webIngestionService = webIngestionService,
    )
    configureSerialization()
    configureStatusPages()
    configureTracing()
    configureBackgroundJobs(
        jobs = buildList {
            add(
                PollingBackgroundJob(
                    interval = 1.seconds,
                    job = ExclusiveBackgroundJob(
                        OrgBillingJob(billingService = billingService),
                    ),
                ),
            )
            add(
                PollingBackgroundJob(
                    interval = 6.hours,
                    job = ExclusiveBackgroundJob(
                        UserEngagementMetricsJob(userEngagementService = UserEngagementService()),
                    ),
                ),
            )
            add(
                PollingBackgroundJob(
                    interval = 7.hours,
                    job = ExclusiveBackgroundJob(
                        GrowthMetricsJob(growthService = GrowthService()),
                    ),
                ),
            )
            when {
                config.featureFlags.enableBuildIngestion -> add(
                    PollingBackgroundJob(
                        interval = 1.minutes,
                        job = ExclusiveBackgroundJob(
                            BuildIngestionJob(versionService = versionService),
                        ),
                    ),
                )
            }
            add(
                PollingBackgroundJob(
                    interval = 2.minutes,
                    job = bootstrappingPromotionJob,
                ),
            )
            add(
                PollingBackgroundJob(
                    interval = 10.minutes,
                    job = onboardingAlarmJob,
                ),
            )
            add(
                PollingBackgroundJob(
                    interval = 1.hours,
                    job = ExclusiveBackgroundJob(
                        IntercomInternalUserAttributeJob(internalUserAttributeService = internalUserAttributeService),
                    ),
                ),
            )
            add(
                PollingBackgroundJob(
                    interval = 1.minutes,
                    job = repoCleanupJob,
                ),
            )
            add(
                PollingBackgroundJob(
                    interval = 5.minutes,
                    job = pullRequestIngestionStatusJob,
                ),
            )
            add(
                PollingBackgroundJob(
                    interval = 5.minutes,
                    job = ingestionProgressCheckerJob,
                ),
            )
            add(
                PollingBackgroundJob(
                    interval = 1.minutes,
                    job = ExclusiveBackgroundJob(
                        PersonPreferencesMetricsJob(),
                    ),
                ),
            )
            add(
                PollingBackgroundJob(
                    interval = 1.minutes,
                    job = ExclusiveBackgroundJob(
                        SlackChannelPreferencesMetricsJob(),
                    ),
                ),
            )
            add(
                PollingBackgroundJob(
                    interval = 1.seconds,
                    job = ExclusiveBackgroundJob(
                        MigrationEventProcessingJob(
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.migrationEventsQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(
                                    handler = MigrationEventHandler(
                                        archiveLowRelevanceThreadsForRepoEventHandler = ArchiveLowRelevanceThreadsForRepoEventHandler(
                                            migrationEventEnqueueService = migrationEventEnqueueService,
                                        ),
                                        archiveLowRelevanceThreadsForPullRequestEventHandler = ArchiveLowRelevanceThreadsForPullRequestEventHandler(
                                            indexingAndEmbeddingService = indexingAndEmbeddingService,
                                        ),
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            )
            repeat(CONSUMER_COUNT) {
                add(
                    PollingBackgroundJob(
                        interval = 500.milliseconds,
                        job = RegressionEventJob(
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.regressionTestingEventsQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(regressionTestingEventHandler),
                            ),
                        ),
                    ),
                )
                add(
                    PollingBackgroundJob(
                        interval = 500.milliseconds,
                        job = createBackgroundJob(
                            name = "Feedback Analysis Event Job",
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.feedbackAnalysisQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(conversationAnalysisService),
                            ),
                        ),
                    ),
                )
            }
        },
    )
    configureJvmMetrics()
}
