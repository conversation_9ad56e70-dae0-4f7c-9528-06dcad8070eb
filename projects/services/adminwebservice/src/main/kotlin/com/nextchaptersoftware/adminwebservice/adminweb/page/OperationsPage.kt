package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.renderActionMenu
import com.nextchaptersoftware.adminwebservice.adminweb.service.ResetDataIngestionService
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.utils.IngestionAlert
import com.nextchaptersoftware.adminwebservice.auth.AdminRoutingContextExtensions.getAdminIdentity
import com.nextchaptersoftware.adminwebservice.migration.ConfluenceSiteMigrator
import com.nextchaptersoftware.adminwebservice.migration.HackOrgMemberMigrator
import com.nextchaptersoftware.adminwebservice.migration.HardcodedMemberMigrator
import com.nextchaptersoftware.adminwebservice.migration.JiraSiteMigrator
import com.nextchaptersoftware.adminwebservice.migration.Migrator
import com.nextchaptersoftware.adminwebservice.migration.OrgMemberDeferralMigrator
import com.nextchaptersoftware.adminwebservice.migration.OrgMemberQuestionsMigrator
import com.nextchaptersoftware.adminwebservice.migration.ProductFeedbackResponseDedupeMigrator
import com.nextchaptersoftware.billing.utils.OrgBillingPlanService
import com.nextchaptersoftware.bot.services.BotAccountService
import com.nextchaptersoftware.bot.services.InstallationBotAccountService
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.AuditLogDAO
import com.nextchaptersoftware.db.models.AuditLogModel
import com.nextchaptersoftware.db.models.GitHubIssuesIngestionModel
import com.nextchaptersoftware.db.models.IngestionDAO
import com.nextchaptersoftware.db.models.IngestionModel
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.Cohort
import com.nextchaptersoftware.db.stores.GrowthMetricsStore
import com.nextchaptersoftware.db.stores.NotionObjectStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.UserEngagementMetricsStore
import com.nextchaptersoftware.ingestion.redis.IngestionDisablementService
import com.nextchaptersoftware.insider.InsiderServiceInterface
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.scm.services.ProfileService
import com.nextchaptersoftware.userengagement.CIAnalyticsService
import com.nextchaptersoftware.userengagement.GrowthService
import com.nextchaptersoftware.userengagement.SlackQuestionAnalyticsService
import com.nextchaptersoftware.userengagement.UserEngagementService
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.nextchaptersoftware.utils.nullIfBlank
import com.nextchaptersoftware.utils.startOfDay
import io.ktor.http.ContentDisposition
import io.ktor.http.HttpHeaders
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.response.header
import io.ktor.server.response.respondFile
import io.ktor.server.routing.RoutingContext
import kotlin.time.Duration
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Instant
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withTimeout
import kotlinx.html.div
import kotlinx.html.h1
import mu.KotlinLogging
import org.jetbrains.exposed.sql.update

private val LOGGER = KotlinLogging.logger {}

object OperationsPage {

    @Suppress("LongMethod")
    suspend fun RoutingContext.renderOperationsPage(
        page: AdminPage,
        ingestionDisablementService: IngestionDisablementService,
    ) {
        val path = call.request.path()

        val metricActions = listOf(
            MenuItem(
                href = "$path/backfillEngagementMetrics",
                label = "Backfill activity metrics",
                description = """
                Regenerates all activity metrics.
                """.trimIndent(),
                style = BootstrapStyle.Info,
            ),
            MenuItem(
                href = "$path/backfillGrowthMetrics",
                label = "Backfill growth metrics",
                description = """
                Regenerates all growth metrics.
                """.trimIndent(),
                style = BootstrapStyle.Info,
            ),
            MenuItem(
                href = "$path/backfillGrowthSlackMetrics",
                label = "Backfill Slack growth metrics",
                description = """
                Regenerates Slack growth metrics only.
                """.trimIndent(),
                style = BootstrapStyle.Info,
            ),
            MenuItem(
                href = "$path/backfillGrowthCIMetrics",
                label = "Backfill CI growth metrics",
                description = """
                Regenerates CI growth metrics only.
                """.trimIndent(),
                style = BootstrapStyle.Info,
            ),
            MenuItem(
                href = "$path/clearEngagementMetrics",
                label = "Clear activity metrics",
                description = """
                Clears all activity metrics.
                """.trimIndent(),
                style = BootstrapStyle.Danger,
            ),
            MenuItem(
                href = "$path/clearGrowthMetrics",
                label = "Clear growth metrics",
                description = """
                Clears all growth metrics.
                """.trimIndent(),
                style = BootstrapStyle.Danger,
            ),
            MenuItem(
                href = "$path/clearGrowthSlackMetrics",
                label = "Clear Slack growth metrics",
                description = """
                Clears Slack growth metrics only.
                """.trimIndent(),
                style = BootstrapStyle.Danger,
            ),
            MenuItem(
                href = "$path/clearGrowthCIMetrics",
                label = "Clear CI growth metrics",
                description = """
                Clears CI growth metrics only.
                """.trimIndent(),
                style = BootstrapStyle.Danger,
            ),
        )

        val schemaActions = listOf(
            MenuItem(
                href = "$path/SchemaMigrator",
                label = "Schema migration",
                description = """
                Runs a schema migration to drop columns, indices and tables.
                """.trimIndent(),
                style = BootstrapStyle.Danger,
            ),
        )

        val ingestionDisabled = ingestionDisablementService.isDisabled()

        val ingestionActions = listOf(
            MenuItem(
                href = "$path/toggleIngestion",
                label = "${if (ingestionDisabled) "Re-enable" else "Disable"} Ingestion",
                description = """
                ${if (ingestionDisabled) "Re-enable" else "Disable"} all jobs related to ingestion of pull requests and integrations.
                """.trimIndent(),
            ),
            MenuItem(
                href = "$path/reingestAllConfluenceSites",
                label = "Reingest all Confluence Sites",
                description = """
                Reingests all confluence sites (with low priority embedding events).
                """.trimIndent(),
            ),
            MenuItem(
                href = "$path/reingestAllJiraSites",
                label = "Reingest all Jira Sites",
                description = """
                Reingests all Jira sites for active teams (with low priority embedding events).
                """.trimIndent(),
            ),
            MenuItem(
                href = "$path/reingestAllGitHubIssues",
                label = "Reingest all GitHub issues",
                description = """
                Reingests all GitHub issues for all teams.
                """.trimIndent(),
            ),
            MenuItem(
                href = "$path/resetDataIngestionForTeamsNeedingReprocessing",
                label = "Reset Data Ingestion Needing Reprocessing",
                description = """
                Resets data ingestion for teams needing reprocessing.
                """.trimIndent(),
            ),
            MenuItem(
                href = "$path/setLastSyncedForJiraIngestions",
                label = "Set Last Synced for Jira Ingestions",
                description = """
                Sets lastSynced for all Jira ingestions to 1 day before last synced.
                """.trimIndent(),
            ),
            MenuItem(
                href = "$path/retryErroredNotionPageIngestions",
                label = "Retry Errored Notion Page Ingestions",
                description = """
                Retries ingestion of Notion pages that errored the last time we attempted ingestion.
                """.trimIndent(),
            ),
        )

        val miscActions = listOf(
            MenuItem(
                href = "$path/refreshProfilesMissingPrimaryEmail",
                label = "Refresh Profiles Missing Primary Email",
                description = """
                Refreshes profiles (IdentityModels) of sign-in capable providers missing primary email.
                """.trimIndent(),
                style = BootstrapStyle.Primary,
            ),
            MenuItem(
                href = "$path/refreshBotProfile",
                label = "Refresh Bot Profile",
                description = """
                Refreshes the Unblocked bot profile (name, avatar, etc).
                """.trimIndent(),
                style = BootstrapStyle.Primary,
            ),
        )

        val modelMigrations = listOf(
            MenuItem(
                href = "$path/migrateHackOrgMember",
                label = "Perform hack org member migration",
                description = """
                    Used to migrate org members
                """.trimIndent(),
            ),
            MenuItem(
                href = "$path/migrateMembers",
                label = "Perform hack member migration",
                description = """
                    Used to migrate members
                """.trimIndent(),
            ),
            MenuItem(
                href = "$path/backfillLastQuestionAskedAt",
                label = "Backfill last question asked at",
                description = """
                    Backfills last question asked at for org members
                """.trimIndent(),
            ),
            MenuItem(
                href = "$path/migrateDeferralDates",
                label = "Update Product Feedback Deferrals",
                description = """
                    Updates productFeedbackDeferredUntil: null->null, expired->null, future->July 11, 2025
                """.trimIndent(),
            ),
            MenuItem(
                href = "$path/deduplicateFeedbackResponses",
                label = "Deduplicate Product Feedback Responses",
                description = """
                    Removes duplicate feedback responses with same org+person, keeping oldest record
                """.trimIndent(),
            ),
            MenuItem(
                href = "$path/setAuditLogSearchableText",
                label = "Set Audit Log Searchable Text",
                description = """
                    Sets searchable text for audit logs
                """.trimIndent(),
            ),
            MenuItem(
                href = "$path/mergeDuplicatePeople",
                label = "Merge duplicate people",
                description = """
                    Merges duplicate people into a single person record by matching email addresses.
                """.trimIndent(),
            ),
        )

        val billingActions = listOf(
            MenuItem(
                href = "$path/downloadBusinessPlanOrgsCsv",
                label = "Download CSV of all orgs on a business plan",
                description = """
                    Downloads a CSV of all orgs on a business plan
                """.trimIndent(),
                style = BootstrapStyle.Primary,
            ),
        )

        val adminIdentity = call.getAdminIdentity()

        val additionalAlerts = IngestionAlert.get(ingestionDisablementService.isDisabled())

        call.respondHtmlTemplate(WideContentTemplate(page, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, additionalAlerts = additionalAlerts) }
            content {
                h1 { +page.label }

                div(classes = "row mb-5") {
                    div(classes = "col-6") {
                        div(classes = "mt-5") { renderActionMenu(ingestionActions, "Ingestion") }
                        div(classes = "mt-5") { renderActionMenu(miscActions, "Miscellaneous") }
                        div(classes = "mt-5") { renderActionMenu(modelMigrations, "Model Migrations") }
                    }
                    div(classes = "col-6") {
                        div(classes = "mt-5") { renderActionMenu(metricActions, "Metrics") }
                        div(classes = "mt-5") { renderActionMenu(billingActions, "Billing") }
                        div(classes = "mt-5") { renderActionMenu(schemaActions, "Schema") }
                    }
                }
            }
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun executeMigrator(
        migrator: Migrator,
        timeout: Duration = 60.minutes,
    ) {
        GlobalScope.launch {
            withLoggingContextAsync(
                "migrator" to migrator.javaClass.simpleName,
                "timeout" to timeout,
            ) {
                LOGGER.debugAsync { "Migrator: starting.." }
                runSuspendCatching {
                    withTimeout(timeout) {
                        migrator.execute()
                    }
                }
                    .onFailure {
                        LOGGER.debugAsync(t = it) { "Migrator: failure" }
                    }
                    .onSuccess {
                        LOGGER.debugAsync { "Migrator: success" }
                    }
                    .getOrNull()
            }
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun migrateHackOrgMember(
        hackOrgMemberMigrator: HackOrgMemberMigrator = HackOrgMemberMigrator(),
    ) {
        GlobalScope.launch {
            withTimeout(240.minutes) {
                hackOrgMemberMigrator.migrate()
            }
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun migrateMembers(
        hardcodedMemberMigrator: HardcodedMemberMigrator = HardcodedMemberMigrator(),
    ) {
        GlobalScope.launch {
            withTimeout(240.minutes) {
                hardcodedMemberMigrator.migrate()
            }
        }
    }

    suspend fun clearEngagementMetrics(userEngagementMetricsStore: UserEngagementMetricsStore = Stores.userEngagementMetricsStore) {
        userEngagementMetricsStore.clear()
    }

    suspend fun clearGrowthMetrics(growthMetricsStore: GrowthMetricsStore = Stores.growthMetricsStore) {
        growthMetricsStore.clearAll()
    }

    suspend fun clearGrowthSlackMetrics(growthMetricsStore: GrowthMetricsStore = Stores.growthMetricsStore) {
        growthMetricsStore.clearSlackOnly()
    }

    suspend fun clearGrowthCIMetrics(growthMetricsStore: GrowthMetricsStore = Stores.growthMetricsStore) {
        growthMetricsStore.clearCIOnly()
    }

    suspend fun backfillGrowthMetrics(
        growthService: GrowthService = GrowthService(),
        slackQuestionAnalyticsService: SlackQuestionAnalyticsService = SlackQuestionAnalyticsService(),
        ciAnalyticsService: CIAnalyticsService = CIAnalyticsService(),
    ) {
        slackQuestionAnalyticsService.backfillSlackMemberFirstInteractions()
        ciAnalyticsService.backfillCIMemberFirstInteractions()

        val now = Instant.nowWithMicrosecondPrecision().startOfDay()
        val startDate = now.minus(364.days)

        var date = startDate
        while (date < now) {
            Cohort.entries.forEach { cohort ->
                growthService.calculateGrowthMetrics(priorTo = date, cohort = cohort)
            }
            date += 1.days
        }
    }

    suspend fun backfillGrowthSlackMetrics(
        growthService: GrowthService = GrowthService(),
        slackQuestionAnalyticsService: SlackQuestionAnalyticsService = SlackQuestionAnalyticsService(),
    ) {
        slackQuestionAnalyticsService.backfillSlackMemberFirstInteractions()

        Cohort.entries.forEach { cohort ->
            growthService.calculateSlackGrowthMetricsOnly(cohort = cohort)
        }
    }

    suspend fun backfillGrowthCIMetrics(
        growthService: GrowthService = GrowthService(),
        ciAnalyticsService: CIAnalyticsService = CIAnalyticsService(),
    ) {
        ciAnalyticsService.backfillCIMemberFirstInteractions()

        Cohort.entries.forEach { cohort ->
            growthService.calculateCIGrowthMetricsOnly(cohort = cohort)
        }
    }

    suspend fun backfillEngagementMetrics(userEngagementService: UserEngagementService = UserEngagementService()) {
        val now = Instant.nowWithMicrosecondPrecision().startOfDay()
        val startDate = now.minus(364.days)

        var date = startDate
        while (date < now) {
            Cohort.entries.forEach { cohort ->
                userEngagementService.calculateUserEngagementMetrics(priorTo = date, cohort = cohort)
            }
            date += 1.days
        }
    }

    suspend fun refreshBotProfile(
        botAccountService: BotAccountService = InstallationBotAccountService(),
    ) {
        botAccountService.upsertBotAccountIdentities()
    }

    suspend fun refreshProfilesMissingPrimaryEmail(
        profileService: ProfileService,
    ) {
        profileService.refreshProfilesMissingPrimaryEmail()
    }

    suspend fun toggleIngestion(
        ingestionDisablementService: IngestionDisablementService,
    ) {
        ingestionDisablementService.toggle()
    }

    suspend fun reingestAllConfluenceSites(
        migrator: ConfluenceSiteMigrator = ConfluenceSiteMigrator(),
    ) {
        migrator.reingestAllConfluenceSites()
    }

    @OptIn(DelicateCoroutinesApi::class)
    fun reingestAllJiraSites() {
        GlobalScope.launch {
            withTimeout(30.minutes) {
                JiraSiteMigrator().reingestAllJiraSites()
            }
        }
    }

    suspend fun resetDataIngestionForTeamsNeedingReprocessing(
        resetDataIngestionService: ResetDataIngestionService = ResetDataIngestionService(),
    ) {
        resetDataIngestionService.resetDataIngestionForTeamsNeedingReprocessing()
    }

    suspend fun backfillLastQuestionAskedAt() {
        OrgMemberQuestionsMigrator.backfillLastQuestionAskedAt()
    }

    suspend fun migrateDeferralDates() {
        OrgMemberDeferralMigrator.migrateDeferralDates()
    }

    suspend fun deduplicateFeedbackResponses() {
        ProductFeedbackResponseDedupeMigrator.deduplicateFeedbackResponses()
    }

    suspend fun backfillGoogleConnectingMember() {
        suspendedTransaction { // TODO to this for all teams
            val installation = InstallationDAO[InstallationId.fromString("7acddd28-59f9-4dba-9481-64a3f62c71df")]
            val teamMember = MemberDAO[MemberId.fromString("e600c871-62f7-40b7-9637-aed60ba99afa")]
            installation.connectingMember = teamMember.idValue
        }
    }

    suspend fun setLastSyncedForJiraIngestions() {
        suspendedTransaction {
            IngestionDAO.find {
                IngestionModel.provider eq Provider.Jira
            }.toList()
        }.forEach { ingestion ->
            suspendedTransaction {
                ingestion.lastSynced = ingestion.lastSynced?.let { it - 7.days }
            }
        }
    }

    suspend fun retryErroredNotionPageIngestions(
        notionObjectStore: NotionObjectStore = Stores.notionObjectStore,
    ) {
        notionObjectStore.setStatusToNullForAllErroredPages()
    }

    suspend fun reingestAllGitHubIssues() {
        suspendedTransaction {
            GitHubIssuesIngestionModel.update {
                it[this.bulkIngestionComplete] = false
            }
        }
    }

    suspend fun RoutingContext.downloadBusinessPlanOrgsCsv(
        insiderService: InsiderServiceInterface,
        orgBillingPlanService: OrgBillingPlanService = OrgBillingPlanService(insiderService = insiderService),
    ) {
        val data = orgBillingPlanService.generateBusinessPlanOrgsCsv()

        call.response.header(
            HttpHeaders.ContentDisposition,
            ContentDisposition.Attachment.withParameter(ContentDisposition.Parameters.FileName, "business_plan_orgs.csv")
                .toString(),
        )
        val tempFile = kotlin.io.path.createTempFile("business_plan_orgs", ".csv").toFile()
        tempFile.writeText(data)
        call.respondFile(tempFile)
    }

    @OptIn(DelicateCoroutinesApi::class)
    suspend fun setAuditLogSearchableText() {
        GlobalScope.launch {
            withTimeout(30.minutes) {
                suspendedTransaction {
                    AuditLogDAO.all().map { it.asDataModel() }
                }.onEach { log ->
                    val searchableText = listOfNotNull(
                        log.type.category.displayName,
                        log.provider?.displayName,
                        log.action.joinToString(" "),
                    )

                    suspendedTransaction {
                        AuditLogModel.update({ AuditLogModel.id eq log.id }) {
                            it[AuditLogModel.searchableText] = searchableText.joinToString(" ").lowercase().nullIfBlank()
                        }
                    }
                }
            }
        }

        LOGGER.debugAsync { "setAuditLogSearchableText: complete" }
    }

    suspend fun mergeDuplicatePeople() {
        Stores.personStore.findAndMergeDuplicatePeople()
    }
}
