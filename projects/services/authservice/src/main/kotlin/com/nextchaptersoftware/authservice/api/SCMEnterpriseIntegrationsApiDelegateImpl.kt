package com.nextchaptersoftware.authservice.api

import com.nextchaptersoftware.access.RestrictedAccessServiceInterface
import com.nextchaptersoftware.api.SCMEnterpriseIntegrationsApiDelegateInterface
import com.nextchaptersoftware.api.auth.services.AccessValidation
import com.nextchaptersoftware.api.auth.services.LoginService
import com.nextchaptersoftware.api.auth.services.url.login.ScmLoginSource
import com.nextchaptersoftware.api.models.BitbucketDataCenterApplication
import com.nextchaptersoftware.api.models.EnterpriseProvider
import com.nextchaptersoftware.api.models.EnterpriseProviderOutcome
import com.nextchaptersoftware.api.models.EnterpriseProviderResult
import com.nextchaptersoftware.api.models.EnterpriseProvidersUrls
import com.nextchaptersoftware.api.models.GitLabSelfHostedApplication
import com.nextchaptersoftware.api.models.UnregisteredEnterpriseProvider
import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.api.models.converters.asProvider
import com.nextchaptersoftware.api.services.enterprise.ScmEnterpriseService
import com.nextchaptersoftware.auditlog.auditService
import com.nextchaptersoftware.billing.services.downgrade.CapabilityValidation
import com.nextchaptersoftware.config.AuthenticationConfig
import com.nextchaptersoftware.db.models.EnterpriseAppConfig
import com.nextchaptersoftware.db.models.EnterpriseAppConfigId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PlanCapabilityType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.EnterpriseAppConfigStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.NotFoundException
import com.nextchaptersoftware.ktor.utils.RepoUrl
import com.nextchaptersoftware.ktor.utils.UrlExtensions
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.models.orgId
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmNoAuthApi
import com.nextchaptersoftware.scm.ScmNoAuthApiFactory
import com.nextchaptersoftware.scm.ScmWeb
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.apps.AppManifestNegotiationStore
import com.nextchaptersoftware.scm.apps.AppManifestProvider
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.models.HostAvailabilityStatus
import com.nextchaptersoftware.serialization.Serialization.encode
import com.nextchaptersoftware.utils.asUUID
import io.ktor.http.URLBuilder
import io.ktor.http.Url
import io.ktor.http.authority
import io.ktor.http.hostWithPort
import io.ktor.http.set
import io.ktor.server.routing.RoutingContext
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import mu.KotlinLogging
import org.openapitools.server.Resources

private val LOGGER = KotlinLogging.logger {}

class SCMEnterpriseIntegrationsApiDelegateImpl(
    private val accessValidation: AccessValidation,
    private val appManifestNegotiationStore: AppManifestNegotiationStore = AppManifestNegotiationStore(),
    private val appManifestProvider: AppManifestProvider,
    private val authenticationConfig: AuthenticationConfig,
    private val capabilityValidation: CapabilityValidation,
    private val enterpriseAppConfigStore: EnterpriseAppConfigStore = Stores.enterpriseAppConfigStore,
    private val loginService: LoginService,
    private val restrictedAccessService: RestrictedAccessServiceInterface,
    private val scmConfig: ScmConfig,
    private val scmEnterpriseService: ScmEnterpriseService,
    private val scmNoAuthApiFactory: ScmNoAuthApiFactory,
    private val scmWebFactory: ScmWebFactory,
) : SCMEnterpriseIntegrationsApiDelegateInterface {

    override suspend fun getEnterpriseProvider(
        context: RoutingContext,
        input: Resources.getEnterpriseProvider,
    ): EnterpriseProvider {
        val config = enterpriseAppConfigStore.getByIdOrNull(input.enterpriseProviderId.let(::EnterpriseAppConfigId))
            ?: throw NotFoundException("Enterprise provider not found.")

        return config.getAsApiModel(
            clientState = input.clientState,
            authRedirectOverrideUrl = input.overrideRedirectUrl?.toString(),
        )
    }

    override suspend fun listOrgEnterpriseProviders(
        context: RoutingContext,
        input: Resources.listOrgEnterpriseProviders,
    ): List<EnterpriseProvider> {
        return enterpriseAppConfigStore
            .findForOrg(
                orgId = context.orgId,
                provider = input.provider.asProvider(),
            )
            .map {
                it.getAsApiModel(
                    clientState = input.clientState,
                    authRedirectOverrideUrl = input.overrideRedirectUrl?.toString(),
                )
            }
    }

    override suspend fun createGitHubEnterpriseProvider(
        context: RoutingContext,
        input: Resources.createGitHubEnterpriseProvider,
    ): EnterpriseProvider {
        return commonCreateOrgGitHubEnterpriseProvider(
            context = context,
            orgId = null,
            state = input.state,
            code = input.code,
        )
    }

    override suspend fun createOrgGitHubEnterpriseProvider(
        context: RoutingContext,
        input: Resources.createOrgGitHubEnterpriseProvider,
    ): EnterpriseProvider {
        val orgId = context.orgId
        capabilityValidation.requiresCapability(orgId, PlanCapabilityType.GitHubEnterprise)
        return commonCreateOrgGitHubEnterpriseProvider(
            context = context,
            orgId = orgId,
            state = input.state,
            code = input.code,
        ).also {
            context.auditCreatedEnterpriseApp(
                provider = Provider.GitHubEnterprise,
                displayName = it.displayName,
            )
        }
    }

    private suspend fun commonCreateOrgGitHubEnterpriseProvider(
        context: RoutingContext,
        orgId: OrgId?,
        state: String,
        code: String,
    ): EnterpriseProvider {
        accessValidation.expectAdminRole(context)

        val negotiation = appManifestNegotiationStore.getState(state.asUUID())
            ?: throw IllegalArgumentException("Manifest state not found, possibly expired.")

        restrictedAccessService.checkHostAccess(negotiation.enterpriseAuthority)

        return scmEnterpriseService.createGitHubEnterpriseProvider(
            orgId = orgId,
            code = code,
            negotiation = negotiation,
        )
    }

    override suspend fun createGitLabEnterpriseProvider(
        context: RoutingContext,
        input: Resources.createGitLabEnterpriseProvider,
        body: GitLabSelfHostedApplication,
    ): EnterpriseProviderOutcome {
        return commonCreateOrgGitLabEnterpriseProvider(
            context = context,
            orgId = null,
            body = body,
        )
    }

    override suspend fun createOrgGitLabEnterpriseProvider(
        context: RoutingContext,
        input: Resources.createOrgGitLabEnterpriseProvider,
        body: GitLabSelfHostedApplication,
    ): EnterpriseProviderOutcome {
        val orgId = context.orgId
        capabilityValidation.requiresCapability(orgId, PlanCapabilityType.GitLabDedicated)
        return commonCreateOrgGitLabEnterpriseProvider(
            context = context,
            orgId = orgId,
            body = body,
        ).also {
            context.auditCreatedEnterpriseApp(
                provider = Provider.GitLabSelfHosted,
                displayName = body.displayName,
                condition = it.isSuccess,
            )
        }
    }

    private suspend fun commonCreateOrgGitLabEnterpriseProvider(
        context: RoutingContext,
        orgId: OrgId?,
        body: GitLabSelfHostedApplication,
    ): EnterpriseProviderOutcome {
        accessValidation.expectAdminRole(context)
        restrictedAccessService.checkHostAccess(body.displayName)

        return scmEnterpriseService.createGitLabEnterpriseProvider(
            orgId = orgId,
            applicationId = body.applicationId.trim(),
            displayName = body.displayName,
            encryptedApplicationSecret = body.encryptedApplicationSecret,
        )
    }

    override suspend fun createBitbucketDataCenterEnterpriseProvider(
        context: RoutingContext,
        input: Resources.createBitbucketDataCenterEnterpriseProvider,
        body: BitbucketDataCenterApplication,
    ): EnterpriseProviderOutcome {
        return commonCreateOrgBitbucketDataCenterEnterpriseProvider(
            context = context,
            orgId = null,
            body = body,
        )
    }

    override suspend fun createOrgBitbucketDataCenterEnterpriseProvider(
        context: RoutingContext,
        input: Resources.createOrgBitbucketDataCenterEnterpriseProvider,
        body: BitbucketDataCenterApplication,
    ): EnterpriseProviderOutcome {
        val orgId = context.orgId
        capabilityValidation.requiresCapability(orgId, PlanCapabilityType.BitbucketDataCenter)
        return commonCreateOrgBitbucketDataCenterEnterpriseProvider(
            context = context,
            orgId = orgId,
            body = body,
        ).also {
            context.auditCreatedEnterpriseApp(
                provider = Provider.BitbucketDataCenter,
                displayName = body.displayName,
                condition = it.isSuccess,
            )
        }
    }

    private suspend fun commonCreateOrgBitbucketDataCenterEnterpriseProvider(
        context: RoutingContext,
        orgId: OrgId?,
        body: BitbucketDataCenterApplication,
    ): EnterpriseProviderOutcome {
        accessValidation.expectAdminRole(context)
        restrictedAccessService.checkHostAccess(body.displayName)

        return scmEnterpriseService.createBitbucketDataCenterEnterpriseProvider(
            orgId = orgId,
            applicationId = body.applicationId.trim(),
            displayName = body.displayName,
            encryptedApplicationSecret = body.encryptedApplicationSecret,
        )
    }

    override suspend fun findEnterpriseProviders(
        context: RoutingContext,
        input: Resources.findEnterpriseProviders,
        body: EnterpriseProvidersUrls,
    ): List<EnterpriseProviderResult> {
        return findEnterpriseProvidersInternal(
            inputUrls = body.inputUrls,
            input = input,
            orgId = null,
        )
    }

    override suspend fun findOrgEnterpriseProviders(
        context: RoutingContext,
        input: Resources.findOrgEnterpriseProviders,
        body: EnterpriseProvidersUrls,
    ): List<EnterpriseProviderResult> {
        return findEnterpriseProvidersInternal(
            inputUrls = body.inputUrls,
            input = Resources.findEnterpriseProviders(
                clientSecret = input.clientSecret,
                agentType = input.agentType,
                redirectUrl = input.redirectUrl,
                manifestRedirectUrl = input.manifestRedirectUrl,
                clientState = input.clientState,
                provider = input.provider,
            ),
            orgId = context.orgId,
        )
    }

    private suspend fun findEnterpriseProvidersInternal(
        inputUrls: List<String>,
        input: Resources.findEnterpriseProviders,
        orgId: OrgId?,
    ): List<EnterpriseProviderResult> {
        return inputUrls
            .map { it.trim() }
            .distinct()
            .map { inputUrl -> Pair(inputUrl, sanitizeUrl(inputUrl)) }
            .let { urlPairs: List<Pair<String, Url?>> ->

                val invalidUrlResults = urlPairs
                    .filter { (_, sanitizedUrl) -> sanitizedUrl == null }
                    .map { (inputUrl, _) ->
                        EnterpriseProviderResult(
                            inputUrl = inputUrl,
                            status = EnterpriseProviderResult.Status.invalidInputUrl,
                            userMessage = """
                            Provide a valid hostname and port in the format "hostname:port".
                            """.trimIndent(),
                        )
                    }

                val validUrlResults = coroutineScope {
                    urlPairs
                        .filter { (_, sanitizedUrl) -> sanitizedUrl != null }
                        .map { Pair(it.first, it.second ?: throw IllegalStateException("Impossible")) }
                        .groupBy { (_, sanitizedUrl) -> sanitizedUrl.authority }
                        .map { (commonAuthority, urlPairs) ->
                            val result: Deferred<EnterpriseProviderResult> = urlPairs.first().let { (inputUrl, sanitizedUrl) ->
                                async {
                                    withLoggingContextAsync("authority" to commonAuthority) {
                                        lookupEnterpriseProvider(
                                            input = input,
                                            inputUrl = inputUrl,
                                            sanitizedUrl = sanitizedUrl,
                                        ) ?: detectEnterprise(
                                            input = input,
                                            inputUrl = inputUrl,
                                            sanitizedUrl = sanitizedUrl,
                                            orgId = orgId,
                                        )
                                    }
                                }
                            }
                            Pair(result, urlPairs)
                        }
                        .flatMap { (deferredResult: Deferred<EnterpriseProviderResult>, urlPairs: List<Pair<String, Url>>) ->
                            urlPairs.map { (inputUrl, _) ->
                                deferredResult.await().copy(inputUrl = inputUrl)
                            }
                        }
                }

                validUrlResults + invalidUrlResults
            }
    }

    private suspend fun lookupEnterpriseProvider(
        input: Resources.findEnterpriseProviders,
        inputUrl: String,
        sanitizedUrl: Url,
    ): EnterpriseProviderResult? {
        return enterpriseAppConfigStore.findByHostAndPort(sanitizedUrl.hostWithPort, input.provider?.asProvider())?.let { config ->
            LOGGER.debugAsync("appConfig" to config.id) {
                "Found already registered enterprise provider"
            }
            EnterpriseProviderResult(
                inputUrl = inputUrl,
                status = EnterpriseProviderResult.Status.ok,
                unregisteredProvider = null,
                registeredProvider = EnterpriseProvider(
                    id = config.id.value,
                    displayName = sanitizedUrl.authority,
                    provider = config.provider.asApiModel(),
                    oauthUrl = loginService.buildLoginUrl(
                        loginSource = ScmLoginSource(Scm.fromProvider(config.provider, config.id)),
                        clientSecret = input.clientSecret,
                        agentType = input.agentType,
                        authRedirectOverrideUrl = input.redirectUrl,
                        clientState = input.clientState,
                    ).asString,
                ),
            )
        }
    }

    /**
     * Detects an enterprise provider by trying to connect to each supported enterprise server type.
     * Choose the enterprise provider that is ok, or the first one if none are ok.
     */
    private suspend fun detectEnterprise(
        input: Resources.findEnterpriseProviders,
        inputUrl: String,
        sanitizedUrl: Url,
        orgId: OrgId?,
    ): EnterpriseProviderResult {
        return when (val provider = input.provider?.asProvider()) {
            null -> {
                Provider.entries
                    .filter { it.isEnterprise }
                    .filter { it.isScmProvider }
                    .map { provider ->
                        detectEnterpriseAsProvider(
                            input = input,
                            inputUrl = inputUrl,
                            sanitizedUrl = sanitizedUrl,
                            provider = provider,
                            orgId = orgId,
                        )
                    }
                    .sortedWith { a, b ->
                        when {
                            a.status == b.status -> 0
                            a.status == EnterpriseProviderResult.Status.ok -> -1
                            b.status == EnterpriseProviderResult.Status.ok -> 1
                            else -> 0
                        }
                    }
                    .first()
            }

            else -> {
                detectEnterpriseAsProvider(
                    input = input,
                    inputUrl = inputUrl,
                    sanitizedUrl = sanitizedUrl,
                    provider = provider,
                    orgId = orgId,
                )
            }
        }
    }

    @Suppress("LongMethod")
    private suspend fun detectEnterpriseAsProvider(
        input: Resources.findEnterpriseProviders,
        inputUrl: String,
        sanitizedUrl: Url,
        provider: Provider,
        orgId: OrgId?,
    ): EnterpriseProviderResult {
        val scmNoAuthApi = scmNoAuthApiFactory.fromAuthority(
            orgIds = setOfNotNull(orgId),
            authority = sanitizedUrl.hostWithPort,
            provider = provider,
        )

        check(scmNoAuthApi is ScmNoAuthApi.WithHostAvailability) {
            "Not applicable"
        }

        return when (val hostAvailability = scmNoAuthApi.getHostAvailability()) {
            HostAvailabilityStatus.HostNotReachable -> {
                LOGGER.debugAsync { "HostNotReachable" }
                EnterpriseProviderResult(
                    inputUrl = inputUrl,
                    status = EnterpriseProviderResult.Status.endpointNotReachable,
                    userMessage = """
                        Unblocked is unable to establish a connection to your enterprise server (${sanitizedUrl.hostWithPort}).
                        You need to ensure that your firewall is configured to allow incoming HTTPS connections
                        from each of the IP addresses listed below that Unblocked uses to access your server.
                    """.trimIndent(),
                )
            }

            HostAvailabilityStatus.HostnameNotFound -> {
                LOGGER.debugAsync { "HostnameNotFound" }
                EnterpriseProviderResult(
                    inputUrl = inputUrl,
                    status = EnterpriseProviderResult.Status.endpointNotFound,
                    userMessage = """
                        Unblocked is unable to find your enterprise server because the hostname cannot be located.
                        Make sure that your internal enterprise server DNS is configured to be publicly accessible for a successful connection.
                    """.trimIndent(),
                )
            }

            HostAvailabilityStatus.InvalidSslCertificate -> {
                LOGGER.debugAsync { "InvalidSslCertificate" }
                EnterpriseProviderResult(
                    inputUrl = inputUrl,
                    status = EnterpriseProviderResult.Status.invalidSslCertificate,
                    userMessage = """
                        Unblocked is unable to establish a connection with your enterprise server due to an SSL certificate issue.
                        This could happen if your enterprise server certificate is self-signed, expired,
                        or issued by a certificate authority that is not trusted by a public root certificate authority.
                    """.trimIndent(),
                )
            }

            is HostAvailabilityStatus.HostTemporarilyUnavailable -> {
                LOGGER.debugAsync("statusCode" to hostAvailability.statusCode.value) { "HostTemporarilyUnavailable" }
                EnterpriseProviderResult(
                    inputUrl = inputUrl,
                    status = EnterpriseProviderResult.Status.endpointNotAvailable,
                    userMessage = """
                        Unblocked is unable to establish a connection with your enterprise server (${sanitizedUrl.hostWithPort}).
                        This could happen if your enterprise server is undergoing maintenance or is temporarily unavailable.
                    """.trimIndent(),
                )
            }

            is HostAvailabilityStatus.Ok -> {
                LOGGER.debugAsync { "Detected unregistered enterprise provider" }
                EnterpriseProviderResult(
                    inputUrl = inputUrl,
                    status = EnterpriseProviderResult.Status.ok,
                    unregisteredProvider = when (provider) {
                        Provider.GitHubEnterprise -> UnregisteredEnterpriseProvider(
                            displayName = sanitizedUrl.authority,
                            orgName = RepoUrl.parseOrNull(sanitizedUrl)?.repoOwner,
                            provider = provider.asApiModel(),
                            manifestUrl = appManifestProvider.getAppManifestUrl(sanitizedUrl, input).toString(),
                            manifestBody = appManifestProvider.getAppManifestBody(input.manifestRedirectUrl).encode(),
                        )

                        Provider.GitLabSelfHosted -> UnregisteredEnterpriseProvider(
                            displayName = sanitizedUrl.authority,
                            orgName = RepoUrl.parseOrNull(sanitizedUrl)?.repoOwner,
                            provider = provider.asApiModel(),
                            oauthRedirectUrl = authenticationConfig.authRedirectUrl,
                            encryptionPublicKey = scmConfig.gitlabSelfHosted.appSecretsPublicKey,
                            /**
                             * LIMITATION: GitLab Self-Managed applications are registered at the user-level.
                             * GitLab Self-Managed applications can be registered at the group- or user-level,
                             * but since we do not know the group name, we will always provide the user-level URL.
                             *  - GROUP-level
                             *    https://gitlab.secops.getunblocked.com/groups/{GROUP_NAME}/-/settings/applications
                             *  - USER-level
                             *    https://gitlab.secops.getunblocked.com/-/user_settings/applications
                             */
                            manifestUrl = "https://${sanitizedUrl.authority}/-/user_settings/applications",
                            manifestBody = "", // Ideally would be null, but the API spec requires a non-null value.
                        )

                        Provider.BitbucketDataCenter -> UnregisteredEnterpriseProvider(
                            displayName = sanitizedUrl.authority,
                            orgName = RepoUrl.parseOrNull(sanitizedUrl)?.repoOwner,
                            provider = provider.asApiModel(),
                            manifestUrl = "https://${sanitizedUrl.authority}/plugins/servlet/applinks/listApplicationLinks",
                            manifestBody = "", // Ideally would be null, but the API spec requires a non-null value.
                            oauthRedirectUrl = authenticationConfig.authRedirectUrl,
                            encryptionPublicKey = scmConfig.bitbucketDataCenter.appSecretsPublicKey,
                        )

                        Provider.Asana,
                        Provider.Aws,
                        Provider.AwsIdentityCenter,
                        Provider.AzureDevOps,
                        Provider.Bitbucket,
                        Provider.BitbucketPipelines,
                        Provider.Buildkite,
                        Provider.CircleCI,
                        Provider.Coda,
                        Provider.Confluence,
                        Provider.ConfluenceDataCenter,
                        Provider.CustomIntegration,
                        Provider.GenericSaml,
                        Provider.GitHub,
                        Provider.GitHubActions,
                        Provider.GitLab,
                        Provider.GitLabPipelines,
                        Provider.GoogleDrive,
                        Provider.GoogleDriveWorkspace,
                        Provider.GoogleWorkspace,
                        Provider.Jenkins,
                        Provider.Jira,
                        Provider.JiraDataCenter,
                        Provider.Linear,
                        Provider.MicrosoftEntra,
                        Provider.MicrosoftTeams,
                        Provider.Notion,
                        Provider.Okta,
                        Provider.PingOne,
                        Provider.Slack,
                        Provider.StackOverflowTeams,
                        Provider.Unblocked,
                        Provider.Web,
                        -> throw IllegalStateException("Unexpected provider: $provider")
                    },
                )
            }

            is HostAvailabilityStatus.UnknownIssue -> {
                LOGGER.debugAsync("cause" to hostAvailability.cause) { "Failed to detect enterprise provider" }
                EnterpriseProviderResult(
                    inputUrl = inputUrl,
                    status = EnterpriseProviderResult.Status.unknownProvider,
                    userMessage = """
                        This does not appear to be a ${provider.displayName} server.
                    """.trimIndent(),
                )
            }
        }
    }

    private fun sanitizeUrl(input: String): Url? {
        if (input.isEmpty()) {
            return null
        }

        return runSuspendCatching {
            // parse by treating it as a repo url first, then as an arbitrary url, then as a hostname and port.
            val url = RepoUrl.parseOrNull(input)?.webHtmlUrl?.asUrl
                ?: UrlExtensions.parseOrNull(input)
                ?: return null

            // for consistency, always lowercase the hostname
            val sanitizedUrl = URLBuilder(url)
            sanitizedUrl.set(host = url.host.lowercase())
            sanitizedUrl.build()
        }.getOrNull()
    }

    private fun EnterpriseAppConfig.getAsApiModel(
        clientState: String? = null,
        authRedirectOverrideUrl: String? = null,
    ): EnterpriseProvider {
        val scm = Scm.fromProvider(provider, id)
        val scmWeb: ScmWeb = scmWebFactory.fromEnterpriseApp(this)
        return EnterpriseProvider(
            id = id.value,
            displayName = authority,
            provider = provider.asApiModel(),
            oauthUrl = loginService.buildLoginUrl(
                loginSource = ScmLoginSource(scm),
                clientSecret = null,
                agentType = null,
                authRedirectOverrideUrl = authRedirectOverrideUrl,
                clientState = clientState,
            ).asString,
            installUrl = scmWeb.appInstallGeneralUrl?.asString,
        )
    }
}

private suspend fun RoutingContext.auditCreatedEnterpriseApp(
    provider: Provider,
    displayName: String,
    condition: Boolean = true,
) {
    if (condition) {
        auditService().createdEnterpriseApp(
            provider = provider,
            displayName = displayName,
        )
    }
}

private val EnterpriseProviderOutcome.isSuccess: Boolean
    get() = status == EnterpriseProviderOutcome.Status.success
