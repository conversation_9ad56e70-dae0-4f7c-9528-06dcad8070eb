package com.nextchaptersoftware.analytics

import com.nextchaptersoftware.db.ModelBuilders.makeMLInference
import com.nextchaptersoftware.db.ModelBuilders.makeMessage
import com.nextchaptersoftware.db.ModelBuilders.makeMessageFeedback
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makeSlackChannel
import com.nextchaptersoftware.db.ModelBuilders.makeThread
import com.nextchaptersoftware.db.common.DateTruncatePrecision
import com.nextchaptersoftware.db.models.FeedbackType
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgMemberDAO
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.nextchaptersoftware.utils.startOfDay
import java.time.ZoneId
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Instant
import kotlinx.datetime.TimeZone
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class AnalyticsMcpInvocationsTest : DatabaseTestsBase() {
    private val analyticsAnswers = AnalyticsAnswers()

    @Test
    fun answerCountByChannel() = suspendingDatabaseTest {
        val org = makeOrg()
        val member = makeOrgMember(org = org)
        val channel1 = makeSlackChannel(name = "channel1")
        val channel2 = makeSlackChannel(name = "channel2")

        makeMLInference(org = org, questionerOrgMember = member, botMessage = makeMessage(thread = makeThread(org = org, slackChannel = channel1)))
        makeMLInference(org = org, questionerOrgMember = member, botMessage = makeMessage(thread = makeThread(org = org, slackChannel = channel2)))
        makeMLInference(org = org, questionerOrgMember = member, botMessage = makeMessage(thread = makeThread(org = org, slackChannel = channel2)))

        analyticsAnswers.answerCountByChannel(orgId = org.idValue, since = null, limit = 5).also {
            assertThat(it).hasSize(2)
            assertThat(it).isEqualTo(
                listOf(
                    DataSegment.ByChannel(count = 2, channelId = channel2.idValue, channelName = "channel2"),
                    DataSegment.ByChannel(count = 1, channelId = channel1.idValue, channelName = "channel1"),
                ),
            )
        }
        analyticsAnswers.answerCount(orgId = org.idValue, since = null).also {
            assertThat(it).isEqualTo(3)
        }
    }

    @Test
    fun answerCountByProductAgent() = suspendingDatabaseTest {
        val org = makeOrg()
        val member = makeOrgMember(org = org)
        makeMLInference(org = org, botMessage = makeMessage(), questionerOrgMember = member, productAgent = ProductAgentType.Dashboard)
        makeMLInference(org = org, botMessage = makeMessage(), questionerOrgMember = member, productAgent = ProductAgentType.Dashboard)
        makeMLInference(org = org, botMessage = makeMessage(), questionerOrgMember = member, productAgent = ProductAgentType.Desktop)
        makeMLInference(org = org, botMessage = makeMessage(), questionerOrgMember = member, productAgent = ProductAgentType.Hub)
        makeMLInference(org = org, botMessage = makeMessage(), questionerOrgMember = member, productAgent = null)
        analyticsAnswers.answerCountByProductAgent(orgId = org.idValue, since = null).also {
            assertThat(it).hasSize(2)
            assertThat(it).isEqualTo(
                listOf(
                    DataSegment.ByProductAgent(count = 3, productAgentType = ProductAgentType.Dashboard),
                    DataSegment.ByProductAgent(count = 2, productAgentType = ProductAgentType.Desktop),
                ),
            )
        }
        analyticsAnswers.answerCount(orgId = org.idValue, since = null).also {
            assertThat(it).isEqualTo(5)
        }
    }

    @Test
    fun dailyAnswerCountByFeedback() = suspendingDatabaseTest {
        val org = makeOrg()
        val now = Instant.nowWithMicrosecondPrecision().startOfDay()
        val questionerOrgMember = makeOrgMember(org = org)

        val oneDayAgo = now.minus(1.days).also {
            makeAnswerFeedback(org = org, member = questionerOrgMember, createdAt = it)
            makeAnswerFeedback(org = org, member = questionerOrgMember, createdAt = it, feedbacks = listOf(FeedbackType.Negative))
            makeAnswerFeedback(org = org, member = questionerOrgMember, createdAt = it, feedbacks = listOf(FeedbackType.Negative))
            makeAnswerFeedback(org = org, member = questionerOrgMember, createdAt = it)
        }

        val twoDaysAgo = now.minus(2.days).also {
            makeAnswerFeedback(org = org, member = questionerOrgMember, createdAt = it)
        }

        analyticsAnswers.timeSeriesAnswerCountByFeedback(
            orgId = org.idValue,
            precision = DateTruncatePrecision.day,
            since = null,
            timeZone = TimeZone.currentSystemDefault(),
        ).also {
            assertThat(it).hasSize(3)
            assertThat(it).containsExactlyInAnyOrder(
                AnalyticsFeedbackTuple(
                    date = twoDaysAgo,
                    feedbackType = FeedbackType.None,
                    count = 1,
                ),
                AnalyticsFeedbackTuple(
                    date = oneDayAgo,
                    feedbackType = FeedbackType.None,
                    count = 2,
                ),
                AnalyticsFeedbackTuple(
                    date = oneDayAgo,
                    feedbackType = FeedbackType.Negative,
                    count = 2,
                ),
            )
        }
        analyticsAnswers.answerCount(orgId = org.idValue, since = null).also {
            assertThat(it).isEqualTo(5)
        }
    }

    @Test
    fun `dailyAnswerCountByFeedback timezone alignment`() = suspendingDatabaseTest {
        val org = makeOrg()
        val baseZoneId = ZoneId.of("Z")
        val nowUTC = Instant.nowWithMicrosecondPrecision().startOfDay(zoneId = baseZoneId)
        val questionerOrgMember = makeOrgMember(org = org)

        val oneDayAgoUTC = nowUTC.minus(1.days).also {
            makeAnswerFeedback(org = org, member = questionerOrgMember, createdAt = it)
        }

        val twoDaysAgoUTC = nowUTC.minus(2.days).also {
            makeAnswerFeedback(org = org, member = questionerOrgMember, createdAt = it)
            makeAnswerFeedback(org = org, member = questionerOrgMember, createdAt = it)
        }

        analyticsAnswers.timeSeriesAnswerCountByFeedback(
            orgId = org.idValue,
            precision = DateTruncatePrecision.day,
            since = null,
            timeZone = TimeZone.of(baseZoneId.id),
        ).also {
            assertThat(it).hasSize(2)
            assertThat(it).containsExactlyInAnyOrder(
                AnalyticsFeedbackTuple(
                    date = twoDaysAgoUTC,
                    feedbackType = FeedbackType.None,
                    count = 2,
                ),
                AnalyticsFeedbackTuple(
                    date = oneDayAgoUTC,
                    feedbackType = FeedbackType.None,
                    count = 1,
                ),
            )
        }

        val otherZoneId = ZoneId.of("Australia/Sydney")
        analyticsAnswers.timeSeriesAnswerCountByFeedback(
            orgId = org.idValue,
            precision = DateTruncatePrecision.day,
            since = null,
            timeZone = TimeZone.of(otherZoneId.id),
        ).also {
            assertThat(it).hasSize(2)
            assertThat(it).containsExactlyInAnyOrder(
                AnalyticsFeedbackTuple(
                    date = twoDaysAgoUTC.startOfDay(zoneId = otherZoneId),
                    feedbackType = FeedbackType.None,
                    count = 2,
                ),
                AnalyticsFeedbackTuple(
                    date = oneDayAgoUTC.startOfDay(zoneId = otherZoneId),
                    feedbackType = FeedbackType.None,
                    count = 1,
                ),
            )
        }
        analyticsAnswers.answerCount(orgId = org.idValue, since = null).also {
            assertThat(it).isEqualTo(3)
        }
    }

    @Test
    fun `dailyAnswerCountByFeedback takes latest feedback per message`() = suspendingDatabaseTest {
        val org = makeOrg()
        val now = Instant.nowWithMicrosecondPrecision().startOfDay()
        val questionerOrgMember = makeOrgMember(org = org)

        val oneDayAgo = now.minus(1.days).also {
            makeAnswerFeedback(
                org = org,
                member = questionerOrgMember,
                createdAt = it,
                feedbacks = listOf(FeedbackType.Negative, FeedbackType.Positive),
            )
            makeAnswerFeedback(
                org = org,
                member = questionerOrgMember,
                createdAt = it,
                feedbacks = listOf(FeedbackType.Neutral, FeedbackType.Positive),
            )
        }

        val twoDaysAgo = now.minus(2.days).also {
            makeAnswerFeedback(
                org = org,
                member = questionerOrgMember,
                createdAt = it,
                feedbacks = listOf(FeedbackType.Negative),
            )
            makeAnswerFeedback(
                org = org,
                member = questionerOrgMember,
                createdAt = it,
                feedbacks = listOf(FeedbackType.Neutral, FeedbackType.Positive),
            )
        }

        analyticsAnswers.timeSeriesAnswerCountByFeedback(
            orgId = org.idValue,
            precision = DateTruncatePrecision.day,
            since = null,
            timeZone = TimeZone.currentSystemDefault(),
        ).also {
            assertThat(it).hasSize(3)
            assertThat(it).containsExactlyInAnyOrder(
                AnalyticsFeedbackTuple(
                    date = oneDayAgo,
                    feedbackType = FeedbackType.Positive,
                    count = 2,
                ),
                AnalyticsFeedbackTuple(
                    date = twoDaysAgo,
                    feedbackType = FeedbackType.Positive,
                    count = 1,
                ),
                AnalyticsFeedbackTuple(
                    date = twoDaysAgo,
                    feedbackType = FeedbackType.Negative,
                    count = 1,
                ),
            )
        }
        analyticsAnswers.answerCount(orgId = org.idValue, since = null).also {
            assertThat(it).isEqualTo(4)
        }
    }

    /**
     * Make an inference and message, with multiple feedbacks.
     * Each feedback is created at a different time incrementing by 1 minute, so that the last item in [feedbacks] is the most recent.
     */
    private suspend fun makeAnswerFeedback(
        org: OrgDAO,
        member: OrgMemberDAO,
        createdAt: Instant,
        feedbacks: List<FeedbackType> = emptyList(),
    ) {
        val message = makeMessage()
        val now = Instant.nowWithMicrosecondPrecision()
        feedbacks.forEachIndexed { index, feedbackType ->
            makeMessageFeedback(org = org, message = message, feedbackType = feedbackType, createdAt = now.plus(index.minutes))
        }
        makeMLInference(org = org, createdAt = createdAt, botMessage = message, questionerOrgMember = member)
    }
}
