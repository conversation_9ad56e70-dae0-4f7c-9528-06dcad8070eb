@file:Suppress("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FunctionParameterNaming")

package com.nextchaptersoftware.db

import com.nextchaptersoftware.compress.compress
import com.nextchaptersoftware.compress.serializeAndCompress
import com.nextchaptersoftware.db.ModelFactory.randomId
import com.nextchaptersoftware.db.ModelFactory.randomSha1
import com.nextchaptersoftware.db.ModelFactory.randomStrings
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.common.EntityExtensions
import com.nextchaptersoftware.db.models.ActivityType
import com.nextchaptersoftware.db.models.AnswerConcisenessPreference
import com.nextchaptersoftware.db.models.AnswerDepthPreference
import com.nextchaptersoftware.db.models.AnswerMetricDAO
import com.nextchaptersoftware.db.models.AnswerMetricId
import com.nextchaptersoftware.db.models.AnswerTonePreference
import com.nextchaptersoftware.db.models.ApiKeyAudience
import com.nextchaptersoftware.db.models.ArchivedReason
import com.nextchaptersoftware.db.models.ArchivedReferenceDAO
import com.nextchaptersoftware.db.models.ArchivedReferenceId
import com.nextchaptersoftware.db.models.AsanaProjectAccessDAO
import com.nextchaptersoftware.db.models.AsanaProjectAccessId
import com.nextchaptersoftware.db.models.AsanaProjectDAO
import com.nextchaptersoftware.db.models.AsanaProjectId
import com.nextchaptersoftware.db.models.AsanaProjectIngestionType
import com.nextchaptersoftware.db.models.AsanaProjectTaskDAO
import com.nextchaptersoftware.db.models.AsanaTaskAccessDAO
import com.nextchaptersoftware.db.models.AsanaTaskAccessId
import com.nextchaptersoftware.db.models.AsanaTaskDAO
import com.nextchaptersoftware.db.models.AsanaTaskId
import com.nextchaptersoftware.db.models.AsanaTeamDAO
import com.nextchaptersoftware.db.models.AsanaTeamMembershipDAO
import com.nextchaptersoftware.db.models.AsanaWorkspaceDAO
import com.nextchaptersoftware.db.models.AsanaWorkspaceId
import com.nextchaptersoftware.db.models.AssetDAO
import com.nextchaptersoftware.db.models.AssetId
import com.nextchaptersoftware.db.models.AssetRelationshipDAO
import com.nextchaptersoftware.db.models.AssetRelationshipId
import com.nextchaptersoftware.db.models.AssetRelationshipType
import com.nextchaptersoftware.db.models.AuditLogActorType
import com.nextchaptersoftware.db.models.AuditLogDAO
import com.nextchaptersoftware.db.models.AuditLogDetail
import com.nextchaptersoftware.db.models.AuditLogType
import com.nextchaptersoftware.db.models.AuthenticationStateDAO
import com.nextchaptersoftware.db.models.AuthenticationStateId
import com.nextchaptersoftware.db.models.BuildDAO
import com.nextchaptersoftware.db.models.BuildId
import com.nextchaptersoftware.db.models.BuildJobDAO
import com.nextchaptersoftware.db.models.BuildJobFailureClassification
import com.nextchaptersoftware.db.models.BuildJobFailureInsufficientLogsReason
import com.nextchaptersoftware.db.models.BuildJobFailureStatsDAO
import com.nextchaptersoftware.db.models.BuildJobId
import com.nextchaptersoftware.db.models.BuildResult
import com.nextchaptersoftware.db.models.BuildStatus
import com.nextchaptersoftware.db.models.BuildTriageDAO
import com.nextchaptersoftware.db.models.BuildTriageExecutionDAO
import com.nextchaptersoftware.db.models.BuildTriageExecutionId
import com.nextchaptersoftware.db.models.BuildTriageExecutionState
import com.nextchaptersoftware.db.models.BuildTriageFilterStage
import com.nextchaptersoftware.db.models.BuildTriageId
import com.nextchaptersoftware.db.models.BuildTriageReviewStatus
import com.nextchaptersoftware.db.models.BuildTriageState
import com.nextchaptersoftware.db.models.CIProjectDAO
import com.nextchaptersoftware.db.models.CIProjectId
import com.nextchaptersoftware.db.models.CIScmDAO
import com.nextchaptersoftware.db.models.CIScmId
import com.nextchaptersoftware.db.models.CITokenDAO
import com.nextchaptersoftware.db.models.CITokenId
import com.nextchaptersoftware.db.models.CIWebhookEventDAO
import com.nextchaptersoftware.db.models.ClientCapabilityDAO
import com.nextchaptersoftware.db.models.ClientQuantityDAO
import com.nextchaptersoftware.db.models.CodaDocDAO
import com.nextchaptersoftware.db.models.CodaDocId
import com.nextchaptersoftware.db.models.CodaGroupDAO
import com.nextchaptersoftware.db.models.CodaGroupId
import com.nextchaptersoftware.db.models.CodaGroupMemberDAO
import com.nextchaptersoftware.db.models.CodaGroupMemberId
import com.nextchaptersoftware.db.models.CodaOrganizationDAO
import com.nextchaptersoftware.db.models.CodaOrganizationId
import com.nextchaptersoftware.db.models.CodaResourceDAO
import com.nextchaptersoftware.db.models.CodaResourceId
import com.nextchaptersoftware.db.models.CodaResourceType
import com.nextchaptersoftware.db.models.CodaWorkspaceDAO
import com.nextchaptersoftware.db.models.CodaWorkspaceId
import com.nextchaptersoftware.db.models.CodaWorkspaceMemberDAO
import com.nextchaptersoftware.db.models.CodaWorkspaceMemberId
import com.nextchaptersoftware.db.models.CodeReviewExclusionDAO
import com.nextchaptersoftware.db.models.CodeReviewExclusionId
import com.nextchaptersoftware.db.models.CollectionDAO
import com.nextchaptersoftware.db.models.CollectionDocumentDAO
import com.nextchaptersoftware.db.models.CollectionDocumentId
import com.nextchaptersoftware.db.models.CollectionId
import com.nextchaptersoftware.db.models.ConfluenceGroupDAO
import com.nextchaptersoftware.db.models.ConfluenceGroupId
import com.nextchaptersoftware.db.models.ConfluenceSiteDAO
import com.nextchaptersoftware.db.models.ConfluenceSiteId
import com.nextchaptersoftware.db.models.ConfluenceSpaceDAO
import com.nextchaptersoftware.db.models.ConfluenceSpaceId
import com.nextchaptersoftware.db.models.ConfluenceSpaceIngestionType
import com.nextchaptersoftware.db.models.DataSourcePresetDAO
import com.nextchaptersoftware.db.models.DataSourcePresetId
import com.nextchaptersoftware.db.models.DocumentType
import com.nextchaptersoftware.db.models.DsacMode
import com.nextchaptersoftware.db.models.EmailEventDAO
import com.nextchaptersoftware.db.models.EmailEventId
import com.nextchaptersoftware.db.models.EmailEventType
import com.nextchaptersoftware.db.models.EmbeddingDeleteDAO
import com.nextchaptersoftware.db.models.EmbeddingDeleteId
import com.nextchaptersoftware.db.models.EmbeddingDeleteStatus
import com.nextchaptersoftware.db.models.EmbeddingModel
import com.nextchaptersoftware.db.models.EnterpriseAppConfigDAO
import com.nextchaptersoftware.db.models.EvalDAO
import com.nextchaptersoftware.db.models.EvalId
import com.nextchaptersoftware.db.models.ExecutionState
import com.nextchaptersoftware.db.models.FeedbackType
import com.nextchaptersoftware.db.models.GitHubAppRequestDAO
import com.nextchaptersoftware.db.models.GitHubAppRequestId
import com.nextchaptersoftware.db.models.GoogleDriveFileDAO
import com.nextchaptersoftware.db.models.GoogleDriveFileId
import com.nextchaptersoftware.db.models.GoogleDriveFileType
import com.nextchaptersoftware.db.models.GroupDAO
import com.nextchaptersoftware.db.models.GroupId
import com.nextchaptersoftware.db.models.GroupMembershipDAO
import com.nextchaptersoftware.db.models.GroupMembershipId
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.IngestionDAO
import com.nextchaptersoftware.db.models.IngestionId
import com.nextchaptersoftware.db.models.InsightType
import com.nextchaptersoftware.db.models.InstallationBotDAO
import com.nextchaptersoftware.db.models.InstallationBotId
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.IsQuestionAnswerRegressionDAO
import com.nextchaptersoftware.db.models.IsQuestionRegressionDAO
import com.nextchaptersoftware.db.models.IssueStatus
import com.nextchaptersoftware.db.models.JiraBoardDAO
import com.nextchaptersoftware.db.models.JiraBoardId
import com.nextchaptersoftware.db.models.JiraGroupDAO
import com.nextchaptersoftware.db.models.JiraGroupId
import com.nextchaptersoftware.db.models.JiraPermissionSchemeDAO
import com.nextchaptersoftware.db.models.JiraProjectDAO
import com.nextchaptersoftware.db.models.JiraProjectId
import com.nextchaptersoftware.db.models.JiraProjectIngestionType
import com.nextchaptersoftware.db.models.JiraSiteDAO
import com.nextchaptersoftware.db.models.JiraSiteId
import com.nextchaptersoftware.db.models.LinearOrganizationDAO
import com.nextchaptersoftware.db.models.LinearOrganizationId
import com.nextchaptersoftware.db.models.LinearTeamDAO
import com.nextchaptersoftware.db.models.LinearTeamId
import com.nextchaptersoftware.db.models.LinearTeamIngestionType
import com.nextchaptersoftware.db.models.MLDocumentRelevancyEvaluationType
import com.nextchaptersoftware.db.models.MLInferenceCategory
import com.nextchaptersoftware.db.models.MLInferenceDAO
import com.nextchaptersoftware.db.models.MLInferenceEngine
import com.nextchaptersoftware.db.models.MLInferenceId
import com.nextchaptersoftware.db.models.MLInferenceRuntimeConfigurationDAO
import com.nextchaptersoftware.db.models.MLInferenceRuntimeConfigurationType
import com.nextchaptersoftware.db.models.MLInferenceTemplateDAO
import com.nextchaptersoftware.db.models.MLInferenceTemplateId
import com.nextchaptersoftware.db.models.MLInferenceTemplateKind
import com.nextchaptersoftware.db.models.MLInferenceType
import com.nextchaptersoftware.db.models.MLReferenceResolverType
import com.nextchaptersoftware.db.models.MLRerankModel
import com.nextchaptersoftware.db.models.MLSettingsDAO
import com.nextchaptersoftware.db.models.MLSettingsId
import com.nextchaptersoftware.db.models.MLTypedDocument
import com.nextchaptersoftware.db.models.McpInferenceDAO
import com.nextchaptersoftware.db.models.McpInferenceId
import com.nextchaptersoftware.db.models.McpInferenceState
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.MessageDAO
import com.nextchaptersoftware.db.models.MessageFeedbackDAO
import com.nextchaptersoftware.db.models.MessageFeedbackId
import com.nextchaptersoftware.db.models.MessageId
import com.nextchaptersoftware.db.models.MessageMentionDAO
import com.nextchaptersoftware.db.models.MessageMentionId
import com.nextchaptersoftware.db.models.MicrosoftTeamsChannelDAO
import com.nextchaptersoftware.db.models.MicrosoftTeamsChannelId
import com.nextchaptersoftware.db.models.MicrosoftTeamsTeamDAO
import com.nextchaptersoftware.db.models.MicrosoftTeamsTeamId
import com.nextchaptersoftware.db.models.NULL_ATTEMPT
import com.nextchaptersoftware.db.models.OrgApiKeyDAO
import com.nextchaptersoftware.db.models.OrgApiKeyId
import com.nextchaptersoftware.db.models.OrgBillingDAO
import com.nextchaptersoftware.db.models.OrgBillingEmailDAO
import com.nextchaptersoftware.db.models.OrgBillingEmailState
import com.nextchaptersoftware.db.models.OrgBillingEmailType
import com.nextchaptersoftware.db.models.OrgBillingSeatDAO
import com.nextchaptersoftware.db.models.OrgBillingSeatState
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberDAO
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.OrgMemberMigrateDAO
import com.nextchaptersoftware.db.models.OrgMemberMigrateId
import com.nextchaptersoftware.db.models.OrgMemberMigrateStatus
import com.nextchaptersoftware.db.models.OrgProxyDAO
import com.nextchaptersoftware.db.models.OrgProxyId
import com.nextchaptersoftware.db.models.OrgSettingsDAO
import com.nextchaptersoftware.db.models.OrgSettingsId
import com.nextchaptersoftware.db.models.PersonDAO
import com.nextchaptersoftware.db.models.PersonEmailPreferencesDAO
import com.nextchaptersoftware.db.models.PersonEmailPreferencesId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.PersonOnboardingState
import com.nextchaptersoftware.db.models.PersonPreferencesDAO
import com.nextchaptersoftware.db.models.PersonPreferencesId
import com.nextchaptersoftware.db.models.PlanCapabilityDAO
import com.nextchaptersoftware.db.models.PlanCapabilityType
import com.nextchaptersoftware.db.models.PlanDAO
import com.nextchaptersoftware.db.models.PlanRate
import com.nextchaptersoftware.db.models.PlanTier
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.ProductFeedbackResponseDAO
import com.nextchaptersoftware.db.models.ProductFeedbackResponseId
import com.nextchaptersoftware.db.models.ProductFeedbackResponseInstallations
import com.nextchaptersoftware.db.models.ProductFeedbackResponseType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ProviderAuthenticationStateDAO
import com.nextchaptersoftware.db.models.ProviderAuthenticationStateId
import com.nextchaptersoftware.db.models.ProviderRole
import com.nextchaptersoftware.db.models.PullRequestCommentDAO
import com.nextchaptersoftware.db.models.PullRequestCommentId
import com.nextchaptersoftware.db.models.PullRequestDAO
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.PullRequestIngestionDAO
import com.nextchaptersoftware.db.models.PullRequestIngestionId
import com.nextchaptersoftware.db.models.PullRequestReviewDAO
import com.nextchaptersoftware.db.models.PullRequestReviewId
import com.nextchaptersoftware.db.models.PullRequestReviewState
import com.nextchaptersoftware.db.models.PullRequestState
import com.nextchaptersoftware.db.models.ReferenceArtifact
import com.nextchaptersoftware.db.models.RegisteredDomainDAO
import com.nextchaptersoftware.db.models.RegisteredDomainId
import com.nextchaptersoftware.db.models.RegressionTestDAO
import com.nextchaptersoftware.db.models.RegressionTestId
import com.nextchaptersoftware.db.models.ReleaseChannel
import com.nextchaptersoftware.db.models.ReleasedVersionDAO
import com.nextchaptersoftware.db.models.ReleasedVersionId
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.SamlIdentityProvider
import com.nextchaptersoftware.db.models.SamlIdpMetadataDAO
import com.nextchaptersoftware.db.models.SamlIdpMetadataId
import com.nextchaptersoftware.db.models.SampleQuestionDAO
import com.nextchaptersoftware.db.models.SampleQuestionId
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.models.ScmTeamSettingsDAO
import com.nextchaptersoftware.db.models.ScmTeamSettingsId
import com.nextchaptersoftware.db.models.SessionActionDAO
import com.nextchaptersoftware.db.models.SessionActionId
import com.nextchaptersoftware.db.models.SessionReferralDAO
import com.nextchaptersoftware.db.models.SessionReferralId
import com.nextchaptersoftware.db.models.SlackAutoAnswerDAO
import com.nextchaptersoftware.db.models.SlackAutoAnswerFilterStage
import com.nextchaptersoftware.db.models.SlackAutoAnswerMode
import com.nextchaptersoftware.db.models.SlackChannelDAO
import com.nextchaptersoftware.db.models.SlackChannelId
import com.nextchaptersoftware.db.models.SlackChannelIngestionDAO
import com.nextchaptersoftware.db.models.SlackChannelIngestionId
import com.nextchaptersoftware.db.models.SlackChannelIngestionStatus
import com.nextchaptersoftware.db.models.SlackChannelMemberDAO
import com.nextchaptersoftware.db.models.SlackChannelMemberId
import com.nextchaptersoftware.db.models.SlackChannelPatternPreferencesDAO
import com.nextchaptersoftware.db.models.SlackChannelPatternPreferencesId
import com.nextchaptersoftware.db.models.SlackChannelPreferencesDAO
import com.nextchaptersoftware.db.models.SlackChannelPreferencesId
import com.nextchaptersoftware.db.models.SlackChannelsIngestionDAO
import com.nextchaptersoftware.db.models.SlackChannelsIngestionId
import com.nextchaptersoftware.db.models.SlackChannelsIngestionStatus
import com.nextchaptersoftware.db.models.SlackIngestionDAO
import com.nextchaptersoftware.db.models.SlackIngestionId
import com.nextchaptersoftware.db.models.SlackSettingsDAO
import com.nextchaptersoftware.db.models.SlackSettingsId
import com.nextchaptersoftware.db.models.SlackTeamDAO
import com.nextchaptersoftware.db.models.SlackTeamId
import com.nextchaptersoftware.db.models.SlackThreadDAO
import com.nextchaptersoftware.db.models.SlackThreadId
import com.nextchaptersoftware.db.models.SlackUserConnectPromptHistoryDAO
import com.nextchaptersoftware.db.models.SlackUserConnectPromptHistoryId
import com.nextchaptersoftware.db.models.SlackUserConnectTrigger
import com.nextchaptersoftware.db.models.SocialNetworkDAO
import com.nextchaptersoftware.db.models.SocialNetworkId
import com.nextchaptersoftware.db.models.SourceMarkDAO
import com.nextchaptersoftware.db.models.SourceMarkId
import com.nextchaptersoftware.db.models.SourcePointDAO
import com.nextchaptersoftware.db.models.SourcePointId
import com.nextchaptersoftware.db.models.TeamInviteeDAO
import com.nextchaptersoftware.db.models.TeamInviteeEventType
import com.nextchaptersoftware.db.models.TeamInviteeId
import com.nextchaptersoftware.db.models.ThreadDAO
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.db.models.ThreadParticipantDAO
import com.nextchaptersoftware.db.models.ThreadParticipantId
import com.nextchaptersoftware.db.models.ThreadPullRequestDAO
import com.nextchaptersoftware.db.models.ThreadPullRequestId
import com.nextchaptersoftware.db.models.ThreadUnreadDAO
import com.nextchaptersoftware.db.models.ThreadUnreadId
import com.nextchaptersoftware.db.models.TopicDAO
import com.nextchaptersoftware.db.models.TopicExpertDAO
import com.nextchaptersoftware.db.models.TopicExpertId
import com.nextchaptersoftware.db.models.TopicId
import com.nextchaptersoftware.db.models.TopicInsightDAO
import com.nextchaptersoftware.db.models.TopicInsightId
import com.nextchaptersoftware.db.models.TopicMappingSourceType
import com.nextchaptersoftware.db.models.TopicSourceType
import com.nextchaptersoftware.db.models.UnblockedRole
import com.nextchaptersoftware.db.models.UserEngagementDAO
import com.nextchaptersoftware.db.models.UserEngagementMetricsDAO
import com.nextchaptersoftware.db.models.VersionInfoDAO
import com.nextchaptersoftware.db.models.VersionInfoId
import com.nextchaptersoftware.db.models.WebIngestionSiteDAO
import com.nextchaptersoftware.db.models.WebIngestionSiteId
import com.nextchaptersoftware.db.models.types.RepoSelectionMode
import com.nextchaptersoftware.db.stores.Cohort
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.markdown.MarkdownConverter.asMessageBody
import com.nextchaptersoftware.models.SourceSnippet
import com.nextchaptersoftware.models.clientconfig.ClientCapabilityType
import com.nextchaptersoftware.models.clientconfig.ClientQuantityType
import com.nextchaptersoftware.security.Hashing.asSha256Hash
import com.nextchaptersoftware.serialization.Serialization.encode
import com.nextchaptersoftware.types.EmailAddress
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.types.Hostname
import com.nextchaptersoftware.utils.Base64.base64DecodeAsByteArray
import com.nextchaptersoftware.utils.Base64.base64Encode
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import com.nextchaptersoftware.utils.KotlinUtils.doNothing
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.Url
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.absoluteValue
import kotlin.random.Random
import kotlin.time.Duration
import kotlin.time.Instant
import org.jetbrains.exposed.dao.Entity
import org.jetbrains.exposed.sql.Transaction

/**
 * Utility for easily building random unique models suitable for tests.
 */
@Suppress("LargeClass", "LongParameterList", "CyclomaticComplexMethod")
object ModelBuilders {

    // prints the created objects along their stack tree calls
    internal val DEBUG = System.getenv("DEBUG_MODEL_BUILDERS") == "1"

    // prints the created objects as Graphviz's directed graph
    internal val GRAPH = System.getenv("GRAPH_MODEL_BUILDERS") == "1"

    // exclude nodes from the generated output
    internal val IGNORE = System.getenv("GRAPH_MODEL_IGNORE")?.split(",") ?: emptyList<String>()

    private var debugDepth = 0

    private fun debug(label: String?, type: String) {
        if (IGNORE.contains(type)) {
            return // ignored
        }
        if (DEBUG) {
            val init = if (GRAPH) "#" else ""
            val tabs = "  ".repeat(debugDepth + 1)
            print("$init$tabs")
            println(
                when (label) {
                    null -> "make$type"
                    else -> "$label: $type"
                },
            )
            debugDepth += 1
        }
    }

    private fun debugEnd(
        label: String?,
        type: String,
        instance: Entity<*>,
        relations: Map<String, Any?> = emptyMap(),
    ) {
        if (IGNORE.contains(type)) {
            return // ignored
        }
        if (DEBUG) {
            debugDepth -= 1
            val related = instance.references()
                .plus(relations.filter { it.value != null })
                .ifEmpty { null }
                ?.map { "${it.key}=${it.value}" }?.joinToString(", ", prefix = ", ") ?: ""
            val init = if (GRAPH) "#" else ""
            val tabs = "  ".repeat(debugDepth + 1)
            print("$init$tabs")
            println("$type(id=${instance.id}$related)")
        }
        if (GRAPH) {
            graph(label, type, instance, relations)
        }
        if (DEBUG && debugDepth == 0) {
            println()
        }
    }

    private fun Entity<*>.references(): Map<String, Any?> {
        return klass.table.columns
            .filter { it.referee != null }
            .associate { column ->
                val field = column.name
                val value = writeValues.get(column)
                field to value
            }
            .filterKeys { IGNORE.contains(it).not() }
            .filterValues { it != null }
    }

    private fun graph(
        label: String?,
        type: String,
        instance: Entity<*>,
        relations: Map<String, Any?> = emptyMap(),
    ) {
        val node = instance.id
        val refs = instance.references()
            .plus(relations.filter { it.value != null })
            .ifEmpty { null }
        val tabs = "  ".repeat(debugDepth + 2)

        """ |$tabs"$node" [ color=${graphNodeColor(type)}, label="$type: $node" ]
        """.trimMargin().let(::println)
        refs?.let {
            """
                |$tabs"$node" -> { ${refs.map { "\"${it.value}\"" }.joinToString(", ")} }
            """.trimMargin().let(::println)
        }
        label?.let {
            """
                |$tabs"$label" [ shape="box"] ; "$label" -> "$node"
            """.trimMargin().let(::println)
        }
    }

    private val graphColors = ConcurrentHashMap<String, Int>()

    private fun graphNodeColor(node: String): Int {
        return graphColors.computeIfAbsent(node) { (graphColors.keys.size % 12) + 1 }
    }

    /**
     * Checks that either all the objects are null, or they all share a single unique id
     */
    private fun <DAO> checkSame(
        vararg objects: DAO?,
        lazyMessage: () -> Any = { "Check failed" },
    ) where DAO : EntityExtensions<*, *> {
        val ids = objects
            .asSequence()
            .filterNotNull()
            .map { it.idValue }
            .toSet()
        check(
            value = ids.size <= 1,
            lazyMessage = lazyMessage,
        )
    }

    /**
     * Checks that either all the [values] are null, or they all are equal
     */
    private fun checkSame(
        vararg values: Any?,
        lazyMessage: () -> Any = { "Check failed" },
    ) {
        val set = values
            .asSequence()
            .filterNotNull()
            .toSet()
        check(
            value = set.size <= 1,
            lazyMessage = lazyMessage,
        )
    }

    /**
     * Checks that no [instance] is given when [predicate] is `false`
     */
    private fun <DAO> checkCreate(
        instance: DAO?,
        predicate: Boolean?,
        lazyMessage: () -> Any = { "Check failed" },
    ) {
        if (predicate == false) {
            check(instance == null, lazyMessage)
        }
    }

    /**
     * Calls [factory] to create the object when [predicate] is `true`
     */
    private suspend fun <T> createIf(
        predicate: Boolean?,
        factory: suspend () -> T,
    ): T? = when (predicate) {
        true -> factory()
        else -> null
    }

    suspend fun makeOrgMember(
        trx: Transaction? = null,
        _label: String? = null,
        id: OrgMemberId = OrgMemberId.random(),
        org: OrgDAO? = null,
        person: PersonDAO? = null,
        unblockedRole: UnblockedRole? = null,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
        createPerson: Boolean = true,
        questionsAskedPublic: Int? = null,
        questionsAskedPrivate: Int? = null,
        lastQuestionAskedAt: Instant? = null,
        answerConcisenessPreference: AnswerConcisenessPreference? = null,
        answerTonePreference: AnswerTonePreference? = null,
        answerDepthPreference: AnswerDepthPreference? = null,
        enableCiTriage: Boolean? = null,
        enableCodeReview: Boolean? = null,
        productFeedbackDeferredUntil: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "OrgMember")
        checkCreate(person, createPerson) { "Unexpected person given when createPerson=false" }

        val orgInstance = org ?: makeOrg(trx = this)
        val personInstance = person ?: createIf(createPerson) { makePerson(trx = this) }

        OrgMemberDAO.new(id) {
            this.org = orgInstance
            this.unblockedRole = unblockedRole
            personInstance?.also { this.person = it }
            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
            questionsAskedPublic?.also { this.questionsAskedPublic = it }
            questionsAskedPrivate?.also { this.questionsAskedPrivate = it }
            lastQuestionAskedAt?.also { this.lastQuestionAskedAt = it }
            answerConcisenessPreference?.also { this.answerConcisenessPreference = it }
            answerTonePreference?.also { this.answerTonePreference = it }
            answerDepthPreference?.also { this.answerDepthPreference = it }
            enableCiTriage?.also { this.enableCiTriage = it }
            enableCodeReview?.also { this.enableCodeReview = it }
            productFeedbackDeferredUntil?.also { this.productFeedbackDeferredUntil = it }
        }.also {
            debugEnd(_label, "OrgMember", it)
        }
    }

    suspend fun makeOrgApiKey(
        trx: Transaction? = null,
        _label: String? = null,
        id: OrgApiKeyId = OrgApiKeyId.random(),
        org: OrgDAO? = null,
        orgMemberCreator: OrgMemberDAO? = null,
        audience: ApiKeyAudience? = null,
        lastUsed: Instant? = null,
        name: String? = null,
        hash: String? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "OrgApiKey")

        val orgInstance = org ?: makeOrg(trx = this)
        val orgMemberInstance = orgMemberCreator ?: makeOrgMember(trx = this, org = orgInstance)

        OrgApiKeyDAO.new(id) {
            this.org = orgInstance
            this.orgMemberCreator = orgMemberInstance
            this.audience = audience ?: ApiKeyAudience.PublicApi
            this.name = name ?: ("Key " + UUID.randomUUID().toString())
            this.hash = hash ?: UUID.randomUUID().toString().asSha256Hash()
            lastUsed?.also { this.lastUsed = it }
        }.also {
            debugEnd(_label, "OrgApiKey", it)
        }
    }

    suspend fun makePerson(
        trx: Transaction? = null,
        _label: String? = null,
        id: PersonId = PersonId.random(),
        customDisplayName: String? = null,
        customAvatarUrl: String? = null,
        primaryEmail: EmailAddress? = null,
        hasSeenTutorialVSCode: Boolean? = null,
        hasSeenTopFileVSCode: Boolean? = null,
        hasCreatedNoteVSCode: Boolean? = null,
        hasDismissedToastVSCode: Boolean? = null,
        hasSeenTutorialIntellij: Boolean? = null,
        hasSeenTopFileIntellij: Boolean? = null,
        hasCreatedNoteIntellij: Boolean? = null,
        hasCreatedWalkthroughIntellij: Boolean? = null,
        hasDismissedToastIntellij: Boolean? = null,
        hasSeenTutorialHub: Boolean? = null,
        hasInstalledHub: Boolean? = null,
        hasInstalledDesktop: Boolean? = null,
        hasSeenDocs: Boolean? = null,
        sentOnboardingEmailCampaign: Boolean? = null,
        createdAt: Instant? = null,
        subscribedReleaseChannel: ReleaseChannel? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "Person")
        PersonDAO.new(id) {
            this.customDisplayName = customDisplayName ?: "First Last $id"
            this.customAvatarUrl = customAvatarUrl ?: "https://avatar.com/$id"
            this.primaryEmail = (primaryEmail ?: EmailAddress.of("$<EMAIL>")).value
            var states = 0u
            hasSeenTutorialVSCode?.also { states = PersonOnboardingState.VSCodeHasSeenTutorial.set(states, it) }
            hasSeenTopFileVSCode?.also { states = PersonOnboardingState.VSCodeHasSeenTopFile.set(states, it) }
            hasCreatedNoteVSCode?.also { states = PersonOnboardingState.VSCodeHasCreatedNote.set(states, it) }
            hasDismissedToastVSCode?.also { states = PersonOnboardingState.VSCodeHasDismissedToast.set(states, it) }
            hasSeenTutorialIntellij?.also { states = PersonOnboardingState.IntellijHasSeenTutorial.set(states, it) }
            hasSeenTopFileIntellij?.also { states = PersonOnboardingState.IntellijHasSeenTopFile.set(states, it) }
            hasCreatedNoteIntellij?.also { states = PersonOnboardingState.IntellijHasCreatedNote.set(states, it) }
            hasCreatedWalkthroughIntellij?.also { states = PersonOnboardingState.IntellijHasCreatedWalkthrough.set(states, it) }
            hasDismissedToastIntellij?.also { states = PersonOnboardingState.IntellijHasDismissedToast.set(states, it) }
            hasSeenTutorialHub?.also { states = PersonOnboardingState.HubHasSeenTutorial.set(states, it) }
            hasInstalledHub?.also { states = PersonOnboardingState.HubIsInstalled.set(states, it) }
            hasInstalledDesktop?.also { states = PersonOnboardingState.DesktopIsInstalled.set(states, it) }
            hasSeenDocs?.also { states = PersonOnboardingState.HasSeenDocs.set(states, it) }
            sentOnboardingEmailCampaign?.also { states = PersonOnboardingState.SentOnboardingEmailCampaign.set(states, it) }
            this.onboardingStates = states
            createdAt?.also { this.createdAt = it }
            subscribedReleaseChannel?.also { this.subscribedReleaseChannel = it }
        }.also {
            debugEnd(_label, "Person", it)
        }
    }

    suspend fun makePersonPreferences(
        trx: Transaction? = null,
        _label: String? = null,
        id: PersonPreferencesId = PersonPreferencesId.random(),
        person: PersonDAO? = null,
        incognitoMode: Boolean? = null,
        hideIncognitoBanner: Boolean? = null,
    ): PersonPreferencesDAO = suspendedTransaction(trx = trx) {
        debug(_label, "PersonPreferences")

        val thePerson = person ?: makePerson(trx = this)

        PersonPreferencesDAO.new(id) {
            this.person = thePerson
            incognitoMode?.also { this.incognitoMode = it }
            hideIncognitoBanner?.also { this.hideIncognitoBanner = it }
        }.also {
            debugEnd(_label, "PersonPreferences", it)
        }
    }

    suspend fun makeIdentity(
        trx: Transaction? = null,
        _label: String? = null,
        id: IdentityId = IdentityId.random(),
        provider: Provider? = null,
        externalId: String? = null,
        externalTeamId: String? = null,
        username: String? = null,
        displayName: String? = "Display $id Name",
        avatarUrl: String? = null,
        htmlUrl: String? = null,
        primaryEmail: EmailAddress? = null,
        emails: List<EmailAddress>? = null,
        person: PersonDAO? = null,
        rawAccessToken: ByteArray? = null,
        rawRefreshToken: ByteArray? = null,
        accessTokenExpiresAt: Instant? = null,
        refreshTokenExpiresAt: Instant? = null,
        accessTokenScope: String? = null,
        isBot: Boolean = false,
        promptToReconnect: Boolean? = null,
        modifiedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "Identity")
        IdentityDAO.new(id) {
            modifiedAt?.also { this.modifiedAt = it }
            this.provider = provider ?: Provider.GitHub
            this.externalId = externalId ?: "123$id"
            this.username = username ?: "user_${id}_name"
            this.displayName = displayName
            this.avatarUrl = avatarUrl ?: "https://avatars.githubusercontent.com/u/123$id?v=4"
            this.htmlUrl = htmlUrl ?: "https://github.com/user_${id}_name"
            this.primaryEmail = primaryEmail?.value ?: person?.primaryEmail
            this.person = person
            this.emails = (emails ?: listOfNotNull(primaryEmail)).encode()
            this.rawAccessToken = rawAccessToken
            this.rawRefreshToken = rawRefreshToken
            this.accessTokenExpiresAt = accessTokenExpiresAt
            this.refreshTokenExpiresAt = refreshTokenExpiresAt
            this.accessTokenScope = accessTokenScope
            this.externalTeamId = externalTeamId ?: (provider ?: Provider.GitHub).uniqueSignature
            this.isBot = isBot
            promptToReconnect?.also { this.promptToReconnect = it }
        }.also {
            debugEnd(_label, "Identity", it)
        }
    }

    suspend fun makeCIToken(
        trx: Transaction? = null,
        _label: String? = null,
        id: CITokenId = CITokenId.random(),
        ciInstallation: InstallationDAO? = null,
        createdBy: OrgMemberDAO? = null,
        encryptedToken: ByteArray? = null,
        orgs: List<String>? = null,
        revoked: Boolean? = null,
        scopes: List<String>? = null,
        webUrl: Url? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "CIToken")
        val encryptedTokenInstance = encryptedToken ?: "token".base64Encode().base64DecodeAsByteArray()
        val ciInstallationInstance = ciInstallation ?: makeInstallation(trx = this)
        val createdByInstance = createdBy ?: makeOrgMember(trx = this)
        CITokenDAO.new(id) {
            this.ciInstallation = ciInstallationInstance
            this.createdBy = createdByInstance
            this.encryptedToken = encryptedTokenInstance
            this.orgs = orgs ?: listOf("Org 1", "Org 2")
            this.revoked = revoked ?: false
            this.scopes = scopes ?: listOf("scope1", "scope2")
            this.webUrl = webUrl?.asString ?: "https://buildkite.com/token/$id"
        }.also {
            debugEnd(_label, "CIToken", it)
        }
    }

    suspend fun makeCIScm(
        trx: Transaction? = null,
        _label: String? = null,
        id: CIScmId = CIScmId.random(),
        ciInstallation: InstallationDAO? = null,
        scmInstallation: InstallationDAO? = null,
        mode: RepoSelectionMode? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "CIScm")
        val ciInstallationInstance = ciInstallation ?: makeInstallation(trx = this)
        val scmInstallationInstance = scmInstallation ?: makeInstallation(trx = this)
        CIScmDAO.new(id) {
            this.ciInstallation = ciInstallationInstance
            this.scmInstallation = scmInstallationInstance
            this.mode = mode ?: RepoSelectionMode.None
        }.also {
            debugEnd(_label, "CIScm", it)
        }
    }

    suspend fun makeCIProject(
        trx: Transaction? = null,
        _label: String? = null,
        id: CIProjectId = CIProjectId.random(),
        ciInstallation: InstallationDAO? = null,
        selected: Boolean? = null,
        organizationExternalId: UUID? = null,
        organizationSlug: String? = null,
        organizationName: String? = null,
        organizationUrl: Url? = null,
        projectExternalId: UUID? = null,
        projectSlug: String? = null,
        projectName: String? = null,
        projectUrl: Url? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "CIProject")
        val ciInstallationInstance = ciInstallation ?: makeInstallation(trx = this)
        CIProjectDAO.new(id) {
            this.ciInstallation = ciInstallationInstance
            this.selected = selected ?: false
            this.organizationExternalId = organizationExternalId ?: UUID.randomUUID()
            this.organizationSlug = organizationSlug ?: "org-slug-$id"
            this.organizationName = organizationName ?: "organizationName"
            this.organizationUrl = organizationUrl?.asString ?: "https://example.com/organization/$id"
            this.projectExternalId = projectExternalId ?: UUID.randomUUID()
            this.projectSlug = projectSlug ?: "project-slug-$id"
            this.projectName = projectName ?: "projectName"
            this.projectUrl = projectUrl?.asString ?: "https://example.com/project/$id"
        }.also {
            debugEnd(_label, "CIProject", it)
        }
    }

    suspend fun makeOrg(
        trx: Transaction? = null,
        _label: String? = null,
        id: OrgId = OrgId.random(),
        displayName: String? = null,
        customDisplayName: String? = null,
        avatarUrl: String? = null,
        htmlUrl: String? = null,
        createdAt: Instant? = null,
        createdBy: IdentityDAO? = null,
        subscribedReleaseChannel: ReleaseChannel = ReleaseChannel.Stable,
        needsReprocessing: Boolean? = null,
        enabledAt: Instant? = null,
        isDeleted: Boolean? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "Org")
        OrgDAO.new(id) {
            this.displayName = displayName ?: "Org_$id"
            this.avatarUrl = avatarUrl ?: "https://avatars.githubusercontent.com/"
            this.htmlUrl = htmlUrl ?: "https://github.com/org_${id}_name"
            this.subscribedReleaseChannel = subscribedReleaseChannel
            createdBy?.also { this.createdBy = it }
            createdAt?.also { this.createdAt = it }
            enabledAt?.also { this.enabledAt = it }
            isDeleted?.also { this.isDeleted = it }
            needsReprocessing?.also { this.needsReprocessing = it }
            customDisplayName?.also { this.customDisplayName = it }
        }.also {
            debugEnd(_label, "Org", it)
        }
    }

    suspend fun makeOrgProxy(
        trx: Transaction? = null,
        _label: String? = null,
        id: OrgProxyId = OrgProxyId.random(),
        org: OrgDAO? = null,
        proxyUrl: Url = Url("http://localhost:8888"), // sensible default for tests
        isEnabled: Boolean = true,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "OrgProxy")

        val theOrg = org ?: makeOrg(trx = this)

        OrgProxyDAO.new(id) {
            this.org = theOrg
            this.proxyUrl = proxyUrl.asString
            this.isEnabled = isEnabled
            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
        }.also {
            debugEnd(_label, "OrgProxy", it)
        }
    }

    suspend fun makeGroup(
        trx: Transaction? = null,
        _label: String? = null,
        id: GroupId = GroupId.random(),
        org: OrgDAO? = null,
        saml: SamlIdpMetadataDAO? = null,
        displayName: String? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "Group")
        val thisOrg = org ?: makeOrg(trx = this)
        val thisSaml = saml ?: makeSamlIdpMetadata(trx = this, org = org)

        GroupDAO.new(id) {
            this.org = thisOrg
            this.saml = thisSaml
            this.displayName = displayName ?: "Group_$id"
        }.also {
            debugEnd(_label, "Group", it)
        }
    }

    suspend fun makeGroupMembership(
        trx: Transaction? = null,
        _label: String? = null,
        id: GroupMembershipId = GroupMembershipId.random(),
        group: GroupDAO? = null,
        identity: IdentityDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "GroupMembership")
        val thisGroup = group ?: makeGroup(trx = this)
        val thisIdentity = identity ?: makeIdentity(trx = this)

        GroupMembershipDAO.new(id) {
            this.group = thisGroup
            this.identity = thisIdentity
        }.also {
            debugEnd(_label, "GroupMembership", it)
        }
    }

    suspend fun makeOrgBilling(
        trx: Transaction? = null,
        _label: String? = null,
        org: OrgDAO? = null,
        stripeCustomerId: String = "cus_${UUID.randomUUID()}",
        plan: PlanDAO? = null,
        rate: PlanRate? = null,
        seats: Int? = null,
        nextPlan: PlanDAO? = null,
        nextPlanRate: PlanRate? = null,
        nextSeats: Int? = null,
        maxSeats: Int? = null,
        currentBillingCycleStart: Instant? = null,
        currentBillingCycleEnd: Instant? = null,
        trialEnd: Instant = Instant.nowWithMicrosecondPrecision(),
    ) = suspendedTransaction(trx) {
        debug(_label, "OrgBilling")

        val thisOrg = org ?: makeOrg(trx = this)
        val thisPlan = plan ?: makePlan(trx = this)

        OrgBillingDAO.new {
            this.org = thisOrg
            this.stripeCustomerId = stripeCustomerId
            this.plan = thisPlan
            this.rate = rate
            this.seats = seats
            this.trialEnd = trialEnd
            this.nextPlan = nextPlan
            this.nextPlanRate = nextPlanRate
            this.nextSeats = nextSeats
            this.maxSeats = maxSeats
            this.currentBillingPeriodStart = currentBillingCycleStart
            this.currentBillingPeriodEnd = currentBillingCycleEnd
        }.also {
            debugEnd(_label, "OrgBilling", it)
        }
    }

    suspend fun makeOrgBillingSeat(
        trx: Transaction? = null,
        _label: String? = null,
        orgBilling: OrgBillingDAO? = null,
        orgMember: OrgMemberDAO? = null,
        state: OrgBillingSeatState = OrgBillingSeatState.Assigned,
    ) = suspendedTransaction(trx) {
        debug(_label, "OrgBilling")

        val thisOrgBilling = orgBilling ?: makeOrgBilling(trx = this)
        val thisOrgMember = orgMember ?: makeOrgMember(trx = this, org = thisOrgBilling.org)

        OrgBillingSeatDAO.new {
            this.orgBilling = thisOrgBilling
            this.orgMember = thisOrgMember
            this.state = state
        }.also {
            debugEnd(_label, "makeOrgBillingSeat", it)
        }
    }

    suspend fun makeOrgBillingEmail(
        trx: Transaction? = null,
        _label: String? = null,
        orgBilling: OrgBillingDAO? = null,
        type: OrgBillingEmailType = OrgBillingEmailType.TrialPitch,
        send: Instant = Instant.nowWithMicrosecondPrecision(),
        expire: Instant = Instant.nowWithMicrosecondPrecision(),
        state: OrgBillingEmailState = OrgBillingEmailState.Pending,
    ) = suspendedTransaction(trx) {
        debug(_label, "OrgBillingEmail")

        val thisOrgBilling = orgBilling ?: makeOrgBilling(trx = this)

        OrgBillingEmailDAO.new {
            this.orgBilling = thisOrgBilling
            this.type = type
            this.send = send
            this.expire = expire
            this.state = state
        }.also {
            debugEnd(_label, "OrgBillingEmail", it)
        }
    }

    suspend fun makeOrgSettings(
        trx: Transaction? = null,
        _label: String? = null,
        id: OrgSettingsId = OrgSettingsId.random(),
        org: OrgDAO? = null,
        createdAt: Instant? = null,
        dataSourceAccessControlMode: DsacMode? = null,
        enableEmailAlignment: Boolean? = null,
        enablePresetBotResponse: Boolean? = null,
        enableProductFeedback: Boolean? = null,
        enableTeamStatsUserFeedback: Boolean? = null,
        roleBasedAccessControl: Boolean? = null,
        webIngestionSiteLimitOverride: Int? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "OrgSettings")

        val scmTeamOrg = org ?: makeOrg(trx)

        OrgSettingsDAO.new(id) {
            this.org = scmTeamOrg
            createdAt?.also { this.createdAt = it }
            dataSourceAccessControlMode?.also { this.dataSourceAccessControlMode = it }
            enableEmailAlignment?.also { this.enableEmailAlignment = it }
            enablePresetBotResponse?.also { this.enablePresetBotResponse = it }
            enableProductFeedback?.also { this.enableProductFeedback = it }
            enableTeamStatsUserFeedback?.also { this.enableTeamStatsUserFeedback = it }
            roleBasedAccessControl?.also { this.roleBasedAccessControl = it }
            webIngestionSiteLimitOverride?.also { this.webIngestionSiteLimitOverride = it }
        }.also {
            debugEnd(_label, "orgSettings", it)
        }
    }

    suspend fun makeSlackSettings(
        trx: Transaction? = null,
        _label: String? = null,
        id: SlackSettingsId = SlackSettingsId.random(),
        org: OrgDAO? = null,
        createdAt: Instant? = null,
        slackBotDisclaimer: Boolean? = null,
        slackBotSuggestedQuestions: Boolean? = null,
        slackQAValidationChannel: String? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "SlackSettings")

        val scmTeamOrg = org ?: makeOrg(trx)

        SlackSettingsDAO.new(id) {
            this.org = scmTeamOrg
            createdAt?.also { this.createdAt = it }
            slackBotDisclaimer?.also { this.slackBotDisclaimer = it }
            slackBotSuggestedQuestions?.also { this.slackBotSuggestedQuestions = it }
            slackQAValidationChannel?.also { this.slackQAValidationChannel = it }
        }.also {
            debugEnd(_label, "SlackSettings", it)
        }
    }

    suspend fun makeScmTeam(
        trx: Transaction? = null,
        _label: String? = null,
        id: ScmTeamId = ScmTeamId.random(),
        org: OrgDAO? = null,
        installation: InstallationDAO? = null,
        displayName: String? = null,
        provider: Provider? = null,
        providerEnterprise: EnterpriseAppConfigDAO? = null,
        providerExternalId: String? = null,
        providerLogin: String? = null,
        providerDisplayName: String? = null,
        providerAvatarUrl: String? = null,
        providerHtmlUrl: String? = null,
        providerIsPersonalAccount: Boolean? = null,
        providerExternalInstallationId: String? = null,
        providerInstallationValid: Boolean? = null,
        providerLastRefreshedAt: Instant? = null,
        createdAt: Instant? = null,
        createdBy: IdentityDAO? = null,
        deletedProviderExternalId: String? = null,
        subscribedReleaseChannel: ReleaseChannel = ReleaseChannel.Stable,
    ) = suspendedTransaction(trx) {
        debug(_label, "ScmTeam")
        checkSame(provider, installation?.provider) {
            "Multiple providers given"
        }

        val theProvider = provider ?: installation?.provider ?: Provider.GitHub
        val scmTeamOrg = org ?: installation?.org ?: makeOrg(
            trx = this,
            displayName = displayName,
            avatarUrl = providerAvatarUrl,
            htmlUrl = providerHtmlUrl,
            createdAt = createdAt,
            createdBy = createdBy,
            subscribedReleaseChannel = subscribedReleaseChannel,
        )
        val scmTeamInstallation = installation ?: makeInstallation(
            trx = this,
            org = scmTeamOrg,
            provider = theProvider,
            installationExternalId = providerExternalId ?: "abc$id",
        )
        ScmTeamDAO.new(id) {
            this.provider = theProvider
            this.providerExternalId = providerExternalId ?: "abc$id"
            this.providerEnterprise = providerEnterprise
            this.providerUniqueSignature = providerEnterprise?.id?.value?.toString() ?: ":${theProvider.dbOrdinal}:"
            this.providerLogin = providerLogin ?: "org_${id}_name"
            this.providerDisplayName = providerDisplayName ?: displayName ?: "Org $id Name"
            this.providerAvatarUrl = providerAvatarUrl ?: "https://avatars.githubusercontent.com/u/${this.providerExternalId}?v=4"
            this.providerHtmlUrl = providerHtmlUrl ?: "https://github.com/org_${id}_name"
            this.providerIsPersonalAccount = providerIsPersonalAccount ?: false
            this.providerExternalInstallationId = providerExternalInstallationId ?: "abc_install_$id"
            this.providerInstallationValid = providerInstallationValid ?: true
            providerLastRefreshedAt?.also { this.providerLastRefreshedAt = it }
            this.deletedProviderExternalId = deletedProviderExternalId
            this.org = scmTeamOrg
            this.installation = scmTeamInstallation
            createdBy?.also { this.createdBy = it }
            createdAt?.also { this.createdAt = it }
        }.also {
            debugEnd(
                _label, "ScmTeam", it,
                mapOf(
                    "provider" to theProvider.name,
                ),
            )
        }
    }

    suspend fun makeScmTeamSettings(
        id: ScmTeamSettingsId = ScmTeamSettingsId.random(),
        trx: Transaction? = null,
        _label: String? = null,
        scmTeam: ScmTeamDAO? = null,
        autoSelectNewSourceRepos: Boolean? = null,
        enableScmIssues: Boolean? = null,
        disableCommentSignatures: Boolean? = null,
        enablePullRequestIngestion: Boolean? = null,
        pinnedServiceIdentityId: IdentityId? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "ScmTeamSettings")

        val settingsTeam = scmTeam ?: makeScmTeam(trx = this)

        ScmTeamSettingsDAO.new(id) {
            this.scmTeam = settingsTeam
            autoSelectNewSourceRepos?.also { this.autoSelectNewSourceRepos = it }
            enableScmIssues?.also { this.enableScmIssues = it }
            enablePullRequestIngestion?.also { this.enablePullRequestSummaryGeneration = it }
            disableCommentSignatures?.also { this.disableCommentSignatures = it }
            pinnedServiceIdentityId?.also { this.pinnedServiceIdentity = it }
        }.also {
            debugEnd(_label, "ScmTeamSettings", it)
        }
    }

    suspend fun makeMember(
        trx: Transaction? = null,
        _label: String? = null,
        id: MemberId = MemberId.random(),
        scmTeam: ScmTeamDAO? = null,
        installation: InstallationDAO? = null,
        identity: IdentityDAO? = null,
        org: OrgDAO? = null,
        orgMember: OrgMemberDAO? = null,
        providerRole: ProviderRole? = null,
        unblockedRole: UnblockedRole? = null,
        isCurrentMember: Boolean? = null,
        isPrimaryMember: Boolean? = null,
        ignoreThreads: Boolean? = null,
        association: MemberId? = null,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "Member")
        checkSame(org, orgMember?.org, installation?.org, scmTeam?.org) {
            "Multiple org given"
        }
        checkSame(installation, scmTeam?.installation) {
            "Multiple installations given"
        }

        val memberOrg = org ?: orgMember?.org ?: installation?.org ?: scmTeam?.org ?: makeOrg(trx = this)
        val memberInstallation = installation ?: scmTeam?.installation ?: makeInstallation(trx = this, org = memberOrg)
        val memberIdentity = identity ?: makeIdentity(trx = this)
        val memberOrgMember = orgMember ?: makeOrgMember(trx = this, org = memberOrg, person = memberIdentity.person, unblockedRole = unblockedRole)

        MemberDAO.new(id) {
            this.installation = memberInstallation
            this.identity = memberIdentity
            this.providerRole = providerRole
            this.orgMember = memberOrgMember
            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
            isCurrentMember?.also { this.isCurrentMember = it }
            isPrimaryMember?.also { this.isPrimaryMember = it }
            ignoreThreads?.also { this.ignoreThreads = it }
            association?.also { this.associatedPrimaryMember = MemberDAO[association] }
        }.also {
            debugEnd(_label, "Member", it)
        }
    }

    suspend fun makeArchivedReference(
        trx: Transaction? = null,
        _label: String? = null,
        id: ArchivedReferenceId = ArchivedReferenceId.random(),
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
        org: OrgDAO,
        archivedBy: OrgMemberDAO,
        thread: ThreadDAO? = null,
        message: MessageDAO? = null,
        threadReference: ThreadDAO? = null,
        pullRequestReference: PullRequestDAO? = null,
        documentReferenceId: String? = null,
        reason: ArchivedReason? = null,
        comment: String? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "ArchivedReference")

        ArchivedReferenceDAO.new(id) {
            this.org = org
            this.archivedBy = archivedBy

            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
            thread?.also { this.thread = it }
            message?.also { this.message = it }
            threadReference?.also { this.threadReference = it }
            pullRequestReference?.also { this.pullRequestReference = it }
            documentReferenceId?.also { this.documentReferenceId = it }
            reason?.also { this.archivedReason = it }
            comment?.also { this.comment = it }
        }.also {
            debugEnd(_label, "ArchivedReference", it)
        }
    }

    suspend fun makeTeamInvitee(
        trx: Transaction? = null,
        _label: String? = null,
        id: TeamInviteeId = TeamInviteeId.random(),
        org: OrgDAO? = null,
        sender: OrgMemberDAO? = null,
        invitee: OrgMemberDAO? = null,
        eventType: TeamInviteeEventType? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "TeamInvitee")

        val thisOrg = org ?: makeOrg(trx = this)
        val thisSender = sender ?: makeOrgMember(trx = this, org = thisOrg)
        val thisInvitee = invitee ?: makeOrgMember(trx = this, org = thisOrg)
        val thisEventType = eventType ?: TeamInviteeEventType.Sent

        TeamInviteeDAO.new(id) {
            this.senderOrgMember = thisSender
            this.inviteeOrgMember = thisInvitee
            this.eventType = thisEventType
        }.also {
            debugEnd(_label, "TeamInvitee", it)
        }
    }

    suspend fun makeAuthenticationState(
        trx: Transaction? = null,
        _label: String? = null,
        id: AuthenticationStateId = AuthenticationStateId.random(),
        identity: IdentityDAO? = null,
        nonce: UUID = UUID.randomUUID(),
        secret: UUID = UUID.randomUUID(),
    ) = suspendedTransaction(trx) {
        debug(_label, "AuthenticationState")

        AuthenticationStateDAO.new(id) {
            this.identity = identity
            this.nonce = nonce
            this.secret = secret
        }.also {
            debugEnd(_label, "AuthenticationState", it)
        }
    }

    suspend fun makeBuild(
        trx: Transaction? = null,
        _label: String? = null,
        id: BuildId = BuildId.random(),
        ciInstallation: InstallationDAO? = null,
        pullRequest: PullRequestDAO? = null,
        externalId: String = randomId().toString(),
        displayName: String = randomStrings.nextAscii(5),
        displayNumber: Long? = null,
        groupKey: String = displayName,
        headSha: Hash = randomSha1(),
        apiUrl: Url? = "https:///api.example.org/builds/$externalId".asUrl,
        htmlUrl: Url? = "https://www.example.org/builds/$externalId".asUrl,
        type: String = "Build",
        runner: String = "bash",
        status: BuildStatus = BuildStatus.entries.random(),
        result: BuildResult = BuildResult.entries.random(),
        attempt: Long? = null,
        createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
        startedAt: Instant? = null,
        completedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "Build")

        val theCiInstallation = ciInstallation ?: makeInstallation(trx = this, provider = Provider.GitHubActions)
        val thePullRequest = pullRequest ?: makePullRequest(trx = this)

        BuildDAO.new(id) {
            this.ciInstallation = theCiInstallation
            this.projectContext = ""
            this.pullRequest = thePullRequest
            this.externalId = externalId
            this.headSha = headSha.asString()
            this.displayName = displayName
            this.displayNumber = displayNumber
            this.groupKey = groupKey
            this.apiUrl = apiUrl?.asString
            this.htmlUrl = htmlUrl?.asString
            this.type = type
            this.runner = runner
            this.status = status
            this.result = result
            this.attemptUnsafe = attempt ?: NULL_ATTEMPT
            this.createdAt = createdAt
            this.startedAt = startedAt
            this.completedAt = completedAt
        }.also {
            debugEnd(_label, "Build", it)
        }
    }

    suspend fun makeBuildJob(
        trx: Transaction? = null,
        _label: String? = null,
        id: BuildJobId = BuildJobId.random(),
        build: BuildDAO,
        externalId: String = randomId().toString(),
        externalParentId: String = randomId().toString(),
        displayName: String = "job $externalId",
        apiUrl: Url? = "https:///api.example.org/jobs/$externalId".asUrl,
        htmlUrl: Url? = "https://www.example.org/jobs/$externalId".asUrl,
        annotationsId: String? = null,
        type: String = "Job",
        status: BuildStatus = BuildStatus.entries.random(),
        result: BuildResult = BuildResult.entries.random(),
        attempt: Long? = null,
        createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
        startedAt: Instant? = null,
        completedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "BuildJob")

        BuildJobDAO.new(id) {
            this.build = build
            this.externalId = externalId
            this.externalParentId = externalParentId
            this.displayName = displayName
            this.apiUrl = apiUrl?.asString
            this.htmlUrl = htmlUrl?.asString
            this.annotationsId = annotationsId
            this.type = type
            this.status = status
            this.result = result
            this.createdAt = createdAt
            this.attempt = attempt ?: build.attempt
            this.createdAt = createdAt
            this.startedAt = startedAt
            this.completedAt = completedAt
        }.also {
            debugEnd(_label, "BuildJob", it)
        }
    }

    suspend fun makeBuildJobFailureStats(
        trx: Transaction? = null,
        _label: String? = null,
        org: OrgDAO? = null,
        build: BuildDAO? = null,
        buildJob: BuildJobDAO? = null,
        classification: BuildJobFailureClassification = BuildJobFailureClassification.entries.random(),
        insufficientLogReason: BuildJobFailureInsufficientLogsReason? = null,
        originalLogSize: Long = Random.nextLong(1000, 1000000),
        focusedLogSize: Long = Random.nextLong(10, 100),
        summarizedLogSize: Int? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "BuildJobFailureStats")

        val org = org ?: makeOrg(trx = this)

        val build = build
            ?: buildJob?.build
            ?: makeBuild(trx = this)

        val buildJob = buildJob
            ?: makeBuildJob(trx = this, build = build)

        checkSame(build, buildJob.build) {
            "Multiple builds detected"
        }

        BuildJobFailureStatsDAO.new {
            this.org = org.idValue
            this.build = build.idValue
            this.job = buildJob.idValue
            this.classification = classification
            this.insufficientLogReason = insufficientLogReason
            this.originalLogSize = originalLogSize
            this.focusedLogSize = focusedLogSize
            this.summarizedLogSize = summarizedLogSize
        }.also {
            debugEnd(_label, "BuildJobFailureStats", it)
        }
    }

    suspend fun makeBuildTriage(
        trx: Transaction? = null,
        _label: String? = null,
        id: BuildTriageId = BuildTriageId.random(),
        build: BuildDAO? = null,
        buildJobs: Collection<BuildJobDAO>? = null,
        groupKey: String = randomStrings.nextAscii(3),
        header: String = "Build triage: ${build?.displayName ?: id}",
        details: String = "Details for triage $id",
        modelReasoning: String? = null,
        modelReview: String? = null,
        modelDecision: String? = null,
        evalReasoning: String? = null,
        evalScore: String? = null,
        evalDecision: String? = null,
        state: BuildTriageState = BuildTriageState.entries.random(),
        commitIssueSha: String? = null,
        commitFixedSha: String? = null,
        isVisible: Boolean = true,
        isInsider: Boolean = false,
        reviewer: IdentityDAO? = null,
        reviewStatus: BuildTriageReviewStatus? = null,
        reviewRootCause: String? = null,
        reviewComment: String? = null,
        createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
    ) = suspendedTransaction(trx) {
        debug(_label, "BuildTriage")

        val theBuild = build ?: makeBuild(trx = this)
        val theBuildJobs = buildJobs?.nullIfEmpty() ?: makeBuildJob(trx = this, build = theBuild).let(::setOf)

        checkSame(theBuildJobs.map { it.build } + theBuild) {
            "Multiple builds detected"
        }

        BuildTriageDAO.new(id) {
            this.build = theBuild
            this.job = theBuildJobs.minBy { it.idValue }
            this.jobs = theBuildJobs.map { it.idValue }
            this.groupKey = groupKey
            this.header = header
            this.details = details
            this.modelReasoning = modelReasoning
            this.modelReview = modelReview
            this.modelDecision = modelDecision
            this.evalReasoning = evalReasoning
            this.evalScore = evalScore
            this.evalDecision = evalDecision
            this.state = state
            this.commitIssueSha = commitIssueSha
            this.commitFixedSha = commitFixedSha
            this.isVisible = isVisible
            this.isInsider = isInsider
            this.reviewer = reviewer
            this.reviewStatus = reviewStatus
            this.reviewRootCause = reviewRootCause
            this.reviewComment = reviewComment
            this.createdAt = createdAt
        }.also {
            debugEnd(_label, "BuildTriage", it)
        }
    }

    suspend fun makeBuildTriageExecution(
        trx: Transaction? = null,
        _label: String? = null,
        id: BuildTriageExecutionId = BuildTriageExecutionId.random(),
        org: OrgDAO? = null,
        ciInstallation: InstallationDAO? = null,
        scmTeam: ScmTeamDAO? = null,
        repo: RepoDAO? = null,
        pullRequest: PullRequestDAO? = null,
        pullRequestCreator: OrgMemberDAO? = null,
        build: BuildDAO? = null,
        buildJobs: Collection<BuildJobDAO>? = null,
        triage: BuildTriageDAO? = null,
        triageJobs: Collection<BuildJobDAO>? = null,
        state: BuildTriageExecutionState = BuildTriageExecutionState.Start,
        stage: BuildTriageFilterStage? = null,
        errorType: String? = null,
        errorText: String? = null,
        createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
        modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    ) = suspendedTransaction(trx) {
        debug(_label, "BuildTriageExecution")

        val org = org
            ?: ciInstallation?.org
            ?: scmTeam?.org
            ?: pullRequestCreator?.org
            ?: repo?.scmTeam?.org
            ?: makeOrg(trx = this)
        val ciInstallation = ciInstallation
            ?: build?.ciInstallation
            ?: makeInstallation(trx = this, org = org)
        val scmTeam = scmTeam
            ?: repo?.scmTeam
            ?: pullRequest?.repo?.scmTeam
            ?: build?.pullRequest?.repo?.scmTeam
            ?: makeScmTeam(trx = this, org = org)
        val repo = repo
            ?: pullRequest?.repo
            ?: build?.pullRequest?.repo
            ?: makeRepo(trx = this, scmTeam = scmTeam)
        val pullRequestCreator = pullRequestCreator
            ?: pullRequest?.creatorOrgMember
            ?: build?.pullRequest?.creatorOrgMember
            ?: makeOrgMember(trx = this, org = org)
        val pullRequest = pullRequest
            ?: build?.pullRequest
            ?: makePullRequest(trx = this, repo = repo, creatorOrgMember = pullRequestCreator)
        val build = build
            ?: triage?.build
            ?: makeBuild(trx = this, ciInstallation = ciInstallation, pullRequest = pullRequest)
        val buildJobs = buildJobs
            ?: makeBuildJob(trx = this, build = build).let(::listOf)

        BuildTriageExecutionDAO.new(id) {
            this.org = org.idValue
            this.ciInstallation = ciInstallation.idValue
            this.scmTeam = scmTeam.idValue
            this.repo = repo.idValue
            this.pullRequest = pullRequest.idValue
            this.pullRequestCreator = pullRequestCreator.idValue
            this.build = build.idValue
            this.buildJobs = buildJobs.map { it.idValue }
            this.triage = triage?.idValue
            this.triageJobs = triageJobs?.map { it.idValue }
            this.state = state
            this.stage = stage
            this.errorType = errorType
            this.errorText = errorText
            this.createdAt = createdAt
            this.modifiedAt = modifiedAt
        }.also {
            debugEnd(
                _label, "BuildTriageExecution", it,
                mapOf(
                    "org" to org.idValue,
                    "ciInstallation" to ciInstallation.idValue,
                    "scmTeam" to scmTeam.idValue,
                    "repo" to repo.idValue,
                    "pullRequest" to pullRequest.idValue,
                    "pullRequestCreator" to pullRequestCreator.idValue,
                    "build" to build.idValue,
                    "triage" to triage?.idValue,
                    "state" to state,
                    "stage" to stage,
                    "errorType" to errorType,
                ),
            )
        }
    }

    suspend fun makeSourceMark(
        trx: Transaction? = null,
        _label: String? = null,
        id: SourceMarkId = SourceMarkId.random(),
        scmTeam: ScmTeamDAO? = null,
        repo: RepoDAO? = null,
        thread: ThreadDAO? = null,
        isDeleted: Boolean = false,
        isArchived: Boolean = false,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "SourceMark")

        val markTeam = scmTeam ?: makeScmTeam(trx = this)
        val markRepo = repo ?: makeRepo(trx = this, scmTeam = markTeam)
        val markThread = thread ?: makeThread(trx = this, org = markTeam.org, scmTeam = markTeam)

        SourceMarkDAO.new(id) {
            this.repo = markRepo
            this.thread = markThread
            this.isDeleted = isDeleted
            this.isArchived = isArchived
            createdAt?.also { this.createdAt = createdAt }
            modifiedAt?.also { this.modifiedAt = modifiedAt }
        }.also {
            debugEnd(_label, "SourceMark", it)
        }
    }

    @Suppress("ComplexMethod")
    suspend fun makeSourcePoint(
        trx: Transaction? = null,
        _label: String? = null,
        id: SourcePointId = SourcePointId.random(),
        scmTeam: ScmTeamDAO? = null,
        sourceMark: SourceMarkDAO? = null,
        commitHash: ByteArray? = null,
        fileHash: ByteArray? = null,
        filePath: String? = null,
        lineStart: Int? = null,
        lineEnd: Int? = null,
        columnStart: Int? = null,
        columnEnd: Int? = null,
        snippet: SourceSnippet? = null,
        isOriginal: Boolean? = null,
        isTrusted: Boolean? = null,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "SourcePoint")

        val pointTeam = scmTeam ?: makeScmTeam(trx = this)
        val pointMark = sourceMark ?: makeSourceMark(trx = this, scmTeam = pointTeam)
        val pointLineStart = lineStart ?: 10
        val pointLineEnd = lineEnd ?: 11

        SourcePointDAO.new(id) {
            this.mark = pointMark
            this.commitHash = commitHash ?: makeSha1()
            this.fileHash = fileHash ?: makeSha1()
            this.filePath = filePath ?: "src/module/file-$id/.kt"
            this.lineStart = pointLineStart
            this.lineEnd = pointLineEnd
            this.isOriginal = isOriginal ?: false
            this.isTrusted = isTrusted ?: false
            this.snippetJson =
                (snippet ?: SourceSnippet(pointLineStart, (pointLineStart..pointLineEnd).map { "line $it" })).encode()
            columnStart?.also { this.columnStart = it }
            columnEnd?.also { this.columnEnd = it }
            createdAt?.also { this.createdAt = createdAt }
            modifiedAt?.also { this.modifiedAt = modifiedAt }
        }.also {
            debugEnd(_label, "SourcePoint", it)
        }
    }

    suspend fun makeFilePoint(
        trx: Transaction? = null,
        _label: String? = null,
        id: SourcePointId = SourcePointId.random(),
        scmTeam: ScmTeamDAO? = null,
        sourceMark: SourceMarkDAO? = null,
        commitHash: ByteArray? = null,
        fileHash: ByteArray? = null,
        filePath: String? = null,
        isOriginal: Boolean? = null,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "FilePoint")

        val pointTeam = scmTeam ?: makeScmTeam(trx = this)
        val pointMark = sourceMark ?: makeSourceMark(trx = this, scmTeam = pointTeam)

        SourcePointDAO.new(id) {
            this.mark = pointMark
            this.commitHash = commitHash ?: makeSha1()
            this.fileHash = fileHash ?: makeSha1()
            this.filePath = filePath ?: "src/module/file-$id/.kt"
            this.isOriginal = isOriginal ?: false
            createdAt?.also { this.createdAt = createdAt }
            modifiedAt?.also { this.modifiedAt = modifiedAt }
        }.also {
            debugEnd(_label, "FilePoint", it)
        }
    }

    suspend fun makeRegisteredDomain(
        trx: Transaction? = null,
        _label: String? = null,
        id: RegisteredDomainId? = null,
        org: OrgDAO? = null,
        domain: Hostname? = null,
        isVerified: Boolean? = null,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "RegisteredDomain")

        val domainOrg = org ?: makeOrg(trx = this)

        RegisteredDomainDAO.new(id) {
            this.org = domainOrg
            this.domain = domain?.value ?: "example.com"
            isVerified?.also {
                when (it) {
                    true -> this.isVerified = true
                    false -> this.isVerified = null
                }
            }
            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
        }.also {
            debugEnd(_label, "RegisteredDomain", it)
        }
    }

    suspend fun makeRepo(
        trx: Transaction? = null,
        _label: String? = null,
        id: RepoId = RepoId.random(),
        scmTeam: ScmTeamDAO? = null,
        installation: InstallationDAO? = null,
        httpUrl: String? = null,
        languages: List<String>? = null,
        rootHash: ByteArray? = null,
        isScmConnected: Boolean? = null,
        isUserSelected: Boolean? = null,
        isCiEnabled: Boolean? = null,
        isPublic: Boolean? = null,
        isFork: Boolean? = null,
        isEmpty: Boolean? = null,
        hasWiki: Boolean? = null,
        externalId: String? = null,
        externalName: String? = null,
        externalOwner: String? = null,
        provider: Provider? = null,
        ingestionBackoffUntil: Instant? = null,
        codeIngestLastStartAt: Instant? = null,
        codeIngestLastCommitSha: Hash? = null,
        pullRequestIngestLastStartAt: Instant? = null,
        pullRequestIngestLastPrNumber: Int? = null,
        topicIngestLastStartAt: Instant? = null,
        topicIngestLastCommitSha: Hash? = null,
        createdAt: Instant? = null,
        lastActiveAt: Instant? = null,
        revision: Int? = null,
        parentExternalOwner: String? = null,
        parentExternalName: String? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "Repo")
        checkSame(provider, scmTeam?.provider, installation?.provider) {
            "Multiple providers given"
        }

        val theProvider = provider ?: scmTeam?.provider ?: installation?.provider ?: Provider.GitHub
        val repoTeam = scmTeam ?: makeScmTeam(trx = this, installation = installation, provider = theProvider)

        RepoDAO.new(id) {
            this.scmTeam = repoTeam
            this.provider = theProvider
            this.httpUrl = httpUrl?.removeSuffix(".git") ?: "https://example.com/org/repo"
            this.languages = (languages.orEmpty()).encode()
            this.rootHash = rootHash ?: makeSha1()
            this.isScmConnected = isScmConnected ?: true
            this.isUserSelected = isUserSelected ?: true
            this.isCiEnabled = isCiEnabled
            this.isPublic = isPublic ?: false
            this.isFork = isFork ?: false
            this.isEmpty = isEmpty ?: false
            this.hasWiki = hasWiki ?: false
            this.externalId = externalId ?: UUID.randomUUID().toString()
            this.externalName = externalName ?: "repo"
            this.externalOwner = externalOwner ?: "org"
            this.ingestionBackoffUntil = ingestionBackoffUntil
            this.codeIngestLastStartAt = codeIngestLastStartAt
            this.codeIngestLastCommitSha = codeIngestLastCommitSha?.value
            this.pullRequestIngestStartAt = pullRequestIngestLastStartAt
            this.pullRequestIngestLastPrNumber = pullRequestIngestLastPrNumber
            this.topicIngestLastStartAt = topicIngestLastStartAt
            this.topicIngestLastCommitSha = topicIngestLastCommitSha?.value
            this.lastActiveAt = lastActiveAt ?: Instant.nowWithMicrosecondPrecision()
            this.parentExternalName = parentExternalName
            this.parentExternalOwner = parentExternalOwner
            revision?.also { this.revision = it }
            createdAt?.also { this.createdAt = createdAt }
        }.also {
            debugEnd(
                _label, "Repo", it,
                mapOf(
                    "provider" to theProvider,
                ),
            )
        }
    }

    suspend fun makePullRequestIngestion(
        trx: Transaction? = null,
        _label: String? = null,
        id: PullRequestIngestionId = PullRequestIngestionId.random(),
        repo: RepoDAO,
        initialIngestionComplete: Boolean = false,
        bulkIngestionComplete: Boolean = false,
        ingestionComplete: Boolean = false,
    ) = suspendedTransaction(trx) {
        debug(_label, "PullRequestIngestion")

        PullRequestIngestionDAO.new(id) {
            this.repo = repo
            this.initialIngestionComplete = initialIngestionComplete
            this.bulkIngestionComplete = bulkIngestionComplete
            this.ingestionComplete = ingestionComplete
        }.also {
            debugEnd(_label, "PullRequestIngestion", it)
        }
    }

    suspend fun makePrefilledAuthState(
        provider: Provider = Provider.GitHub,
    ): AuthenticationStateDAO = suspendedTransaction {
        debug(null, "PrefilledAuthState")

        val identity = makeIdentity(
            trx = this,
            displayName = "Foo Bar",
            primaryEmail = EmailAddress.of("<EMAIL>"),
            emails = listOf(EmailAddress.of("<EMAIL>")),
            person = makePerson(trx = this, id = PersonId.random(), customDisplayName = "First Last"),
            provider = provider,
        )

        makeAuthenticationState(
            trx = this,
            identity = identity,
            nonce = UUID.randomUUID(),
            secret = UUID.randomUUID(),
        ).also {
            debugEnd(
                null, "PrefilledAuthState", it,
            )
        }
    }

    @Suppress("ComplexMethod")
    suspend fun makeThread(
        trx: Transaction? = null,
        _label: String? = null,
        id: ThreadId = ThreadId.random(),
        author: MemberDAO? = null,
        authorOrgMember: OrgMemberDAO? = null,
        org: OrgDAO? = null,
        scmTeam: ScmTeamDAO? = null,
        installation: InstallationDAO? = null,
        repo: RepoDAO? = null,
        title: String? = null,
        lastMessageCreatedAt: Instant? = null,
        isDeleted: Boolean? = null,
        archivedAt: Instant? = null,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
        archivedBy: OrgMemberDAO? = null,
        archivedReason: ArchivedReason? = null,
        pullRequest: PullRequestDAO? = null,
        slackThreadTs: String? = null,
        slackChannel: SlackChannelDAO? = null,
        slackTeam: SlackTeamDAO? = null,
        isTopLevelCommentThread: Boolean = false,
        isPrivate: Boolean? = null,
        linearTeam: LinearTeamDAO? = null,
        linearIssueId: String? = null,
        linearIssueKey: String? = null,
        gitHubIssueNumber: Int? = null,
        issueStatus: IssueStatus? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "Thread")
        checkSame(org, installation?.org, authorOrgMember?.org) {
            "Multiple orgs given"
        }
        checkSame(scmTeam, repo?.scmTeam) {
            "Multiple scmTeams given"
        }
        checkSame(author?.orgMember ?: authorOrgMember) {
            "Multiple authorOrgMember given"
        }

        val threadOrg = org ?: installation?.org ?: authorOrgMember?.org ?: makeOrg(trx = this)
        val threadScmTeam = scmTeam ?: repo?.scmTeam ?: makeScmTeam(trx = this, org = threadOrg, installation = installation)
        checkSame(threadScmTeam.org, threadOrg)
        checkSame(threadScmTeam.installation, installation)

        val threadRepo = repo ?: makeRepo(trx = this, scmTeam = threadScmTeam)
        checkSame(threadRepo.scmTeam, threadScmTeam)

        val threadAuthorOrgMember = author?.orgMember ?: authorOrgMember ?: makeOrgMember(trx = this, org = threadOrg)

        ThreadDAO.new(id) {
            this.authorOrgMember = threadAuthorOrgMember
            this.org = threadOrg
            this.repo = threadRepo
            this.title = title ?: "Thread title $id!"
            this.isTopLevelCommentThread = isTopLevelCommentThread
            installation?.also { this.installation = it }
            isPrivate?.also { this.isPrivate = it }
            lastMessageCreatedAt?.also { this.lastMessageCreatedAt = lastMessageCreatedAt }
            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
            isDeleted?.also { this.isDeleted = it }
            archivedAt?.also { this.archivedAt = it }
            archivedBy?.also { this.archivedBy = it }
            archivedReason?.also { this.archivedReason = it }
            pullRequest?.also { this.pullRequest = it }
            slackThreadTs?.also { this.slackThreadTs = it }
            slackChannel?.also { this.slackChannel = it }
            slackTeam?.also { this.slackTeam = it }
            linearTeam?.also { this.linearTeam = it }
            linearIssueId?.also { this.linearIssueId = it }
            linearIssueKey?.also { this.linearIssueKey = it }
            issueStatus?.also { this.issueStatus = it }
            gitHubIssueNumber?.also { this.gitHubIssueNumber = it }
        }.also { thread ->
            pullRequest?.also {
                makeThreadPullRequest(trx = this, repo = threadRepo, thread = thread, pullRequest = pullRequest)
            }
        }.also {
            debugEnd(_label, "Thread", it)
        }
    }

    suspend fun makeThreadPullRequest(
        trx: Transaction? = null,
        _label: String? = null,
        id: ThreadPullRequestId = ThreadPullRequestId.random(),
        repo: RepoDAO,
        thread: ThreadDAO,
        pullRequest: PullRequestDAO? = null,
        number: Int = pullRequest?.number ?: 0,
    ) = suspendedTransaction(trx) {
        debug(_label, "ThreadPullRequest")

        ThreadPullRequestDAO.new(id) {
            this.thread = thread
            this.pullRequest = pullRequest
            this.repo = repo
            this.number = number
        }.also {
            debugEnd(_label, "ThreadPullRequest", it)
        }
    }

    suspend fun makeSamlIdpMetadata(
        trx: Transaction? = null,
        _label: String? = null,
        id: SamlIdpMetadataId = SamlIdpMetadataId.random(),
        org: OrgDAO? = null,
        installation: InstallationDAO? = null,
        entityId: String = "http://richie.com",
        singleSignOnUrl: Url = "http://richie.com/saml/idp?idp=richie".asUrl,
        x509Certificate: String = "richiecert",
        samlIdentityProvider: SamlIdentityProvider = SamlIdentityProvider.GoogleWorkspace,
        isEnforced: Boolean? = null,
    ) = suspendedTransaction(trx = trx) {
        debug(_label, "SamlIdpMetadata")

        val samlOrg = org ?: makeOrg(trx = this)
        val samlInstallation = installation ?: makeInstallation(
            trx = this,
            org = samlOrg,
            provider = samlIdentityProvider.asDbProvider,
        )

        SamlIdpMetadataDAO.new(id) {
            this.org = samlOrg
            this.installation = samlInstallation
            this.entityId = entityId
            this.singleSignOnUrl = singleSignOnUrl.asString
            this.x509Certificate = x509Certificate
            this.identityProvider = samlIdentityProvider
            when (isEnforced) {
                null -> doNothing()
                true -> this.isEnforced = isEnforced
                false -> this.isEnforced = null // never set false in tests
            }
        }.also {
            debugEnd(_label, "SamlIdpMetadata", it)
        }
    }

    suspend fun makeMessage(
        trx: Transaction? = null,
        _label: String? = null,
        messageId: MessageId = MessageId.random(),
        author: MemberDAO? = null,
        authorOrgMember: OrgMemberDAO? = null,
        installation: InstallationDAO? = null,
        thread: ThreadDAO? = null,
        content: ByteArray = "this is a message".asMessageBody().toByteArray(),
        contentVersion: String = "1",
        prCommentId: String? = null,
        prCommentUpdatedAt: Instant? = null,
        prCommentBodyHash: ByteArray? = null,
        prReviewId: String? = null,
        isDeleted: Boolean = false,
        slackMessageTs: String? = null,
        slackMessageUrl: String? = null,
        showFeedback: Boolean? = null,
        isLoading: Boolean? = null,
        jiraIssueId: String? = null,
        jiraCommentId: String? = null,
        linearIssueId: String? = null,
        linearCommentId: String? = null,
        isStreaming: Boolean? = null,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
        userLocalContext: ByteArray? = null,
        sensitiveDataSources: ByteArray? = null,
        createAuthor: Boolean = true,
        createPerson: Boolean = true,
    ) = suspendedTransaction(trx) {
        debug(_label, "Message")
        checkCreate(author, createAuthor) { "Unexpected author given when createAuthor=false" }

        val org = installation?.org ?: thread?.org ?: authorOrgMember?.org ?: author?.orgMember?.org ?: makeOrg(trx = this)
        val messageInstallation = installation ?: author?.installation ?: makeInstallation(trx = this, org = org)
        val messageThread = thread ?: makeThread(trx = this, org = org, installation = messageInstallation)

        val messageAuthor = author ?: createIf(createAuthor) {
            makeMember(
                trx = this,
                installation = messageInstallation,
                orgMember = authorOrgMember ?: makeOrgMember(
                    trx = this,
                    org = org,
                    createPerson = createPerson,
                ),
            )
        }

        val messageAuthorOrgMember = authorOrgMember ?: messageAuthor?.orgMember ?: makeOrgMember(
            trx = this,
            org = org,
            createPerson = createPerson,
        )

        MessageDAO.new(messageId) {
            createdAt?.let { this.createdAt = it }
            modifiedAt?.let { this.modifiedAt = it }
            this.author = messageAuthor
            this.authorOrgMember = messageAuthorOrgMember
            this.thread = messageThread
            this.content = content
            this.contentVersion = contentVersion
            this.isDeleted = isDeleted
            prCommentId?.also { this.prCommentId = it }
            prCommentUpdatedAt?.also { this.prCommentUpdatedAt = it }
            prCommentBodyHash?.also { this.prCommentBodyHash = it }
            prReviewId?.also { this.prReviewId = it }
            slackMessageTs?.also { this.slackMessageTs = it }
            slackMessageUrl?.also { this.slackMessageUrl = it }
            showFeedback?.also { this.showFeedback = it }
            isLoading?.also { this.isLoading = it }
            jiraIssueId?.also { this.jiraIssueId = it }
            jiraCommentId?.also { this.jiraCommentId = it }
            linearIssueId?.also { this.linearIssueId = it }
            linearCommentId?.also { this.linearCommentId = it }
            isStreaming?.also { this.isStreaming = it }
            userLocalContext?.also { this.userLocalContext = it }
            sensitiveDataSources?.also { this.sensitiveDataSources = it }
        }.also {
            debugEnd(_label, "Message", it)
        }
    }

    suspend fun makePersonEmailPreferences(
        trx: Transaction? = null,
        _label: String? = null,
        id: PersonEmailPreferencesId = PersonEmailPreferencesId.random(),
        person: PersonDAO? = null,
        digestEmails: Boolean = false,
        threadInviteEmails: Boolean = false,
        recommendedInviteeEmails: Boolean = false,
    ) = suspendedTransaction(trx = trx) {
        debug(_label, "PersonEmailPreferences")

        val resolvedPerson = person ?: makePerson(trx = this)

        PersonEmailPreferencesDAO.new(id) {
            this.person = resolvedPerson
            this.digestEmails = digestEmails
            this.threadInviteEmails = threadInviteEmails
            this.recommendedInviteeEmails = recommendedInviteeEmails
        }.also {
            debugEnd(_label, "PersonEmailPreferences", it)
        }
    }

    suspend fun makeThreadParticipant(
        trx: Transaction? = null,
        _label: String? = null,
        id: ThreadParticipantId = ThreadParticipantId.random(),
        scmTeam: ScmTeamDAO? = null,
        thread: ThreadDAO? = null,
        member: MemberDAO? = null,
        orgMember: OrgMemberDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "ThreadParticipant")

        val participantTeam = scmTeam ?: makeScmTeam(trx = this)
        val participantThread = thread ?: makeThread(trx = this, org = participantTeam.org)
        val participantMember = member ?: makeMember(trx = this, scmTeam = participantTeam)
        val participantOrgMember = orgMember ?: participantMember.orgMember

        ThreadParticipantDAO.new(id) {
            this.thread = participantThread
            this.member = participantMember
            this.orgMember = participantOrgMember
        }.also {
            debugEnd(_label, "ThreadParticipant", it)
        }
    }

    suspend fun makeMessageMention(
        id: MessageMentionId = MessageMentionId.random(),
        trx: Transaction? = null,
        _label: String? = null,
        thread: ThreadDAO? = null,
        message: MessageDAO? = null,
        member: MemberDAO? = null,
        orgMember: OrgMemberDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "MessageMention")

        val mentionThread = thread ?: makeThread(trx = this)
        val mentionMessage = message ?: makeMessage(trx = this, thread = mentionThread)
        val mentionTeam = makeScmTeam(trx = this, org = mentionThread.org)
        val mentionMember = member ?: makeMember(trx = this, scmTeam = mentionTeam)
        val mentionOrgMember = orgMember ?: mentionMember.orgMember

        MessageMentionDAO.new(id) {
            this.thread = mentionThread
            this.message = mentionMessage
            this.member = mentionMember
            this.orgMember = mentionOrgMember
        }.also {
            debugEnd(_label, "MessageMention", it)
        }
    }

    suspend fun makeMessageFeedback(
        id: MessageFeedbackId = MessageFeedbackId.random(),
        trx: Transaction? = null,
        _label: String? = null,
        org: OrgDAO? = null,
        thread: ThreadDAO? = null,
        message: MessageDAO? = null,
        orgMember: OrgMemberDAO? = null,
        feedbackType: FeedbackType? = null,
        feedbackDescription: String? = null,
        createdAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "MessageFeedback")

        val feedbackOrg = org ?: makeOrg(trx = this)
        val feedbackInstallation = makeInstallation(trx = this, org = feedbackOrg)
        val feedbackThread = thread ?: makeThread(trx = this, org = feedbackOrg, installation = feedbackInstallation)
        val feedbackMessage = message ?: makeMessage(trx = this, thread = feedbackThread, installation = feedbackInstallation)
        val feedbackOrgMember = orgMember ?: makeOrgMember(trx = this, org = feedbackOrg)

        MessageFeedbackDAO.new(id) {
            this.message = feedbackMessage
            this.thread = feedbackThread
            this.orgMember = feedbackOrgMember
            this.feedbackType = feedbackType ?: FeedbackType.Positive
            this.feedbackDescription = feedbackDescription
            createdAt?.also { this.createdAt = it }
        }.also {
            debugEnd(_label, "MessageFeedback", it)
        }
    }

    suspend fun makeThreadUnread(
        id: ThreadUnreadId = ThreadUnreadId.random(),
        trx: Transaction? = null,
        _label: String? = null,
        installation: InstallationDAO? = null,
        thread: ThreadDAO? = null,
        member: MemberDAO? = null,
        orgMember: OrgMemberDAO? = null,
        latestMessage: MessageDAO? = null,
        latestReadMessage: MessageDAO? = null,
        modifiedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "ThreadUnread")

        val unreadInstallation = installation ?: member?.installation ?: thread?.installation ?: makeInstallation(trx = this)
        val unreadThread = thread ?: makeThread(trx = this, org = unreadInstallation.org, installation = unreadInstallation)
        val unreadMember = member ?: makeMember(trx = this, installation = unreadInstallation, orgMember = orgMember)
        val unreadOrgMember = orgMember ?: unreadMember.orgMember
        val unreadLatestMessage = latestMessage ?: makeMessage(trx = this, thread = unreadThread)

        ThreadUnreadDAO.new(id) {
            this.thread = unreadThread
            this.orgMember = unreadOrgMember
            this.latestMessage = unreadLatestMessage
            this.latestReadMessage = latestReadMessage
            this.isUnread = when {
                this.latestReadMessage == null -> true
                this.latestReadMessage != this.latestMessage -> true
                else -> null
            }
            modifiedAt?.also { this.modifiedAt = it }
        }.also {
            debugEnd(_label, "ThreadUnread", it)
        }
    }

    suspend fun makeSocialNetwork(
        id: SocialNetworkId = SocialNetworkId.random(),
        trx: Transaction? = null,
        _label: String? = null,
        installation: InstallationDAO? = null,
        author: OrgMemberDAO? = null,
        reviewer: OrgMemberDAO? = null,
        weight: Float? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "SocialNetwork")
        checkSame(installation?.org, author?.org, reviewer?.org) {
            "Multiple org given"
        }

        val useOrg = installation?.org ?: author?.org ?: reviewer?.org

        val theInstallation = installation ?: makeInstallation(trx = this, org = useOrg)
        val theAuthor = author ?: makeMember(trx = this, org = theInstallation.org).orgMember
        val theReviewer = reviewer ?: makeMember(trx = this, org = theInstallation.org).orgMember

        SocialNetworkDAO.new(id) {
            this.authorOrgMember = theAuthor
            this.reviewerOrgMember = theReviewer
            this.weight = weight ?: Random.nextDouble(0.0, 1.0).toFloat()
        }.also {
            debugEnd(_label, "SocialNetwork", it)
        }
    }

    fun makeSha1() = Random.nextBytes(20)

    suspend fun makeAsset(
        trx: Transaction? = null,
        _label: String? = null,
        id: AssetId = AssetId.random(),
        installation: InstallationDAO? = null,
        author: OrgMemberDAO? = null,
        thread: ThreadDAO? = null,
        message: MessageDAO? = null,
        name: String = "asset",
        contentType: String = "image/jpg",
        contentLength: Int = 0,
        isDeleted: Boolean = false,
        bucket: String = "bucket",
        key: String = "key",
    ) = suspendedTransaction(trx) {
        debug(_label, "Asset")

        val assetInstallation = installation ?: thread?.installation ?: makeInstallation(trx = this)
        val assetAuthor = author ?: makeOrgMember(trx = this, org = assetInstallation.org)
        val assetThread = thread ?: makeThread(trx = this, org = assetInstallation.org)
        val assetMessage = message ?: makeMessage(trx = this, thread = assetThread)

        AssetDAO.new(id) {
            this.authorOrgMember = assetAuthor
            this.thread = assetThread
            this.message = assetMessage
            this.name = name
            this.contentLength = contentLength
            this.contentType = contentType
            this.bucket = bucket
            this.key = key
            this.isDeleted = isDeleted
        }.also {
            debugEnd(_label, "Asset", it)
        }
    }

    suspend fun makeAssetRelationship(
        trx: Transaction? = null,
        _label: String? = null,
        id: AssetRelationshipId = AssetRelationshipId.random(),
        sourceAsset: AssetDAO? = null,
        targetAsset: AssetDAO? = null,
        type: AssetRelationshipType = AssetRelationshipType.THUMBNAIL,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "AssetRelationship")

        // Reuse provided assets, or create fresh ones if you have a makeAsset(...) helper.
        // If you prefer to require explicit assets, remove the fallbacks.
        val src = sourceAsset ?: makeAsset(trx = this)
        val tgt = targetAsset ?: makeAsset(trx = this)

        AssetRelationshipDAO.new(id) {
            this.sourceAsset = src
            this.targetAsset = tgt
            this.type = type
            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
        }.also {
            debugEnd(
                _label,
                "AssetRelationship",
                it,
                mapOf(
                    "sourceAsset" to src.idValue,
                    "targetAsset" to tgt.idValue,
                    "type" to type,
                ),
            )
        }
    }

    suspend fun makeAsanaWorkspace(
        trx: Transaction? = null,
        _label: String? = null,
        id: AsanaWorkspaceId = AsanaWorkspaceId.random(),
        installation: InstallationDAO? = null,
        org: OrgDAO? = null,
        asanaGid: String = "asana-workspace-${UUID.randomUUID()}",
        name: String = "Test Workspace-${UUID.randomUUID()}",
        isOrganization: Boolean = false,
        emailDomains: List<String>? = listOf("test.com-${UUID.randomUUID()}"),
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
        projectIngestionType: AsanaProjectIngestionType? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "AsanaWorkspace")
        checkSame(org?.idValue, installation?.org?.idValue) { "Inconsistent org and installation.org" }

        val workspaceOrg = org ?: installation?.org ?: makeOrg(trx = this)

        // If you pass in an installation, you have to pass in the asanaGid
        installation?.let { checkSame(installation.installationExternalId, asanaGid) }
        val workspaceInstallation = installation ?: makeInstallation(trx = this, org = workspaceOrg, provider = Provider.Asana)
        workspaceInstallation.installationExternalId = asanaGid

        AsanaWorkspaceDAO.new(id) {
            this.installation = workspaceInstallation
            this.asanaGid = asanaGid
            this.name = name
            this.isOrganization = isOrganization
            this.emailDomains = emailDomains?.joinToString(",")
            this.projectIngestionType = projectIngestionType
            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
        }.also {
            debugEnd(_label, "AsanaWorkspace", it)
        }
    }

    suspend fun makeAsanaProject(
        trx: Transaction? = null,
        _label: String? = null,
        id: AsanaProjectId = AsanaProjectId.random(),
        workspace: AsanaWorkspaceDAO? = null,
        installation: InstallationDAO? = null,
        org: OrgDAO? = null,
        asanaGid: String = "asana-project-${UUID.randomUUID()}",
        asanaCreatedAt: Instant = Instant.nowWithMicrosecondPrecision(),
        team: AsanaTeamDAO? = null,
        name: String = "Test Project-${UUID.randomUUID()}",
        shouldIngest: Boolean? = null,
        updatedAt: Instant? = null,
        completedAt: Instant? = null,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
        ownerMember: OrgMemberDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "AsanaProject")
        checkSame(org?.idValue, installation?.org?.idValue, workspace?.installation?.org?.idValue) {
            "Inconsistent org across parameters"
        }
        val projectOrg = org ?: installation?.org ?: workspace?.installation?.org ?: makeOrg(trx = this)
        val projectInstallation = installation ?: workspace?.installation ?: makeInstallation(trx = this, org = projectOrg, provider = Provider.Asana)
        val projectTeam = team ?: makeAsanaTeam(trx = this, installation = projectInstallation)
        val project = AsanaProjectDAO.new(id) {
            this.installation = projectInstallation
            this.team = projectTeam
            this.asanaGid = asanaGid
            this.name = name
            this.asanaCreatedAt = asanaCreatedAt
            this.shouldIngest = shouldIngest
            this.updatedAt = updatedAt
            this.completedAt = completedAt
            this.createdAt = createdAt ?: Instant.nowWithMicrosecondPrecision()
            this.modifiedAt = modifiedAt ?: Instant.nowWithMicrosecondPrecision()
            this.ownerMember = ownerMember
        }
        debugEnd(_label, "AsanaProject", project)
        project
    }

    suspend fun makeAsanaTaskAccess(
        trx: Transaction? = null,
        _label: String? = null,
        id: AsanaTaskAccessId = AsanaTaskAccessId.random(),
        identity: IdentityDAO,
        task: AsanaTaskDAO,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        val resourceAccess = AsanaTaskAccessDAO.new(id) {
            this.identity = identity
            this.task = task
            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
        }

        debugEnd(_label, "AsanaResourceAccess", resourceAccess)
        return@suspendedTransaction resourceAccess
    }

    suspend fun makeAsanaProjectAccess(
        trx: Transaction? = null,
        _label: String? = null,
        id: AsanaProjectAccessId = AsanaProjectAccessId.random(),
        identity: IdentityDAO,
        project: AsanaProjectDAO,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        val resourceAccess = AsanaProjectAccessDAO.new(id) {
            this.identity = identity
            this.project = project
            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
        }

        debugEnd(_label, "AsanaResourceAccess", resourceAccess)
        return@suspendedTransaction resourceAccess
    }

    suspend fun makeAsanaTask(
        trx: Transaction? = null,
        _label: String? = null,
        id: AsanaTaskId = AsanaTaskId.random(),
        installation: InstallationDAO? = null,
        projects: List<AsanaProjectDAO>? = null,
        org: OrgDAO? = null,
        parentTask: AsanaTaskDAO? = null,
        asanaGid: String = "task-${UUID.randomUUID()}",
        name: String = "Task ${UUID.randomUUID()}",
        permaLink: String = "https://app.asana.com/0/123456/$asanaGid",
        dueOn: String? = null,
        updatedAt: Instant? = null,
        completedAt: Instant? = null,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
        assigneeMember: OrgMemberDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "AsanaTask")
        if (parentTask != null && installation != null) {
            check(parentTask.installation.idValue == installation.idValue) {
                "Inconsistent installation between parentTask and provided installation"
            }
        }
        val taskOrg = org ?: installation?.org ?: parentTask?.installation?.org ?: makeOrg(trx = this)
        val taskInstallation = installation ?: parentTask?.installation ?: makeInstallation(trx = this, org = taskOrg, provider = Provider.Asana)
        val task = AsanaTaskDAO.new(id) {
            this.installation = taskInstallation
            this.parentTask = parentTask
            this.asanaGid = asanaGid
            this.name = name
            this.permaLink = permaLink
            this.dueOn = dueOn
            this.updatedAt = updatedAt
            this.completedAt = completedAt
            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
            this.assigneeMember = assigneeMember
        }
        projects?.forEach { project ->
            AsanaProjectTaskDAO.new {
                this.project = project
                this.task = task
            }
        }
        debugEnd(_label, "AsanaTask", task)
        return@suspendedTransaction task
    }

    suspend fun makeAsanaTeam(
        trx: Transaction? = null,
        _label: String? = null,
        org: OrgDAO? = null,
        installation: InstallationDAO? = null,
        asanaGid: String = "team-${UUID.randomUUID()}",
        name: String = "Team ${UUID.randomUUID()}",
        permalinkUrl: String? = "https://app.asana.com/0/team-${UUID.randomUUID()}",
    ): AsanaTeamDAO = suspendedTransaction(trx) {
        val teamOrg = org ?: makeOrg(trx = this)
        val teamInstallation = installation ?: makeInstallation(trx = this, org = teamOrg, provider = Provider.Asana)

        val team = AsanaTeamDAO.new {
            this.installation = teamInstallation
            this.asanaGid = asanaGid
            this.name = name
            this.permalinkUrl = permalinkUrl
        }

        debugEnd(_label, "AsanaTeam", team)
        return@suspendedTransaction team
    }

    suspend fun makeAsanaTeamMembership(
        trx: Transaction? = null,
        _label: String? = null,
        team: AsanaTeamDAO,
        member: MemberDAO,
    ): AsanaTeamMembershipDAO = suspendedTransaction(trx) {
        val teamMembership = AsanaTeamMembershipDAO.new {
            this.team = team
            this.member = member
        }
        debugEnd(_label, "AsanaTeamMembership", teamMembership)
        return@suspendedTransaction teamMembership
    }

    suspend fun makeSlackTeam(
        trx: Transaction? = null,
        _label: String? = null,
        id: SlackTeamId = SlackTeamId.random(),
        org: OrgDAO? = null,
        installation: InstallationDAO? = null,
        slackExternalTeamId: String = "slackTeam${UUID.randomUUID()}",
        name: String = "slackTeamName",
        botAccessToken: ByteArray = "blah".toByteArray(),
        userScope: String = "channels:read",
        userId: String? = "userId",
        userAccessToken: ByteArray = "blah".toByteArray(),
        botUserId: String = "botUserId",
        botScope: String = "channels:read",
        domain: String? = null,
        htmlUrl: Url? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "SlackTeam")

        val slackOrg = org ?: installation?.org ?: makeOrg(trx = this)
        val slackTeamInstallation = installation ?: makeInstallation(trx = this, org = slackOrg, installationExternalId = slackExternalTeamId)

        SlackTeamDAO.new(id) {
            this.installation = slackTeamInstallation
            this.slackExternalTeamId = slackExternalTeamId
            this.name = name
            this.userAccessToken = userAccessToken
            this.userId = userId
            this.userScope = userScope
            this.botAccessToken = botAccessToken
            this.botUserId = botUserId
            this.botScope = botScope
            this.domain = domain ?: "acme"
            this.htmlUrl = htmlUrl?.asString ?: "https://acme.slack.com/"
        }.also {
            debugEnd(_label, "SlackTeam", it)
        }
    }

    suspend fun makeSlackIngestion(
        trx: Transaction? = null,
        _label: String? = null,
        id: SlackIngestionId = SlackIngestionId.random(),
        slackTeam: SlackTeamDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "SlackIngestion")

        val thisSlackTeam = slackTeam ?: makeSlackTeam(trx = this)

        SlackIngestionDAO.new(id) {
            this.slackTeam = thisSlackTeam
        }.also {
            debugEnd(_label, "SlackIngestion", it)
        }
    }

    suspend fun makeSlackChannel(
        trx: Transaction? = null,
        _label: String? = null,
        id: SlackChannelId = SlackChannelId.random(),
        slackTeam: SlackTeamDAO? = null,
        slackExternalChannelId: String = "slackChannel${UUID.randomUUID()}",
        name: String = "slackChannelName",
        isPrivate: Boolean? = null,
        isIm: Boolean? = null,
        isChannel: Boolean? = null,
        isShared: Boolean? = null,
        isGroup: Boolean? = null,
        isMember: Boolean? = null,
        memberCount: Int? = null,
        firstMessageTs: String? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "SlackChannel")

        val slackChannelSlackTeam = slackTeam ?: makeSlackTeam(trx = this)

        SlackChannelDAO.new(id) {
            this.slackExternalChannelId = slackExternalChannelId
            this.slackTeam = slackChannelSlackTeam
            this.name = name
            isPrivate?.also { this.isPrivate = it }
            isIm?.also { this.isIm = it }
            isChannel?.also { this.isChannel = it }
            isShared?.also { this.isShared = it }
            isGroup?.also { this.isGroup = it }
            isMember?.also { this.isMember = it }
            memberCount?.also { this.memberCount = it }
            firstMessageTs?.also { this.firstMessageTs = it }
        }.also {
            debugEnd(_label, "SlackChannel", it)
        }
    }

    suspend fun makeSlackChannelMember(
        trx: Transaction? = null,
        _label: String? = null,
        id: SlackChannelMemberId = SlackChannelMemberId.random(),
        slackTeam: SlackTeamDAO? = null,
        slackChannel: SlackChannelDAO? = null,
        identity: IdentityDAO? = null,
        member: MemberDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "SlackChannelMember")
        checkSame(slackTeam, slackChannel?.slackTeam) {
            "Multiple slackTeams given"
        }

        val slackChannelMemberSlackTeam = slackTeam ?: slackChannel?.slackTeam ?: makeSlackTeam(trx = this)
        val slackChannelMemberSlackChannel = slackChannel ?: makeSlackChannel(trx = this)
        val slackChannelMemberIdentity = identity ?: member?.identity ?: makeIdentity(trx = this, provider = Provider.Slack)
        val slackChannelMemberMember = member ?: makeMember(trx = this, isCurrentMember = false, identity = slackChannelMemberIdentity)

        SlackChannelMemberDAO.new(id) {
            this.slackTeam = slackChannelMemberSlackTeam
            this.slackChannel = slackChannelMemberSlackChannel
            this.member = slackChannelMemberMember
            this.orgMember = slackChannelMemberMember.orgMember
            this.slackMemberExternalId = slackChannelMemberIdentity.externalId
            this.slackChannelExternalId = slackChannelMemberSlackChannel.slackExternalChannelId
        }.also {
            debugEnd(_label, "SlackChannelMember", it)
        }
    }

    suspend fun makeSlackChannelPreferences(
        trx: Transaction? = null,
        _label: String? = null,
        id: SlackChannelPreferencesId = SlackChannelPreferencesId.random(),
        slackTeam: SlackTeamDAO? = null,
        slackChannel: SlackChannelDAO? = null,
        autoAnswerMode: SlackAutoAnswerMode? = null,
        dataSourcePreset: DataSourcePresetDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "SlackChannelPreferences")
        checkSame(slackTeam, slackChannel?.slackTeam) {
            "Multiple slackTeams given"
        }

        val thisSlackTeam = slackTeam ?: slackChannel?.slackTeam ?: makeSlackTeam(trx = this)
        val thisSlackChannel = slackChannel ?: makeSlackChannel(trx = this, slackTeam = thisSlackTeam)

        SlackChannelPreferencesDAO.new(id) {
            this.slackTeam = thisSlackTeam
            this.slackChannel = thisSlackChannel
            this.autoAnswerMode = autoAnswerMode
            this.dataSourcePreset = dataSourcePreset
            autoAnswerMode?.also { this.autoAnswerLastModifiedAt = Instant.nowWithMicrosecondPrecision() }
        }.also {
            debugEnd(_label, "SlackChannelPreferences", it)
        }
    }

    suspend fun makeSlackChannelPatternPreferences(
        trx: Transaction? = null,
        _label: String? = null,
        id: SlackChannelPatternPreferencesId = SlackChannelPatternPreferencesId.random(),
        slackChannelPattern: String = "richie",
        slackTeam: SlackTeamDAO? = null,
        autoAnswerMode: SlackAutoAnswerMode? = null,
        dataSourcePreset: DataSourcePresetDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "SlackChannelPatternPreferences")

        val thisSlackTeam = slackTeam ?: makeSlackTeam(trx = this)

        SlackChannelPatternPreferencesDAO.new(id) {
            this.slackTeam = thisSlackTeam
            this.slackChannelPattern = slackChannelPattern
            this.autoAnswerMode = autoAnswerMode
            this.dataSourcePreset = dataSourcePreset
        }.also {
            debugEnd(_label, "SlackChannelPatternPreferences", it)
        }
    }

    suspend fun makeSlackChannelIngestion(
        trx: Transaction? = null,
        _label: String? = null,
        id: SlackChannelIngestionId = SlackChannelIngestionId.random(),
        slackTeam: SlackTeamDAO? = null,
        slackChannel: SlackChannelDAO? = null,
        slackIngestion: SlackIngestionDAO? = null,
        status: SlackChannelIngestionStatus = SlackChannelIngestionStatus.Unknown,
        lastFinishedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "SlackChannelIngestion")
        checkSame(slackTeam, slackChannel?.slackTeam) {
            "Multiple slackTeams given"
        }

        val thisSlackTeam = slackTeam ?: slackChannel?.slackTeam ?: makeSlackTeam(trx = this)
        val thisSlackIngestion = slackIngestion ?: makeSlackIngestion(trx = this, slackTeam = thisSlackTeam)
        val thisSlackChannel = slackChannel ?: makeSlackChannel(trx = this, slackTeam = thisSlackTeam)

        SlackChannelIngestionDAO.new(id) {
            this.slackIngestion = thisSlackIngestion
            this.slackTeam = thisSlackTeam
            this.slackChannel = thisSlackChannel
            this.status = status
            this.lastFinishedAt = lastFinishedAt
        }.also {
            debugEnd(_label, "SlackChannelIngestion", it)
        }
    }

    suspend fun makeSlackChannelMember(
        trx: Transaction? = null,
        _label: String? = null,
        id: SlackChannelMemberId = SlackChannelMemberId.random(),
        slackTeam: SlackTeamDAO? = null,
        slackChannel: SlackChannelDAO? = null,
        member: MemberDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "SlackChannelMember")
        checkSame(slackTeam, slackChannel?.slackTeam) {
            "Multiple slackTeams given"
        }

        val thisSlackTeam = slackTeam ?: slackChannel?.slackTeam ?: makeSlackTeam(trx = this)
        val thisSlackChannel = slackChannel ?: makeSlackChannel(trx = this, slackTeam = thisSlackTeam)
        val thisMember = member ?: makeMember(trx = this)

        SlackChannelMemberDAO.new(id) {
            this.slackTeam = thisSlackTeam
            this.slackChannel = thisSlackChannel
            this.member = thisMember
            this.orgMember = thisMember.orgMember
            this.slackChannelExternalId = thisSlackChannel.slackExternalChannelId
            this.slackMemberExternalId = thisMember.identity.externalId
        }.also {
            debugEnd(_label, "SlackChannelMember", it)
        }
    }

    suspend fun makeSlackChannelsIngestion(
        trx: Transaction? = null,
        _label: String? = null,
        id: SlackChannelsIngestionId = SlackChannelsIngestionId.random(),
        slackTeam: SlackTeamDAO? = null,
        modifiedAt: Instant? = null,
        lastFinishedAt: Instant? = null,
        status: SlackChannelsIngestionStatus = SlackChannelsIngestionStatus.Unknown,
    ) = suspendedTransaction(trx) {
        debug(_label, "SlackChannelsIngestion")

        val thisSlackTeam = slackTeam ?: makeSlackTeam(trx = this)

        SlackChannelsIngestionDAO.new(id) {
            this.slackTeam = thisSlackTeam
            this.status = status
            lastFinishedAt?.also { this.lastFinishedAt = lastFinishedAt }
            modifiedAt?.also { this.modifiedAt = modifiedAt }
        }.also {
            debugEnd(_label, "SlackChannelsIngestion", it)
        }
    }

    suspend fun makeSlackThread(
        trx: Transaction? = null,
        _label: String? = null,
        id: SlackThreadId = SlackThreadId.random(),
        org: OrgDAO? = null,
        createdAt: Instant? = null,
        lastMessageCreatedAt: Instant? = null,
        authorOrgMember: OrgMemberDAO? = null,
        associatedThread: ThreadDAO? = null,
        slackTeam: SlackTeamDAO? = null,
        slackChannel: SlackChannelDAO? = null,
        slackThreadTs: String = "123",
        slackThreadUrl: Url = "https://slack.com/thread".asUrl,
    ): SlackThreadDAO = suspendedTransaction(trx) {
        debug(_label, "SlackThread")
        checkSame(slackTeam, slackChannel?.slackTeam) {
            "Multiple slackTeams given"
        }

        val threadOrg = org ?: makeOrg(trx = this)
        val threadAuthorOrgMember = authorOrgMember ?: makeOrgMember(trx = this, org = threadOrg)
        val threadTeam = slackTeam ?: slackChannel?.slackTeam ?: makeSlackTeam(trx = this, org = threadOrg)
        val threadChannel = slackChannel ?: makeSlackChannel(trx = this, slackTeam = threadTeam)

        SlackThreadDAO.new(id) {
            this.createdAt = createdAt ?: Instant.nowWithMicrosecondPrecision()
            this.lastMessageCreatedAt = lastMessageCreatedAt ?: Instant.nowWithMicrosecondPrecision()
            this.author = threadAuthorOrgMember
            this.associatedThread = associatedThread
            this.slackTeam = threadTeam
            this.slackChannel = threadChannel
            this.slackThreadTs = slackThreadTs
            this.slackThreadUrl = slackThreadUrl.asString
        }.also {
            debugEnd(_label, "SlackThread", it)
        }
    }

    suspend fun makePullRequest(
        trx: Transaction? = null,
        _label: String? = null,
        id: PullRequestId = PullRequestId.random(),
        repo: RepoDAO? = null,
        creator: MemberDAO? = null,
        creatorOrgMember: OrgMemberDAO? = null,
        number: Int = Random.nextInt().absoluteValue % 1000000,
        title: String = "pull request title",
        description: ByteArray? = "hi there".asMessageBody().toByteArray(),
        descriptionHash: ByteArray? = makeSha1(),
        descriptionVersion: String? = "1",
        htmlUrl: String = "https://github.com/NextChapterSoftware/unblocked/pull/1",
        state: PullRequestState = PullRequestState.Merged,
        isDraft: Boolean = false,
        mergeCommitSha: String = "340cd625a0058935954a2c7b66cb863b52a1a22f",
        updatedAt: Instant? = null,
        modifiedAt: Instant? = null,
        archivedAt: Instant? = null,
        archivedBy: OrgMemberDAO? = null,
        archivedReason: ArchivedReason? = null,
        summary: String? = null,
        isIngested: Boolean? = null,
        ciTriageMuted: Boolean? = null,
        ciTriageState: BuildTriageState? = null,
        ciTriageCommentId: String? = null,
        ciTriageCommentUrl: Url? = null,
        ciTriageCommentHash: Hash? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "PullRequest")

        val theRepo = repo ?: makeRepo(trx = this)
        val prCreator = creator ?: makeMember(trx = this, scmTeam = theRepo.scmTeam)
        val prCreatorOrgMember = creatorOrgMember ?: prCreator.orgMember

        PullRequestDAO.new(id) {
            this.repo = theRepo
            this.creator = prCreator
            this.creatorOrgMember = prCreatorOrgMember
            this.number = number
            this.title = title
            this.htmlUrl = htmlUrl
            this.state = state
            this.isDraft = isDraft
            this.mergeCommitSha = mergeCommitSha
            this.description = description
            this.descriptionVersion = descriptionVersion
            this.descriptionHash = descriptionHash
            this.summary = summary
            updatedAt?.also { this.updatedAt = it }
            modifiedAt?.also { this.modifiedAt = it }
            archivedAt?.also { this.archivedAt = it }
            archivedBy?.also { this.archivedBy = it }
            archivedReason?.also { this.archivedReason = it }
            isIngested?.also { this.isIngested = it }
            ciTriageMuted?.also { this.ciTriageMuted = it }
            ciTriageState?.also { this.ciTriageState = it }
            ciTriageCommentId?.also { this.ciTriageCommentId = it }
            ciTriageCommentUrl?.also { this.ciTriageCommentUrl = it.asString }
            ciTriageCommentHash?.also { this.ciTriageCommentHash = it.value }
        }.also {
            debugEnd(_label, "PullRequest", it)
        }
    }

    suspend fun makeThreadPullRequest(
        trx: Transaction? = null,
        _label: String? = null,
        id: ThreadPullRequestId = ThreadPullRequestId.random(),
        repo: RepoDAO? = null,
        number: Int = Random.nextInt().absoluteValue,
        thread: ThreadDAO? = null,
        pullRequest: PullRequestDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "ThreadPullRequest")

        val thisTeam = repo?.scmTeam ?: makeScmTeam(trx = this)
        val thisRepo = repo ?: makeRepo(trx = this, scmTeam = thisTeam)
        val thisThread = thread ?: makeThread(trx = this, org = thisTeam.org, repo = thisRepo)

        ThreadPullRequestDAO.new(id) {
            this.repo = thisRepo
            this.number = number
            this.thread = thisThread
            pullRequest?.also { this.pullRequest = it }
        }.also {
            debugEnd(_label, "ThreadPullRequest", it)
        }
    }

    suspend fun makePullRequestComment(
        trx: Transaction? = null,
        _label: String? = null,
        id: PullRequestCommentId = PullRequestCommentId.random(),
        pullRequest: PullRequestDAO,
        author: MemberDAO,
        content: ByteArray = "hi hi".asMessageBody().toByteArray(),
        contentVersion: String = "1",
        isDeleted: Boolean = false,
        prCommentId: String = UUID.randomUUID().toString(),
        prCommentUrl: String = "https://github.com/NextChapterSoftware/unblocked/pull/1#issuecomment-$prCommentId",
        prCommentBodyHash: ByteArray = makeSha1(),
    ) = suspendedTransaction(trx) {
        debug(_label, "PullRequestComment")
        PullRequestCommentDAO.new(id) {
            this.pullRequest = pullRequest
            this.author = author
            this.authorOrgMember = author.orgMember
            this.content = content
            this.contentVersion = contentVersion
            this.isDeleted = isDeleted
            this.prCommentId = prCommentId
            this.prCommentUrl = prCommentUrl
            this.prCommentBodyHash = prCommentBodyHash
        }.also {
            debugEnd(_label, "PullRequestComment", it)
        }
    }

    suspend fun makePullRequestReview(
        trx: Transaction? = null,
        _label: String? = null,
        id: PullRequestReviewId = PullRequestReviewId.random(),
        pullRequest: PullRequestDAO,
        author: MemberDAO,
        content: ByteArray? = null,
        contentVersion: String? = null,
        state: PullRequestReviewState = PullRequestReviewState.Commented,
        prReviewId: String = UUID.randomUUID().toString(),
        prReviewUrl: String = "https://github.com/NextChapterSoftware/unblocked/pull/1#pullrequestreview-$prReviewId",
        prReviewCommentHash: ByteArray? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "PullRequestReview")
        PullRequestReviewDAO.new(id) {
            this.pullRequest = pullRequest
            this.author = author
            this.authorOrgMember = author.orgMember
            this.content = content
            this.contentVersion = contentVersion
            this.state = state
            this.prReviewId = prReviewId
            this.prReviewUrl = prReviewUrl
            this.prReviewCommentHash = prReviewCommentHash
        }.also {
            debugEnd(_label, "PullRequestReview", it)
        }
    }

    suspend fun makeVersionInfo(
        trx: Transaction? = null,
        _label: String? = null,
        id: VersionInfoId = VersionInfoId.random(),
        productSha: ByteArray = makeSha1(),
        productNumber: Int = 12345,
        productVersion: String = "1.0.$productNumber",
        downloadUrl: String = "https://fake.download.url/download",
        downloadChecksum: String = "abcdef12345",
        obsolete: Boolean = false,
        markdownDescription: String = """
            ### Title
            Body
            - List item 1
            - List item 2
            https://link.com
        """.trimIndent(),
        nodeVersion: String = "18.16.0",
        nodeMacARMDownloadUrl: String = "https://fake.download.url/node-mac-arm",
        nodeMacIntelDownloadUrl: String = "https://fake.download.url/node-mac-intel",
        nodeLinuxIntelDownloadUrl: String = "https://fake.download.url/node-linux-intel",
        nodeLinuxARM64DownloadUrl: String = "https://fake.download.url/node-linux-arm64",
        nodeLinuxARMv7lDownloadUrl: String = "https://fake.download.url/node-linux-armv7l",
        nodeWinX64DownloadUrl: String = "https://fake.download.url/node-win-x64",
        nodeWinX86DownloadUrl: String = "https://fake.download.url/node-win-x86",
        nodeWinARM64DownloadUrl: String = "https://fake.download.url/node-win-arm64",
    ) = suspendedTransaction(trx) {
        debug(_label, "VersionInfo")
        VersionInfoDAO.new(id) {
            this.productVersion = productVersion
            this.productSha = productSha
            this.productNumber = productNumber
            this.downloadUrl = downloadUrl
            this.downloadChecksum = downloadChecksum
            this.obsolete = obsolete
            this.markdownDescription = markdownDescription
            this.nodeVersion = nodeVersion
            this.nodeMacARMDownloadUrl = nodeMacARMDownloadUrl
            this.nodeMacIntelDownloadUrl = nodeMacIntelDownloadUrl
            this.nodeLinuxIntelDownloadUrl = nodeLinuxIntelDownloadUrl
            this.nodeLinuxARM64DownloadUrl = nodeLinuxARM64DownloadUrl
            this.nodeLinuxARMv7lDownloadUrl = nodeLinuxARMv7lDownloadUrl
            this.nodeWinX64DownloadUrl = nodeWinX64DownloadUrl
            this.nodeWinX86DownloadUrl = nodeWinX86DownloadUrl
            this.nodeWinARM64DownloadUrl = nodeWinARM64DownloadUrl
        }.also {
            debugEnd(_label, "VersionInfo", it)
        }
    }

    suspend fun makeReleasedVersion(
        trx: Transaction? = null,
        _label: String? = null,
        id: ReleasedVersionId = ReleasedVersionId.random(),
        version: VersionInfoDAO? = null,
        releaseChannel: ReleaseChannel = ReleaseChannel.NotReleased,
    ) = suspendedTransaction(trx) {
        debug(_label, "ReleasedVersion")

        val versionInfo = version ?: makeVersionInfo(trx = this)

        ReleasedVersionDAO.new(id) {
            this.version = versionInfo
            this.releaseChannel = releaseChannel
        }.also {
            debugEnd(_label, "ReleasedVersion", it)
        }
    }

    suspend fun makeTopic(
        trx: Transaction? = null,
        _label: String? = null,
        id: TopicId = TopicId.random(),
        repo: RepoDAO? = null,
        name: String = "Topic $id",
        score: Int = 100,
        relevant: Float? = null,
        description: String? = null,
        topicSource: TopicSourceType? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "Topic")

        val topicTeam = repo?.scmTeam ?: makeScmTeam(trx = this)
        val topicRepo = repo ?: makeRepo(trx = this, scmTeam = topicTeam)

        TopicDAO.new(id) {
            this.name = name
            this.repo = topicRepo
            this.score = score
            this.relevant = relevant
            this.description = description
            topicSource?.also { this.topicSource = it }
        }.also {
            debugEnd(_label, "Topic", it)
        }
    }

    suspend fun makeTopicExpert(
        trx: Transaction? = null,
        _label: String? = null,
        id: TopicExpertId = TopicExpertId.random(),
        topic: TopicDAO? = null,
        member: MemberDAO? = null,
        activityCount: Int? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "TopicExpert")

        val thisTopic = topic ?: makeTopic(trx = this)
        val thisMember = member ?: makeMember(trx = this, scmTeam = thisTopic.repo.scmTeam)

        TopicExpertDAO.new(id) {
            this.topic = thisTopic
            this.orgMember = thisMember.orgMember
            this.member = thisMember
            this.activityCount = activityCount
        }.also {
            debugEnd(_label, "TopicExpert", it)
        }
    }

    suspend fun makeTopicInsight(
        trx: Transaction? = null,
        _label: String? = null,
        id: TopicInsightId = TopicInsightId.random(),
        topic: TopicDAO,
        insight: UUID,
    ) = suspendedTransaction(trx) {
        debug(_label, "TopicInsight")
        TopicInsightDAO.new(id) {
            this.topic = topic
            this.insight = insight
        }.also {
            debugEnd(_label, "TopicInsight", it)
        }
    }

    suspend fun makeTopicInsight(
        trx: Transaction? = null,
        _label: String? = null,
        id: TopicInsightId = TopicInsightId.random(),
        insightId: UUID,
        topic: TopicDAO,
    ) = suspendedTransaction(trx) {
        debug(_label, "TopicInsight")
        TopicInsightDAO.new(id) {
            this.insight = insightId
            this.topic = topic
        }.also {
            debugEnd(_label, "TopicInsight", it)
        }
    }

    suspend fun makeUserEngagement(
        trx: Transaction? = null,
        _label: String? = null,
        org: OrgDAO? = null,
        orgMember: OrgMemberDAO? = null,
        person: PersonDAO? = null,
        createPerson: Boolean = true,
        member: MemberDAO? = null,
        productAgent: ProductAgentType = ProductAgentType.VSCode,
        activityType: ActivityType = ActivityType.ThreadViewed,
        createdAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "UserEngagement")
        checkSame(org, orgMember?.org, member?.installation?.org) {
            "Multiple orgs given"
        }
        checkSame(orgMember, member?.orgMember) {
            "Multiple orgMembers given"
        }
        checkSame(person, orgMember?.person, member?.orgMember?.person) {
            "Multiple persons given"
        }
        checkCreate(person, createPerson) { "Unexpected person given when createPerson=false" }

        val theOrg = org
            ?: orgMember?.org
            ?: member?.installation?.org
            ?: makeOrg(trx = this)
        val thePerson = person
            ?: createIf(createPerson) { makePerson(trx = this) }
        val theOrgMember = orgMember ?: member?.orgMember
        ?: makeOrgMember(trx = this, org = theOrg, person = thePerson, createPerson = createPerson)

        UserEngagementDAO.new {
            this.personId = thePerson?.idValue
            this.orgId = theOrg.idValue
            this.orgMemberId = theOrgMember.idValue
            this.productAgent = productAgent
            this.activityType = activityType
            this.createdAt = createdAt ?: Instant.nowWithMicrosecondPrecision()
        }.also {
            debugEnd(
                _label, "UserEngagement", it,
                mapOf(
                    "personId" to thePerson?.idValue,
                    "orgId" to theOrg.idValue,
                    "orgMemberId" to theOrgMember.idValue,
                ),
            )
        }
    }

    suspend fun makeUserEngagementMetrics(
        trx: Transaction? = null,
        _label: String? = null,
        dailyActiveUsers: Long = 5,
        weeklyActiveUsers: Long = 3,
        monthlyActiveUsers: Long = 9,
        dailyActiveTeams: Long = 5,
        weeklyActiveTeams: Long = 3,
        monthlyActiveTeams: Long = 9,
        total: Long = 10,
        ending: Instant,
        cohort: Cohort? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "UserEngagementMetrics")
        UserEngagementMetricsDAO.new {
            this.dailyActiveUsers = dailyActiveUsers
            this.weeklyActiveUsers = weeklyActiveUsers
            this.monthlyActiveUsers = monthlyActiveUsers
            this.dailyActiveTeams = dailyActiveTeams
            this.weeklyActiveTeams = weeklyActiveTeams
            this.monthlyActiveTeams = monthlyActiveTeams
            this.totalUsers = total
            this.ending = ending
            this.cohort = cohort ?: Cohort.All
        }.also {
            debugEnd(_label, "UserEngagementMetrics", it)
        }
    }

    suspend fun makeClientCapability(
        _label: String? = null,
        value: Boolean,
        key: ClientCapabilityType? = null,
        person: PersonDAO? = null,
        org: OrgDAO? = null,
    ) = suspendedTransaction {
        debug(_label, "ClientCapability")
        ClientCapabilityDAO.new {
            this.key = key ?: ClientCapabilityType.FeatureSlackIntegration
            this.value = value
            person?.also { this.person = person }
            org?.also { this.org = it }
        }.also {
            debugEnd(_label, "ClientCapability", it)
        }
    }

    suspend fun makeClientQuantity(
        _label: String? = null,
        value: Int,
        key: ClientQuantityType? = null,
        person: PersonDAO? = null,
        org: OrgDAO? = null,
    ) = suspendedTransaction {
        debug(_label, "ClientQuantity")
        ClientQuantityDAO.new {
            this.key = key ?: ClientQuantityType.SourceMarkUpstreamBatchSize
            this.value = value
            person?.also { this.person = person }
            org?.also { this.org = it }
        }.also {
            debugEnd(_label, "ClientQuantity", it)
        }
    }

    suspend fun makeGitHubEnterpriseAppConfig(
        trx: Transaction? = null,
        _label: String? = null,
        org: OrgDAO? = null,
        provider: Provider? = null,
        hostAndPort: String? = null,
        externalAppId: String? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "GitHubEnterpriseAppConfig")
        EnterpriseAppConfigDAO.new {
            this.org = org
            this.provider = provider ?: Provider.GitHubEnterprise
            this.hostAndPort = hostAndPort ?: "git.acme.com:8443"
            this.externalAppId = externalAppId ?: "123"
            this.slug = "unblocked"
            this.owner = "Acme"
            this.appHtmlUrl = "https://$hostAndPort/github-apps/unblocked"
            this.oauthClientId = "Iv1.**********"
            this.oauthClientSecretEncrypted = "12345".encodeToByteArray()
            this.webhookSecretEncrypted = "67890".encodeToByteArray()
            this.privateKeyPemEncrypted = "qwertyui".encodeToByteArray()
        }.also {
            debugEnd(_label, "GitHubEnterpriseAppConfig", it)
        }
    }

    suspend fun makeGitLabEnterpriseAppConfig(
        _label: String? = null,
        org: OrgDAO? = null,
        hostAndPort: String? = null,
        externalAppId: String? = null,
    ) = suspendedTransaction {
        debug(_label, "GitLabEnterpriseAppConfig")
        EnterpriseAppConfigDAO.new {
            this.org = org
            this.provider = Provider.GitLabSelfHosted
            this.hostAndPort = hostAndPort ?: "git.acme.com:8443"
            this.externalAppId = externalAppId ?: "123"
            this.oauthClientId = "Iv1.**********"
            this.oauthClientSecretEncrypted = "12345".encodeToByteArray()
        }.also {
            debugEnd(_label, "GitLabEnterpriseAppConfig", it)
        }
    }

    suspend fun makeGitHubAppRequest(
        trx: Transaction? = null,
        _label: String? = null,
        id: GitHubAppRequestId = GitHubAppRequestId.random(),
        requester: IdentityDAO? = null,
        provider: Provider = Provider.GitHub,
        providerEnterprise: EnterpriseAppConfigDAO? = null,
        providerExternalId: String = "123456789",
        providerLogin: String = "test-org",
        providerDisplayName: String? = "Test Organization",
        providerAvatarUrl: String = "https://avatars.githubusercontent.com/u/123456789?v=4",
        providerHtmlUrl: String = "https://github.com/test-org",
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "GitHubAppRequest")

        val theRequester = requester ?: makeIdentity(trx = this, provider = provider)
        val providerUniqueSignature = providerEnterprise?.id?.value?.toString() ?: provider.uniqueSignature

        GitHubAppRequestDAO.new(id) {
            this.requester = theRequester
            this.provider = provider
            this.providerEnterprise = providerEnterprise
            this.providerUniqueSignature = providerUniqueSignature
            this.providerExternalId = providerExternalId
            this.providerLogin = providerLogin
            this.providerDisplayName = providerDisplayName
            this.providerAvatarUrl = providerAvatarUrl
            this.providerHtmlUrl = providerHtmlUrl
            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
        }.also {
            debugEnd(_label, "GitHubAppRequest", it)
        }
    }

    suspend fun makeJiraSite(
        trx: Transaction? = null,
        _label: String? = null,
        id: JiraSiteId = JiraSiteId.random(),
        installation: InstallationDAO,
        siteId: String = UUID.randomUUID().toString(),
        baseUrl: String = "https://acme.atlassian.net",
        avatarUrl: String = "https://acme.atlassian.net",
        clientKey: String? = null,
        jiraProjectIngestionType: JiraProjectIngestionType? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "JiraSite")
        JiraSiteDAO.new(id) {
            this.installation = installation
            this.siteId = siteId
            this.baseUrl = baseUrl
            this.clientKey = clientKey
            this.jiraProjectIngestionType = jiraProjectIngestionType
            this.avatarUrl = avatarUrl
        }.also {
            debugEnd(_label, "JiraSite", it)
        }
    }

    suspend fun makeJiraProject(
        trx: Transaction? = null,
        _label: String? = null,
        id: JiraProjectId = JiraProjectId.random(),
        site: JiraSiteDAO,
        projectId: String = UUID.randomUUID().toString(),
        projectName: String = "Project Name",
        shouldIngest: Boolean = false,
        permissionScheme: JiraPermissionSchemeDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "JiraProject")
        JiraProjectDAO.new(id) {
            this.jiraSite = site
            this.projectId = projectId
            this.projectName = projectName
            this.shouldIngest = shouldIngest
            this.permissionScheme = permissionScheme
        }.also {
            debugEnd(_label, "JiraProject", it)
        }
    }

    suspend fun makeJiraBoard(
        trx: Transaction? = null,
        _label: String? = null,
        id: JiraBoardId = JiraBoardId.random(),
        installation: InstallationDAO,
        project: JiraProjectDAO,
        boardId: String = UUID.randomUUID().toString(),
        name: String = "Board Name",
        type: String = "scrum",
        isPrivate: Boolean? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "JiraBoard")
        JiraBoardDAO.new(id) {
            this.installation = installation
            this.project = project
            this.boardId = boardId
            this.name = name
            this.type = type
            this.isPrivate = isPrivate
        }.also {
            debugEnd(_label, "JiraBoard", it)
        }
    }

    suspend fun makeJiraGroup(
        trx: Transaction? = null,
        _label: String? = null,
        id: JiraGroupId = JiraGroupId.random(),
        site: JiraSiteDAO,
        groupId: String = UUID.randomUUID().toString(),
    ) = suspendedTransaction(trx) {
        debug(_label, "JiraGroup")
        JiraGroupDAO.new(id) {
            this.jiraSite = site
            this.groupId = groupId
        }.also {
            debugEnd(_label, "JiraGroup", it)
        }
    }

    suspend fun makeConfluenceSite(
        trx: Transaction? = null,
        _label: String? = null,
        id: ConfluenceSiteId = ConfluenceSiteId.random(),
        installation: InstallationDAO,
        siteId: String = UUID.randomUUID().toString(),
        baseUrl: String = "https://$siteId.atlassian.net",
        avatarUrl: String = "https://$siteId.atlassian.net",
        name: String = "Confluence Site",
        confluenceSpaceIngestionType: ConfluenceSpaceIngestionType = ConfluenceSpaceIngestionType.AllSpaces,
    ) = suspendedTransaction(trx) {
        debug(_label, "ConfluenceSite")
        ConfluenceSiteDAO.new(id) {
            this.installation = installation
            this.siteId = siteId
            this.baseUrl = baseUrl
            this.avatarUrl = avatarUrl
            this.name = name
            this.confluenceSpaceIngestionType = confluenceSpaceIngestionType
        }.also {
            debugEnd(_label, "ConfluenceSite", it)
        }
    }

    suspend fun makeConfluenceSpace(
        trx: Transaction? = null,
        _label: String? = null,
        id: ConfluenceSpaceId = ConfluenceSpaceId.random(),
        site: ConfluenceSiteDAO,
        spaceId: String = UUID.randomUUID().toString(),
        spaceName: String = "Space $spaceId",
        shouldIngest: Boolean = false,
    ) = suspendedTransaction(trx) {
        debug(_label, "ConfluenceSpace")
        ConfluenceSpaceDAO.new(id) {
            this.confluenceSite = site
            this.spaceId = spaceId
            this.spaceName = spaceName
            this.shouldIngest = shouldIngest
        }.also {
            debugEnd(_label, "ConfluenceSpace", it)
        }
    }

    suspend fun makeConfluenceGroup(
        trx: Transaction? = null,
        _label: String? = null,
        id: ConfluenceGroupId = ConfluenceGroupId.random(),
        site: ConfluenceSiteDAO,
        groupId: String = UUID.randomUUID().toString(),
    ) = suspendedTransaction(trx) {
        debug(_label, "ConfluenceGroup")
        ConfluenceGroupDAO.new(id) {
            this.confluenceSite = site
            this.groupId = groupId
        }.also {
            debugEnd(_label, "ConfluenceGroup", it)
        }
    }

    suspend fun makeLinearOrganization(
        trx: Transaction? = null,
        _label: String? = null,
        id: LinearOrganizationId = LinearOrganizationId.random(),
        installation: InstallationDAO,
        linearOrganizationId: String = UUID.randomUUID().toString(),
        htmlUrl: String = "https://linear.app/app",
        avatarUrl: String? = "https://linear.app/app",
        name: String = "Linear Organization",
        linearTeamIngestionType: LinearTeamIngestionType = LinearTeamIngestionType.AllTeams,
    ) = suspendedTransaction(trx) {
        debug(_label, "LinearOrganization")
        LinearOrganizationDAO.new(id) {
            this.installation = installation
            this.linearOrganizationId = linearOrganizationId
            this.name = name
            this.avatarUrl = avatarUrl
            this.htmlUrl = htmlUrl
            this.linearTeamIngestionType = linearTeamIngestionType
        }.also {
            debugEnd(_label, "LinearOrganization", it)
        }
    }

    suspend fun makeLinearTeam(
        trx: Transaction? = null,
        _label: String? = null,
        id: LinearTeamId = LinearTeamId.random(),
        linearOrganization: LinearOrganizationDAO,
        linearTeamId: String = UUID.randomUUID().toString(),
        linearTeamName: String = UUID.randomUUID().toString(),
    ) = suspendedTransaction(trx) {
        debug(_label, "LinearTeam")
        LinearTeamDAO.new(id) {
            this.linearOrganization = linearOrganization
            this.linearTeamId = linearTeamId
            this.name = linearTeamName
        }.also {
            debugEnd(_label, "LinearTeam", it)
        }
    }

    suspend fun makeInstallation(
        trx: Transaction? = null,
        _label: String? = null,
        id: InstallationId = InstallationId.random(),
        org: OrgDAO? = null,
        provider: Provider = Provider.Slack,
        displayName: String? = null,
        installationExternalId: String = UUID.randomUUID().toString(),
        rawAccessToken: ByteArray? = null,
        deletedInstallationExternalId: String? = null,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
        dsacEnabled: Boolean? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "Installation")

        val installationOrg = org ?: makeOrg(trx = this)

        InstallationDAO.new(id) {
            this.org = installationOrg
            this.provider = provider
            this.displayName = displayName
            this.installationExternalId = installationExternalId
            this.rawAccessToken = rawAccessToken
            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
            deletedInstallationExternalId?.also { this.deletedInstallationExternalId = it }
            dsacEnabled?.also { this.dsacEnabled = it }
        }.also {
            debugEnd(
                _label, "Installation", it,
                mapOf(
                    "provider" to provider,
                ),
            )
        }
    }

    suspend fun makeInstallationBot(
        trx: Transaction? = null,
        _label: String? = null,
        id: InstallationBotId = InstallationBotId.random(),
        installation: InstallationDAO? = null,
        identity: IdentityDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "InstallationBot")

        val installation = installation ?: makeInstallation(trx = this)
        val identity = identity ?: makeIdentity(trx = this, provider = installation.provider)

        checkSame(installation.provider, identity.provider) { "Multiple providers detected" }

        InstallationBotDAO.new(id) {
            this.installation = installation
            this.identity = identity
        }.also {
            debugEnd(_label, "InstallationBot", it)
        }
    }

    suspend fun makeCIWebhookEvent(
        trx: Transaction? = null,
        _label: String? = null,
        ciInstallation: InstallationDAO? = null,
        label: String = randomStrings.nextAscii(5),
        isSignatureValid: Boolean = true,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "CIWebhookEvent")

        val ciInstallation = ciInstallation ?: makeInstallation(trx = this)

        CIWebhookEventDAO.new {
            this.ciInstallation = ciInstallation
            this.label = label
            this.isSignatureValid = isSignatureValid
            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
        }.also {
            debugEnd(
                _label, "CIWebhookEvent", it,
                mapOf(
                    "label" to label,
                ),
            )
        }
    }

    suspend fun makeIngestion(
        trx: Transaction? = null,
        _label: String? = null,
        id: IngestionId = IngestionId.random(),
        provider: Provider = Provider.Slack,
        installation: InstallationDAO,
        initialIngestionCompletedAt: Instant? = null,
        lastSynced: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "Ingestion")
        checkSame(provider, installation.provider) {
            "Multiple providers given"
        }
        IngestionDAO.new(id) {
            this.provider = provider
            this.installation = installation
            this.initialIngestionCompletedAt = initialIngestionCompletedAt
            this.lastSynced = lastSynced
        }.also {
            debugEnd(
                _label, "Ingestion", it,
                mapOf(
                    "provider" to provider,
                ),
            )
        }
    }

    suspend fun makeSlackUserConnectPromptHistory(
        trx: Transaction? = null,
        _label: String? = null,
        id: SlackUserConnectPromptHistoryId = SlackUserConnectPromptHistoryId.random(),
        createdAt: Instant? = null,
        org: OrgDAO? = null,
        member: MemberDAO? = null,
        slackTeam: SlackTeamDAO? = null,
        slackMemberExternalId: String = UUID.randomUUID().toString(),
        trigger: SlackUserConnectTrigger = SlackUserConnectTrigger.AT_MENTION,
    ) = suspendedTransaction(trx) {
        debug(_label, "SlackUserConnectPromptHistory")

        val thisOrg = org ?: makeOrg(trx = this)
        val thisMember = member ?: makeMember(trx = this, org = org)
        val thisSlackTeam = slackTeam ?: makeSlackTeam(trx = this, org = org)

        SlackUserConnectPromptHistoryDAO.new(id) {
            this.org = thisOrg
            this.slackTeam = thisSlackTeam
            this.member = thisMember
            this.trigger = trigger
            this.slackMemberExternalId = slackMemberExternalId
            createdAt?.let { this.createdAt = it }
        }.also {
            debugEnd(_label, "SlackUserConnectPromptHistory", it)
        }
    }

    suspend fun makeSlackAutoAnswer(
        trx: Transaction? = null,
        _label: String? = null,
        mlInference: MLInferenceDAO,
    ) = suspendedTransaction(trx) {
        val channel = makeSlackChannel(trx = this)

        SlackAutoAnswerDAO.new {
            this.slackMessageTs = UUID.randomUUID().toString()
            this.slackChannel = channel
            this.inference = mlInference
            this.filterStage = SlackAutoAnswerFilterStage.Answered
        }.also {
            debugEnd(_label, "SlackAutoAnswerModel", it)
        }
    }

    suspend fun makeMLInference(
        trx: Transaction? = null,
        _label: String? = null,
        id: MLInferenceId = MLInferenceId.random(),
        createdAt: Instant? = null,
        org: OrgDAO? = null,
        botMessage: MessageDAO? = null,
        questionerOrgMember: OrgMemberDAO? = null,
        query: String = "blah",
        documentQuery: String = "blah",
        response: String = "blah",
        rawResponse: String = "blah",
        sentiment: Int? = null,
        humanFeedback: String? = null,
        type: MLInferenceType = MLInferenceType.Unlabeled,
        category: MLInferenceCategory = MLInferenceCategory.Search,
        isSuggestion: Boolean = false,
        isGlobalRegressionInference: Boolean = false,
        runDuration: Duration? = null,
        executionTrace: String? = null,
        messageFirstGeneratedAt: Instant? = null,
        // result fields
        prompt: String? = null,
        template: MLInferenceTemplateDAO? = null,
        createTemplate: Boolean? = null,
        documents: List<MLTypedDocument>? = null,
        references: List<ReferenceArtifact>? = null,
        configuration: MLInferenceRuntimeConfigurationDAO? = null,
        createConfiguration: Boolean? = null,
        runtimeConfigType: MLInferenceRuntimeConfigurationType? = null,
        productAgent: ProductAgentType? = null,
        ragSkipped: Boolean? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "MLInference")
        checkCreate(template, createTemplate) { "Unexpected template given when createTemplate=false" }
        checkCreate(configuration, createConfiguration) { "Unexpected configuration given when createConfiguration=false" }

        val inferenceOrg = org ?: makeOrg(trx = this)
        val inferenceTemplate = template ?: createIf(createTemplate) {
            makeMLInferenceTemplate(trx = this)
        }
        val inferenceConfiguration = configuration ?: createIf(createConfiguration) {
            makeMLRuntimeConfiguration(trx = this, org = org)
        }

        MLInferenceDAO.new(id) {
            this.org = inferenceOrg
            this.botMessage = botMessage
            this.questionerOrgMember = questionerOrgMember
            this.query = query
            this.documentQuery = documentQuery
            this.response = response
            this.rawResponse = rawResponse.compress()
            this.sentiment = sentiment
            this.humanFeedback = humanFeedback
            this.type = type
            this.category = category
            this.isSuggestion = isSuggestion
            this.isGlobalRegressionInference = isGlobalRegressionInference
            this.runDurationMillis = runDuration?.inWholeMilliseconds
            this.executionTrace = executionTrace?.compress()
            this.messageFirstGeneratedAt = messageFirstGeneratedAt
            createdAt?.let { this.createdAt = it }
            // result fields
            this.prompt = prompt?.toByteArray()
            this.template = inferenceTemplate
            this.documents = documents?.encode()?.compress()
            this.references = references?.encode()
            this.configuration = inferenceConfiguration
            this.runtimeConfigType = runtimeConfigType
            this.productAgent = productAgent
            this.ragSkipped = ragSkipped
        }.also {
            debugEnd(_label, "MLInference", it)
        }
    }

    suspend fun makeMcpInference(
        trx: Transaction? = null,
        _label: String? = null,
        id: McpInferenceId = McpInferenceId.random(),
        org: OrgDAO? = null,
        questionerOrgMember: OrgMemberDAO? = null,
        mlInference: MLInferenceDAO? = null,
        createdAt: Instant? = null,
        productAgent: ProductAgentType? = null,
        state: McpInferenceState? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "McpInference")

        val thisOrg = org ?: mlInference?.org ?: makeOrg(trx = this)
        val thisQuestionerOrgMember = questionerOrgMember ?: makeOrgMember(trx = trx, org = thisOrg)
        val thisRepo = makeRepo(trx = trx)

        McpInferenceDAO.new(id) {
            createdAt?.let { this.createdAt = it }
            this.org = thisOrg
            this.query = "hi there"
            this.mcpQueryId = UUID.randomUUID()
            this.productAgent = productAgent
            this.questionerOrgMember = thisQuestionerOrgMember
            this.resultInference = mlInference
            this.repo = thisRepo
            this.state = state
        }.also {
            debugEnd(_label, "MLInference", it)
        }
    }

    suspend fun makeAnswerMetric(
        trx: Transaction? = null,
        _label: String? = null,
        id: AnswerMetricId = AnswerMetricId.random(),
        createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
        modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
        org: OrgDAO? = null,
        humanOrgMember: OrgMemberDAO? = null,
        humanPerson: PersonDAO? = null,
        inference: MLInferenceDAO? = null,
        feedbackType: FeedbackType = FeedbackType.None,
        productAgent: ProductAgentType = ProductAgentType.Desktop,
        isSlackAutoAnswer: Boolean = false,
        isSuggestion: Boolean = false,
        runDurationMillis: Long = 100,
        waitDurationMillis: Long = 100,
    ) = suspendedTransaction(trx) {
        debug(_label, "AnswerMetric")

        val metricOrg = org ?: makeOrg(trx = this)
        val metricHumanOrgMember = humanOrgMember ?: makeOrgMember(trx = this, org = metricOrg)
        val metricHumanPerson = humanPerson ?: metricHumanOrgMember.person
        val metricInference = inference ?: makeMLInference(trx = this, org = metricOrg, questionerOrgMember = metricHumanOrgMember)

        AnswerMetricDAO.new(id) {
            this.createdAt = createdAt
            this.modifiedAt = modifiedAt
            this.org = metricOrg.idValue
            this.humanOrgMember = metricHumanOrgMember.idValue
            this.humanPerson = metricHumanPerson?.idValue
            this.inference = metricInference.idValue
            this.feedbackType = feedbackType
            this.productAgent = productAgent
            this.isSlackAutoAnswer = isSlackAutoAnswer
            this.isSuggestion = isSuggestion
            this.runDurationMillis = runDurationMillis
            this.waitDurationMillis = waitDurationMillis
        }.also {
            debugEnd(_label, "AnswerMetric", it)
        }
    }

    suspend fun makeMLInferenceTemplate(
        trx: Transaction? = null,
        _label: String? = null,
        id: MLInferenceTemplateId = MLInferenceTemplateId.random(),
        answerPromptTemplate: String? = "",
        codeSectionPromptTemplate: String = "",
        conversationMessageTemplate: String = "",
        conversationTemplate: String = "",
        documentRelevancyEvaluationMaxDepth: Int = 1,
        documentRelevancyEvaluationType: MLDocumentRelevancyEvaluationType = MLDocumentRelevancyEvaluationType.EvaluateAgainstGeneratedQuery,
        documentPromptTemplate: String = "",
        documentSectionPromptTemplate: String = "",
        documentTypes: Set<DocumentType>? = MLInferenceTemplateDAO.DEFAULT_DOCUMENT_TYPES,
        enableExperimentalFunctions: Boolean = false,
        examplePromptTemplate: String = "",
        fallbackTemplate: MLInferenceTemplateDAO? = null,
        functionTemplate: String = "",
        inferenceEngine: MLInferenceEngine = MLInferenceEngine.GPT4OmniMini,
        insightTypes: Set<InsightType>? = MLInferenceTemplateDAO.DEFAULT_INSIGHT_TYPES,
        isDeleted: Boolean = false,
        isGlobalDefault: Boolean = false,
        isGlobalDemo: Boolean = false,
        issueSectionPromptTemplate: String = "",
        jsonOutputFormatInstructions: String = "",
        localContextRetrievalWeight: Float = 1.0f,
        maxDocuments: Int = 10,
        maxExamples: Int = 100,
        maxLocalDocuments: Int = 5,
        maxPromptLength: Int = 2000,
        maxRerankDocs: Int = 100,
        minRankScore: Float = 0.5f,
        name: String = "blah",
        numGeneratedSampleQuestions: Int = 10,
        parallelizeMLFunctionsByClass: Boolean = false,
        prefixPromptTemplate: String = "",
        promptTemplate: String = "",
        pullRequestSectionPromptTemplate: String = "",
        queryPromptTemplate: String = "",
        questionerPromptTemplate: String = "",
        recomposeDocumentPartitions: Boolean = false,
        referenceResolverType: MLReferenceResolverType? = MLReferenceResolverType.LLM,
        repoTemplate: String? = "",
        rerankModel: MLRerankModel = MLRerankModel.CohereRerankV35,
        slackSectionPromptTemplate: String = "",
        sourceTypes: Set<Provider>? = MLInferenceTemplateDAO.DEFAULT_SOURCE_TYPES,
        sparseVectorWeight: Float = 0.5f,
        suffixPromptTemplate: String = "",
        memberPromptTemplate: String = "",
        templateKind: MLInferenceTemplateKind = MLInferenceTemplateKind.Search,
        temperature: Float = 0.01f,
        topicsPromptTemplate: String = "",
        useAgenticRetrieval: Boolean = false,
        useCERR: Boolean = false,
        useCommitsAsSeedDataForSampleQuestionGeneration: Boolean = true,
        useDiffsAsSeedDataForSampleQuestionGeneration: Boolean = true,
        useHighlightedCodeCompressor: Boolean = false,
        useMLFunctions: Boolean = false,
        usePRsAsSeedDataForSampleQuestionGeneration: Boolean = true,
        useRRF: Boolean = false,
        useSeedDataOnlyForSampleQuestionGeneration: Boolean = false,
        userLocalActiveContextTemplate: String = "",
        zeroDocsTemplate: String = "",
    ) = suspendedTransaction(trx) {
        debug(_label, "MLInferenceTemplate")
        MLInferenceTemplateDAO.new(id) {
            this.answerPromptTemplate = answerPromptTemplate
            this.codeSectionPromptTemplate = codeSectionPromptTemplate
            this.conversationMessageTemplate = conversationMessageTemplate
            this.conversationTemplate = conversationTemplate
            this.documentRelevancyEvaluationMaxDepth = documentRelevancyEvaluationMaxDepth
            this.documentRelevancyEvaluationType = documentRelevancyEvaluationType
            this.documentPromptTemplate = documentPromptTemplate
            this.documentSectionPromptTemplate = documentSectionPromptTemplate
            this.documentTypeSet = documentTypes?.encode()
            this.enableExperimentalFunctions = enableExperimentalFunctions
            this.examplePromptTemplate = examplePromptTemplate
            this.fallbackTemplate = fallbackTemplate
            this.functionTemplate = functionTemplate
            this.inferenceEngine = inferenceEngine
            this.insightTypeSet = insightTypes?.encode()
            this.isDeleted = isDeleted
            this.isGlobalDefault = isGlobalDefault
            this.isGlobalDemo = isGlobalDemo
            this.issueSectionPromptTemplate = issueSectionPromptTemplate
            this.jsonOutputFormatInstructions = jsonOutputFormatInstructions
            this.localContextRetrievalWeight = localContextRetrievalWeight
            this.maxDocuments = maxDocuments
            this.maxExamples = maxExamples
            this.maxLocalDocuments = maxLocalDocuments
            this.maxPromptLength = maxPromptLength
            this.maxRerankDocs = maxRerankDocs
            this.minRankScore = minRankScore
            this.name = name
            this.numGeneratedSampleQuestions = numGeneratedSampleQuestions
            this.parallelizeMLFunctionsByClass = parallelizeMLFunctionsByClass
            this.prefixPromptTemplate = prefixPromptTemplate
            this.promptTemplate = promptTemplate
            this.pullRequestSectionPromptTemplate = pullRequestSectionPromptTemplate
            this.queryPromptTemplate = queryPromptTemplate
            this.questionerPromptTemplate = questionerPromptTemplate
            this.recomposeDocumentPartitions = recomposeDocumentPartitions
            this.referenceResolverType = referenceResolverType
            this.repoTemplate = repoTemplate
            this.rerankModel = rerankModel
            this.slackSectionPromptTemplate = slackSectionPromptTemplate
            this.sourceTypeSet = sourceTypes?.encode()
            this.sparseVectorWeight = sparseVectorWeight
            this.suffixPromptTemplate = suffixPromptTemplate
            this.memberPromptTemplate = memberPromptTemplate
            this.templateKind = templateKind
            this.temperature = temperature
            this.topicsPromptTemplate = topicsPromptTemplate
            this.useAgenticRetrieval = useAgenticRetrieval
            this.useCERR = useCERR
            this.useCommitsAsSeedDataForSampleQuestionGeneration = useCommitsAsSeedDataForSampleQuestionGeneration
            this.useDiffsAsSeedDataForSampleQuestionGeneration = useDiffsAsSeedDataForSampleQuestionGeneration
            this.useHighlightedCodeCompressor = useHighlightedCodeCompressor
            this.useMLFunctions = useMLFunctions
            this.usePRsAsSeedDataForSampleQuestionGeneration = usePRsAsSeedDataForSampleQuestionGeneration
            this.useRRF = useRRF
            this.useSeedDataOnlyForSampleQuestionGeneration = useSeedDataOnlyForSampleQuestionGeneration
            this.userLocalActiveContextTemplate = userLocalActiveContextTemplate
            this.zeroDocsTemplate = zeroDocsTemplate
        }.also {
            debugEnd(_label, "MLInferenceTemplate", it)
        }
    }

    suspend fun makeMLRuntimeConfiguration(
        trx: Transaction? = null,
        _label: String? = null,
        org: OrgDAO? = null,
        template: MLInferenceTemplateDAO? = null,
        type: MLInferenceRuntimeConfigurationType = MLInferenceRuntimeConfigurationType.ProductQuery,
        validationRecord: List<UUID>? = null,
        goldenRecord: List<UUID> = emptyList(),
        testRunName: String? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "MLRuntimeConfiguration")

        val inferenceOrg = org ?: makeOrg(trx = this)
        val inferenceTemplate = template ?: makeMLInferenceTemplate(trx = this)

        MLInferenceRuntimeConfigurationDAO.new {
            this.org = inferenceOrg
            this.template = inferenceTemplate
            this.type = type
            this.validationRecord = validationRecord?.encode()
            this.goldenRecord = goldenRecord.encode()
            this.testRunName = testRunName
        }.also {
            debugEnd(_label, "MLRuntimeConfiguration", it)
        }
    }

    suspend fun makeMLSettings(
        trx: Transaction? = null,
        _label: String? = null,
        id: MLSettingsId = MLSettingsId.random(),
        org: OrgDAO? = null,
        createdAt: Instant? = null,
        enableExperts: Boolean? = null,
        readEmbeddingModel: EmbeddingModel? = null,
        writeEmbeddingModels: List<EmbeddingModel>? = null,
        searchTemplate: MLInferenceTemplateDAO? = null,
        expertSummaryTemplate: MLInferenceTemplateDAO? = null,
        fileSearchTemplate: MLInferenceTemplateDAO? = null,
        isQuestionTemplate: MLInferenceTemplateDAO? = null,
        isQuestionAnswerTemplate: MLInferenceTemplateDAO? = null,
        chatCompressorTemplate: MLInferenceTemplateDAO? = null,
        highlightedCodeCompressorTemplate: MLInferenceTemplateDAO? = null,
        inlineReferencesResolverTemplate: MLInferenceTemplateDAO? = null,
        topicExtractionTemplate: MLInferenceTemplateDAO? = null,
        followupSuggestionsTemplate: MLInferenceTemplateDAO? = null,
        functionsTemplate: MLInferenceTemplateDAO? = null,
        repoExtractionTemplate: MLInferenceTemplateDAO? = null,
        maliciousQueryDetectionTemplate: MLInferenceTemplateDAO? = null,
        sampleQuestionsGeneratorTemplate: MLInferenceTemplateDAO? = null,
        shouldBotRespondTemplate: MLInferenceTemplateDAO? = null,
        codeAssistTemplate: MLInferenceTemplateDAO? = null,
        tickleResponderTemplate: MLInferenceTemplateDAO? = null,
        documentRelevanceTemplate: MLInferenceTemplateDAO? = null,
        documentSelectorTemplate: MLInferenceTemplateDAO? = null,
        topicSource: TopicSourceType? = null,
        topicMappingSource: TopicMappingSourceType? = null,
        topicMappingAlpha: Float? = null,
        topicMappingTopK: Int? = null,
        topicMappingScoreCutoff: Float? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "MLSettings")

        val scmTeamOrg = org ?: makeOrg(trx)

        MLSettingsDAO.new(id) {
            this.org = scmTeamOrg
            createdAt?.also { this.createdAt = it }
            enableExperts?.also { this.enableExperts = it }
            readEmbeddingModel?.also { this.readEmbeddingModel = it }
            writeEmbeddingModels?.also { this.writeEmbeddingModels = it.encode() }
            searchTemplate?.also { this.searchTemplate = it }
            expertSummaryTemplate?.also { this.expertSummaryTemplate = it }
            fileSearchTemplate?.also { this.fileSearchTemplate = it }
            isQuestionTemplate?.also { this.isQuestionTemplate = it }
            isQuestionAnswerTemplate?.also { this.isQuestionAnswerTemplate = it }
            chatCompressorTemplate?.also { this.chatCompressorTemplate = it }
            highlightedCodeCompressorTemplate?.also { this.highlightedCodeCompressorTemplate = it }
            inlineReferencesResolverTemplate?.also { this.inlineReferencesResolverTemplate = it }
            topicExtractionTemplate?.also { this.topicExtractionTemplate = it }
            followupSuggestionsTemplate?.also { this.followupSuggestionsTemplate = it }
            functionsTemplate?.also { this.functionsTemplate = it }
            repoExtractionTemplate?.also { this.repoExtractionTemplate = it }
            maliciousQueryDetectionTemplate?.also { this.maliciousQueryDetectionTemplate = it }
            sampleQuestionsGeneratorTemplate?.also { this.sampleQuestionsGeneratorTemplate = it }
            shouldBotRespondTemplate?.also { this.shouldBotRespondTemplate = it }
            codeAssistTemplate?.also { this.codeAssistTemplate = it }
            tickleResponderTemplate?.also { this.tickleResponderTemplate = it }
            documentRelevanceTemplate?.also { this.documentRelevanceTemplate = it }
            documentSelectorTemplate?.also { this.documentSelectorTemplate = it }
            topicSource?.also { this.topicSource = it }
            topicMappingSource?.also { this.topicMappingSource = it }
            topicMappingAlpha?.also { this.topicMappingAlpha = it }
            topicMappingTopK?.also { this.topicMappingTopK = it }
            topicMappingScoreCutoff?.also { this.topicMappingScoreCutoff = it }
        }.also {
            debugEnd(_label, "MLSettings", it)
        }
    }

    suspend fun makeSampleQuestion(
        trx: Transaction? = null,
        _label: String? = null,
        id: SampleQuestionId = SampleQuestionId.random(),
        orgMember: OrgMemberDAO,
        query: String = "What is a sourcemark?",
        example: MLInferenceDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "SampleQuestion")
        SampleQuestionDAO.new(id) {
            this.orgMember = orgMember
            this.query = query
            this.inference = example
        }.also {
            debugEnd(_label, "SampleQuestion", it)
        }
    }

    suspend fun makeProviderAuthenticationState(
        trx: Transaction? = null,
        _label: String? = null,
        id: ProviderAuthenticationStateId = ProviderAuthenticationStateId.random(),
        provider: Provider = Provider.Slack,
        org: OrgDAO? = null,
        identity: IdentityDAO? = null,
        nonce: UUID = UUID.randomUUID(),
        signature: ByteArray = "signature".toByteArray(),
        createdAt: Instant? = null,
    ) = suspendedTransaction(trx = trx) {
        debug(_label, "ProviderAuthenticationState")

        val myOrg = org ?: makeOrg(trx = this)
        val myIdentity = identity ?: makeIdentity(trx = this)

        ProviderAuthenticationStateDAO.new(id) {
            this.org = myOrg
            this.identity = myIdentity
            this.nonce = nonce
            this.signature = signature
            this.provider = provider
            createdAt?.let { this.createdAt = it }
        }.also {
            debugEnd(_label, "ProviderAuthenticationState", it)
        }
    }

    suspend fun makeEval(
        trx: Transaction? = null,
        _label: String? = null,
        id: EvalId = EvalId.random(),
        org: OrgDAO? = null,
        subject: MLInferenceDAO? = null,
        result: MLInferenceDAO? = null,
        executionState: ExecutionState = ExecutionState.Pending,
    ) = suspendedTransaction(trx) {
        debug(_label, "Eval")

        val evalOrg = org ?: makeOrg(trx = this)
        val evalSubject = subject ?: makeMLInference(trx = this, org = evalOrg)

        EvalDAO.new(id) {
            this.evalSubject = evalSubject
            this.executionState = executionState
            this.evalResult = result
        }.also {
            debugEnd(_label, "Eval", it)
        }
    }

    suspend fun makeRegressionTest(
        trx: Transaction? = null,
        _label: String? = null,
        id: RegressionTestId = RegressionTestId.random(),
        template: MLInferenceTemplateDAO? = null,
        executionState: ExecutionState = ExecutionState.Pending,
    ) = suspendedTransaction(trx) {
        debug(_label, "RegressionTest")

        val regressionTestTemplate = template ?: makeMLInferenceTemplate(trx = this)

        RegressionTestDAO.new(id) {
            this.template = regressionTestTemplate
            this.executionState = executionState
        }.also {
            debugEnd(_label, "RegressionTest", it)
        }
    }

    suspend fun makeWebIngestionSite(
        trx: Transaction? = null,
        _label: String? = null,
        id: WebIngestionSiteId = WebIngestionSiteId.random(),
        org: OrgDAO? = null,
        installation: InstallationDAO? = null,
        url: String = "https://example.com",
        disabled: Boolean? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "WebIngestionSite")

        val thisOrg = org ?: makeOrg(trx = this)
        val thisInstallation = installation ?: makeInstallation(trx = this, org = thisOrg, provider = Provider.Web)

        WebIngestionSiteDAO.new(id) {
            this.installation = thisInstallation
            this.url = url
            disabled?.also {
                this.disabled = disabled
            }
        }.also {
            debugEnd(_label, "WebIngestionSite", it)
        }
    }

    suspend fun makeEmailEvent(
        trx: Transaction? = null,
        _label: String? = null,
        id: EmailEventId = EmailEventId.random(),
        emailEventType: EmailEventType,
        person: PersonDAO? = null,
        emailAddress: EmailAddress? = null,
        org: OrgDAO? = null,
        member: MemberDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "EmailEvent")

        val thisPerson = person ?: makePerson(trx = this)
        val thisEmailAddress = emailAddress ?: EmailAddress.of(thisPerson.primaryEmail)

        EmailEventDAO.new(id) {
            this.person = thisPerson
            this.emailAddress = thisEmailAddress.value
            this.emailEventType = emailEventType
            this.orgId = org?.idValue
            this.memberId = member?.idValue
        }.also {
            debugEnd(_label, "EmailEvent", it)
        }
    }

    suspend fun makeGoogleDriveFile(
        trx: Transaction? = null,
        _label: String? = null,
        id: GoogleDriveFileId = GoogleDriveFileId.random(),
        installation: InstallationDAO,
        googleDriveId: String = UUID.randomUUID().toString(),
        name: String = "file",
        type: GoogleDriveFileType = GoogleDriveFileType.GoogleDoc,
    ) = suspendedTransaction(trx) {
        debug(_label, "GoogleDriveFile")
        GoogleDriveFileDAO.new(id) {
            this.installation = installation
            this.googleDriveId = googleDriveId
            this.name = name
            this.type = type
        }.also {
            debugEnd(_label, "GoogleDriveFile", it)
        }
    }

    suspend fun makeEmbeddingDelete(
        trx: Transaction? = null,
        _label: String? = null,
        id: EmbeddingDeleteId = EmbeddingDeleteId.random(),
        namespaceId: OrgId = OrgId.random(),
        installationId: InstallationId? = null,
        groupId: UUID? = null,
        modifiedAt: Instant? = null,
        status: EmbeddingDeleteStatus = EmbeddingDeleteStatus.NotStarted,
    ) = suspendedTransaction(trx) {
        debug(_label, "EmbeddingDelete")
        EmbeddingDeleteDAO.new(id) {
            modifiedAt?.also { this.modifiedAt = modifiedAt }
            this.namespaceId = namespaceId
            this.installationId = installationId
            this.groupId = groupId
            this.status = status
        }.also {
            debugEnd(_label, "EmbeddingDelete", it)
        }
    }

    suspend fun makeOrgMemberMigrate(
        trx: Transaction? = null,
        _label: String? = null,
        id: OrgMemberMigrateId = OrgMemberMigrateId.random(),
        oldOrgMember: OrgMemberDAO? = null,
        newOrgMember: OrgMemberDAO? = null,
        status: OrgMemberMigrateStatus = OrgMemberMigrateStatus.NotStarted,
        modifiedAt: Instant? = Instant.nowWithMicrosecondPrecision(),
    ) = suspendedTransaction(trx) {
        debug(_label, "OrgMemberMigrate")

        val resolvedOldOrgMember = oldOrgMember ?: makeOrgMember(trx = this)
        val resolvedNewOrgMember = newOrgMember ?: makeOrgMember(trx = this)

        OrgMemberMigrateDAO.new(id) {
            this.oldOrgMemberId = resolvedOldOrgMember.idValue
            this.newOrgMemberId = resolvedNewOrgMember.idValue
            this.status = status
            modifiedAt?.also { this.modifiedAt = modifiedAt }
        }.also {
            debugEnd(_label, "OrgMemberMigrate", it)
        }
    }

    suspend fun makeCollection(
        trx: Transaction? = null,
        _label: String? = null,
        id: CollectionId = CollectionId.random(),
        installation: InstallationDAO? = null,
        name: String = "Collection $id",
        description: String = "collection",
        iconUrl: Url = Url("https://example.com/icon.png"),
        updatedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    ) = suspendedTransaction(trx) {
        debug(_label, "Collection")
        val thisInstallation = installation ?: makeInstallation(trx = this)

        CollectionDAO.new(id) {
            this.installation = thisInstallation
            this.name = name
            this.description = description
            this.iconUrl = iconUrl.asString
            this.updatedAt = updatedAt
        }.also {
            debugEnd(_label, "Collection", it)
        }
    }

    suspend fun makeCollectionDocument(
        trx: Transaction? = null,
        _label: String? = null,
        id: CollectionDocumentId = CollectionDocumentId.random(),
        collection: CollectionDAO? = null,
        documentId: UUID = UUID.randomUUID(),
        name: String = "Document $documentId",
        uri: String = "https://example.com/document/$documentId",
        updatedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    ) = suspendedTransaction(trx) {
        debug(_label, "CollectionDocument")
        val thisCollection = collection ?: makeCollection(trx = this)

        CollectionDocumentDAO.new(id) {
            this.collection = thisCollection
            this.documentId = documentId
            this.title = name
            this.uri = uri
            this.updatedAt = updatedAt
        }.also {
            debugEnd(_label, "CollectionDocument", it)
        }
    }

    suspend fun makeSessionAction(
        trx: Transaction? = null,
        _label: String? = null,
        id: SessionActionId = SessionActionId.random(),
        sessionId: UUID = UUID.randomUUID(),
        actionId: String = "signUp",
        url: String = "https://getunblocked.com/login",
        canonicalUrl: String = "https://getunblocked.com/login",
        createdAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "SessionAction")
        SessionActionDAO.new(id) {
            this.url = url
            this.canonicalUrl = canonicalUrl
            this.sessionId = sessionId
            this.actionId = actionId
            createdAt?.let { this.createdAt = createdAt }
        }.also {
            debugEnd(_label, "SessionAction", it)
        }
    }

    suspend fun makeSessionReferral(
        trx: Transaction? = null,
        _label: String? = null,
        id: SessionReferralId = SessionReferralId.random(),
        sessionId: UUID = UUID.randomUUID(),
        campaignName: String = "utm",
        url: String = "https://getunblocked.com/login",
        canonicalUrl: String = "https://getunblocked.com/login",
        createdAt: Instant? = null,
        medium: String? = null,
        campaignSource: String? = null,
        term: String? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "SessionReferral")
        SessionReferralDAO.new(id) {
            this.url = url
            this.canonicalUrl = canonicalUrl
            this.sessionId = sessionId
            this.campaignName = campaignName
            this.medium = medium
            this.campaignSource = campaignSource
            this.term = term
            createdAt?.let { this.createdAt = createdAt }
        }.also {
            debugEnd(_label, "SessionReferral", it)
        }
    }

    suspend fun makeIsQuestionRegressionTest(
        trx: Transaction? = null,
        _label: String? = null,
        question: String?,
        org: OrgDAO? = null,
        shouldPass: Boolean = true,
    ) = suspendedTransaction(trx) {
        debug(_label, "IsQuestionRegressionTest")
        val thisOrg = org ?: makeOrg(trx = this)
        IsQuestionRegressionDAO.new {
            this.org = thisOrg
            this.question = question ?: "test?"
            this.shouldPass = shouldPass
        }.also {
            debugEnd(_label, "IsQuestionRegressionTest", it)
        }
    }

    suspend fun makeIsQuestionAnswerRegressionTest(
        trx: Transaction? = null,
        _label: String? = null,
        inference: MLInferenceDAO? = null,
        shouldPass: Boolean = true,
    ) = suspendedTransaction(trx) {
        debug(_label, "IsQuestionAnswerRegressionTest")
        val thisInference = inference ?: makeMLInference(trx = this)
        IsQuestionAnswerRegressionDAO.new {
            this.inference = thisInference
            this.shouldPass = shouldPass
        }.also {
            debugEnd(_label, "IsQuestionAnswerRegressionTest", it)
        }
    }

    suspend fun makePlan(
        trx: Transaction? = null,
        _label: String? = null,
        name: String = "Plan ${UUID.randomUUID()}",
        tier: PlanTier? = null,
        basePlan: PlanDAO? = null,
        isTrialPlan: Boolean? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "Plan")
        PlanDAO.new {
            this.name = name
            this.tier = tier
            this.basePlan = basePlan
            this.isTrialPlan = isTrialPlan
        }.also {
            debugEnd(_label, "Plan", it)
        }
    }

    suspend fun makePlanCapability(
        trx: Transaction? = null,
        _label: String? = null,
        plan: PlanDAO? = null,
        capability: PlanCapabilityType = PlanCapabilityType.SSO,
        enabled: Boolean,
    ) = suspendedTransaction(trx) {
        debug(_label, "PlanCapability")
        val thisPlan = plan ?: makePlan(trx = this)

        PlanCapabilityDAO.new {
            this.plan = thisPlan
            this.capability = capability
            this.enabled = enabled
        }.also {
            debugEnd(_label, "PlanCapability", it)
        }
    }

    suspend fun makeCodaOrganization(
        trx: Transaction? = null,
        _label: String? = null,
        id: CodaOrganizationId = CodaOrganizationId.random(),
        installation: InstallationDAO? = null,
        organizationId: String = UUID.randomUUID().toString(),
        name: String = "Coda Organization",
    ) = suspendedTransaction(trx) {
        debug(_label, "CodaOrganization")

        val codaOrganizationInstallation = installation ?: makeInstallation(trx = this, provider = Provider.Coda)

        CodaOrganizationDAO.new(id) {
            this.installation = codaOrganizationInstallation
            this.organizationId = organizationId
            this.name = name
        }.also {
            debugEnd(_label, "CodaOrganization", it)
        }
    }

    suspend fun makeCodaWorkspace(
        trx: Transaction? = null,
        _label: String? = null,
        id: CodaWorkspaceId = CodaWorkspaceId.random(),
        codaOrganization: CodaOrganizationDAO? = null,
        workspaceId: String = UUID.randomUUID().toString(),
        name: String = "Coda Workspace",
    ) = suspendedTransaction(trx) {
        debug(_label, "CodaWorkspace")

        val thisCodaOrganization = codaOrganization ?: makeCodaOrganization(trx = this)

        CodaWorkspaceDAO.new(id) {
            this.codaOrganization = thisCodaOrganization
            this.workspaceId = workspaceId
            this.name = name
        }.also {
            debugEnd(_label, "CodaWorkspace", it)
        }
    }

    suspend fun makeCodaWorkspaceMember(
        trx: Transaction? = null,
        _label: String? = null,
        id: CodaWorkspaceMemberId = CodaWorkspaceMemberId.random(),
        codaWorkspace: CodaWorkspaceDAO? = null,
        member: MemberDAO? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "CodaWorkspaceMember")

        val thisCodaWorkspace = codaWorkspace ?: makeCodaWorkspace(trx = this)
        val thisMember = member ?: makeMember(trx = this)

        CodaWorkspaceMemberDAO.new(id) {
            this.codaWorkspace = thisCodaWorkspace
            this.member = thisMember
        }.also {
            debugEnd(_label, "CodaWorkspaceMember", it)
        }
    }

    suspend fun makeCodaGroup(
        trx: Transaction? = null,
        _label: String? = null,
        id: CodaGroupId = CodaGroupId.random(),
        codaOrganization: CodaOrganizationDAO? = null,
        groupId: String = UUID.randomUUID().toString(),
        name: String = "Coda Group $groupId",
    ) = suspendedTransaction(trx) {
        debug(_label, "CodaGroup")

        val thisCodaOrganization = codaOrganization ?: makeCodaOrganization(trx = this)

        CodaGroupDAO.new(id) {
            this.codaOrganization = thisCodaOrganization
            this.groupId = groupId
            this.name = name
        }.also {
            debugEnd(_label, "CodaGroup", it)
        }
    }

    suspend fun makeCodaGroupMember(
        trx: Transaction? = null,
        _label: String? = null,
        id: CodaGroupMemberId = CodaGroupMemberId.random(),
        codaGroup: CodaGroupDAO? = null,
        userId: String = UUID.randomUUID().toString(),
        email: String = "$<EMAIL>",
    ) = suspendedTransaction(trx) {
        debug(_label, "CodaGroupMember")

        val thisCodaGroup = codaGroup ?: makeCodaGroup(trx = this)

        CodaGroupMemberDAO.new(id) {
            this.codaGroup = thisCodaGroup
            this.userId = userId
            this.email = email
        }.also {
            debugEnd(_label, "CodaGroupMember", it)
        }
    }

    suspend fun makeCodaResource(
        trx: Transaction? = null,
        _label: String? = null,
        id: CodaResourceId = CodaResourceId.random(),
        installation: InstallationDAO? = null,
        codaId: String = UUID.randomUUID().toString(),
        name: String = "Coda Resource",
        resourceType: CodaResourceType = CodaResourceType.Folder,
    ) = suspendedTransaction(trx) {
        debug(_label, "CodaResource")

        val codaInstallation = installation ?: makeInstallation(trx = this, provider = Provider.Coda)

        CodaResourceDAO.new(id) {
            this.installation = codaInstallation
            this.codaId = codaId
            this.name = name
            this.resourceType = resourceType
        }.also {
            debugEnd(_label, "CodaResource", it)
        }
    }

    suspend fun makeCodaDoc(
        trx: Transaction? = null,
        _label: String? = null,
        id: CodaDocId = CodaDocId.random(),
        codaOrganization: CodaOrganizationDAO? = null,
        docId: String = UUID.randomUUID().toString(),
        folderId: String = UUID.randomUUID().toString(),
        owner: String = "<EMAIL>",
    ) = suspendedTransaction(trx) {
        debug(_label, "CodaDoc")

        val thisCodaOrganization = codaOrganization ?: makeCodaOrganization(trx = this)

        CodaDocDAO.new(id) {
            this.codaOrganization = thisCodaOrganization
            this.docId = docId
            this.folderId = folderId
            this.owner = owner
        }.also {
            debugEnd(_label, "CodaDoc", it)
        }
    }

    suspend fun makeCodeReviewExclusion(
        trx: Transaction? = null,
        _label: String? = null,
        id: CodeReviewExclusionId = CodeReviewExclusionId.random(),
        repo: RepoDAO? = null,
        excludedPaths: List<String> = listOf("*.md", "docs/**"),
    ): CodeReviewExclusionDAO = suspendedTransaction(trx) {
        debug(_label, "CodeReviewExclusion")

        val exclusionRepo = repo ?: makeRepo(trx = this)

        CodeReviewExclusionDAO.new(id) {
            this.repo = exclusionRepo
            this.excludedPaths = excludedPaths
        }.also {
            debugEnd(_label, "CodeReviewExclusion", it)
        }
    }

    suspend fun makeDataSourcePreset(
        trx: Transaction? = null,
        _label: String? = null,
        id: DataSourcePresetId = DataSourcePresetId.random(),
        org: OrgDAO? = null,
        name: String = "Default Preset Name",
        avatarUrl: Url = Url("https://example.com/avatar.png"),
        createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
        modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    ) = suspendedTransaction(trx) {
        debug(_label, "DataSourcePreset")

        val presetOrg = org ?: makeOrg(trx = this)

        DataSourcePresetDAO.new(id) {
            this.org = presetOrg
            this.name = name
            this.avatarUrl = avatarUrl.asString
            this.createdAt = createdAt
            this.modifiedAt = modifiedAt
        }.also {
            debugEnd(_label, "DataSourcePreset", it)
        }
    }

    suspend fun makeProductFeedbackResponse(
        trx: Transaction? = null,
        _label: String? = null,
        id: ProductFeedbackResponseId = ProductFeedbackResponseId.random(),
        org: OrgDAO? = null,
        person: PersonDAO? = null,
        productAgent: ProductAgentType = ProductAgentType.Slack,
        feedbackType: ProductFeedbackResponseType = ProductFeedbackResponseType.Positive,
        feedbackDescription: String? = null,
        installations: ProductFeedbackResponseInstallations? = null,
        createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
        modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    ) = suspendedTransaction(trx) {
        debug(_label, "ProductFeedback")

        val feedbackOrg = org ?: makeOrg(trx = this)
        val feedbackPerson = person ?: makePerson(trx = this)

        val feedbackConnectedInstallations = installations ?: ProductFeedbackResponseInstallations(
            connectedInstallations = emptyList(),
            unconnectedInstallations = emptyList(),
            suppressedInstallations = emptyList(),
        )

        ProductFeedbackResponseDAO.new(id) {
            this.org = feedbackOrg
            this.person = feedbackPerson
            this.productAgent = productAgent
            this.feedbackType = feedbackType
            this.feedbackDescription = feedbackDescription
            this.installationsState = feedbackConnectedInstallations.serializeAndCompress()
            this.createdAt = createdAt
            this.modifiedAt = modifiedAt
        }.also {
            debugEnd(_label, "ProductFeedback", it)
        }
    }

    suspend fun makeAuditLog(
        trx: Transaction? = null,
        _label: String? = null,
        orgId: OrgId = OrgId.random(),
        type: AuditLogType = AuditLogType.RbacEnabled,
        actorId: UUID = UUID.randomUUID(),
        actorType: AuditLogActorType = AuditLogActorType.Person,
        actorName: String = "Actor",
        actorAvatarUrl: Url? = null,
        actorProvider: Provider? = null,
        actorEmail: EmailAddress? = null,
        provider: Provider? = null,
        installationId: InstallationId? = null,
        installationKey: UUID? = null,
        installationName: String? = null,
        action: List<String> = emptyList(),
        detail: AuditLogDetail? = null,
        createdAt: Instant = Instant.nowWithMicrosecondPrecision(),
        modifiedAt: Instant = Instant.nowWithMicrosecondPrecision(),
    ) = suspendedTransaction(trx) {
        debug(_label, "AuditLog")

        AuditLogDAO.new {
            this.orgId = orgId.value
            this.type = type
            this.actorId = actorId
            this.actorType = actorType
            this.actorName = actorName
            this.actorAvatarUrl = actorAvatarUrl?.asString
            this.actorProvider = actorProvider
            this.actorEmail = actorEmail?.value
            this.provider = provider
            this.installationId = installationId?.value
            this.installationKey = installationKey
            this.installationName = installationName
            this.action = action
            this.detail = detail?.encode()?.compress()
            this.createdAt = createdAt
            this.modifiedAt = modifiedAt
        }.also {
            debugEnd(_label, "AuditLog", it)
        }
    }

    suspend fun makeMicrosoftTeamsTeam(
        trx: Transaction? = null,
        _label: String? = null,
        id: MicrosoftTeamsTeamId = MicrosoftTeamsTeamId.random(),
        org: OrgDAO? = null,
        installation: InstallationDAO? = null,
        externalId: String = UUID.randomUUID().toString(),
        displayName: String = "Test Team",
        webUrl: String? = "https://teams.microsoft.com/team",
        isArchived: Boolean? = false,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "MicrosoftTeamsTeam")

        checkSame(org, installation?.org) { "Inconsistent org and installation.org" }

        val teamOrg = org ?: installation?.org ?: makeOrg(trx = this)
        val teamInstallation = installation ?: makeInstallation(trx = this, org = teamOrg, provider = Provider.MicrosoftTeams)

        MicrosoftTeamsTeamDAO.new(id) {
            this.installation = teamInstallation
            this.externalId = externalId
            this.displayName = displayName
            webUrl?.also { this.webUrl = it }
            isArchived?.also { this.isArchived = it }
            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
        }.also {
            debugEnd(_label, "MicrosoftTeamsTeam", it)
        }
    }

    suspend fun makeMicrosoftTeamsChannel(
        trx: Transaction? = null,
        _label: String? = null,
        id: MicrosoftTeamsChannelId = MicrosoftTeamsChannelId.random(),
        org: OrgDAO? = null,
        installation: InstallationDAO? = null,
        team: MicrosoftTeamsTeamDAO? = null,
        externalId: String = UUID.randomUUID().toString(),
        displayName: String = "Test Channel",
        createdDateTime: Instant? = null,
        webUrl: String? = "https://teams.microsoft.com/channel",
        isArchived: Boolean? = false,
        createdAt: Instant? = null,
        modifiedAt: Instant? = null,
    ) = suspendedTransaction(trx) {
        debug(_label, "MicrosoftTeamsChannel")

        checkSame(org, installation?.org, team?.installation?.org) { "Inconsistent org across parameters" }
        checkSame(installation, team?.installation) { "Inconsistent installation between 'installation' and 'team.installation'" }

        val channelOrg = org ?: installation?.org ?: team?.installation?.org ?: makeOrg(trx = this)
        val channelInstallation =
            installation ?: team?.installation ?: makeInstallation(trx = this, org = channelOrg, provider = Provider.MicrosoftTeams)
        val channelTeam = team ?: makeMicrosoftTeamsTeam(trx = this, installation = channelInstallation)

        MicrosoftTeamsChannelDAO.new(id) {
            this.installation = channelInstallation
            this.team = channelTeam
            this.externalId = externalId
            this.displayName = displayName
            createdDateTime?.also { this.createdDateTime = it }
            webUrl?.also { this.webUrl = it }
            isArchived?.also { this.isArchived = it }
            createdAt?.also { this.createdAt = it }
            modifiedAt?.also { this.modifiedAt = it }
        }.also {
            debugEnd(_label, "MicrosoftTeamsChannel", it)
        }
    }
}
