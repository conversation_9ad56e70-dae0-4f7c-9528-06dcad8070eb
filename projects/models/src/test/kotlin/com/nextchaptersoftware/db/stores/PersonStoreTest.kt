package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.interceptors.explain.SlowQueryConfig
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.PersonDAO
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.PersonOnboardingState
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ReleaseChannel
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.types.EmailAddress
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import java.util.UUID
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.seconds
import kotlin.time.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class PersonStoreTest : DatabaseTestsBase() {
    private val store = Stores.personStore

    @Test
    fun allIds() = suspendingDatabaseTest {
        val personA = makePerson()
        val personB = makePerson()
        val personC = makePerson()

        val result = store.allIds(trx = this.transaction)

        assertThat(result).containsExactlyInAnyOrder(personA.id.value, personB.id.value, personC.id.value)
    }

    @Test
    fun findById() = suspendingDatabaseTest {
        val personA = makePerson().asDataModel()

        val resultA = store.findById(id = personA.id)
        assertThat(resultA?.id).isEqualTo(personA.id)
        assertThat(resultA?.createdAt).isEqualTo(personA.createdAt)
        assertThat(resultA?.modifiedAt).isEqualTo(personA.modifiedAt)
        assertThat(resultA?.customAvatarUrl).isEqualTo(personA.customAvatarUrl)
        assertThat(resultA?.primaryEmail).isEqualTo(personA.primaryEmail)
    }

    @Test
    fun `findByEmail and findEmails`() = suspendingDatabaseTest {
        val testEmail1 = EmailAddress.of("${UUID.randomUUID()}@mail.com")
        val testEmail2 = EmailAddress.of("${UUID.randomUUID()}@mail.com")
        val testEmail3 = EmailAddress.of("${UUID.randomUUID()}@mail.com")

        val p1 = makePerson(primaryEmail = testEmail1).also {
            makeIdentity(person = it, primaryEmail = testEmail1, emails = listOf(testEmail1))
        }
        val p2 = makePerson(primaryEmail = testEmail2).also {
            makeIdentity(person = it, primaryEmail = testEmail2, emails = listOf(testEmail2, testEmail3))
        }

        store.findByEmail(primaryEmail = testEmail1).also {
            assertThat(it?.id).isEqualTo(p1.idValue)
        }
        store.findByEmail(primaryEmail = testEmail2).also {
            assertThat(it?.id).isEqualTo(p2.idValue)
        }
        store.findByEmail(primaryEmail = testEmail3).also {
            assertThat(it?.id).isEqualTo(p2.idValue)
        }
        store.findEmails(personId = p1.idValue).also {
            assertThat(it).containsExactlyInAnyOrder(testEmail1)
        }
        store.findEmails(personId = p2.idValue).also {
            assertThat(it).containsExactlyInAnyOrder(testEmail2, testEmail3)
        }
    }

    @Test
    fun `findByEmail uses exact matching email`() = suspendingDatabaseTest {
        val testEmail = EmailAddress.of("<EMAIL>")

        makePerson(primaryEmail = testEmail).also {
            makeIdentity(person = it, primaryEmail = testEmail, emails = listOf(testEmail))
        }

        store.findByEmail(primaryEmail = EmailAddress.of("<EMAIL>")).also {
            assertThat(it).isNull()
        }
    }

    @Test
    fun `findByEmail finds identity-less person`() = suspendingDatabaseTest {
        val testEmail = EmailAddress.random()

        val person = makePerson(primaryEmail = testEmail)

        store.findByEmail(primaryEmail = testEmail).also {
            assertThat(it?.id).isEqualTo(person.idValue)
        }
    }

    @Test
    fun findByIdentityId() = suspendingDatabaseTest {
        val person = makePerson()
        val identity = makeIdentity(person = person)

        val result = store.findByIdentityId(identityId = identity.id.value)
        assertThat(result).isEqualTo(person.asDataModel())
    }

    @Test
    fun `setHasSeenTutorial VSCode`() = suspendingDatabaseTest {
        val person = makePerson().asDataModel()
        assertThat(person.hasSeenTutorialVSCode).isFalse
        assertThat(person.hasSeenTutorial).isFalse

        val result = store.setHasSeenTutorial(id = person.id, value = true, agent = ProductAgentType.VSCode)
        assertThat(result).isTrue()

        val personFromDb = suspendedTransaction { PersonDAO.findById(person.id)?.asDataModel() } ?: error("missing person")
        assertThat(personFromDb.hasSeenTutorialVSCode).isTrue
        assertThat(personFromDb.hasSeenTutorial).isTrue
    }

    @Test
    fun `setHasSeenTutorial Hub`() = suspendingDatabaseTest {
        val person = makePerson().asDataModel()
        assertThat(person.hasSeenTutorialHub).isFalse
        assertThat(person.hasSeenTutorial).isFalse

        val result = store.setHasSeenTutorial(id = person.id, value = true, agent = ProductAgentType.Hub)
        assertThat(result).isTrue()

        val personFromDb = suspendedTransaction { PersonDAO.findById(person.id)?.asDataModel() } ?: error("missing person")
        assertThat(personFromDb.hasSeenTutorialHub).isTrue
        assertThat(personFromDb.hasSeenTutorial).isTrue
    }

    @Test
    fun `setHasSeenTopFile VSCode`() = suspendingDatabaseTest {
        val person = makePerson()
        val result = store.setHasSeenTopFile(id = person.idValue, value = true, agent = ProductAgentType.VSCode)
        assertThat(result).isTrue()
    }

    @Test
    fun `setHasCreatedNote VSCode`() = suspendingDatabaseTest {
        val person = makePerson()
        val result = store.setHasCreatedNote(id = person.idValue, value = true, agent = ProductAgentType.VSCode)
        assertThat(result).isTrue()
    }

    @Test
    fun `setHasDismissedToast VSCode`() = suspendingDatabaseTest {
        val person = makePerson()
        val result = store.setHasDismissedToast(id = person.idValue, value = true, agent = ProductAgentType.VSCode)
        assertThat(result).isTrue()
    }

    @Test
    fun setHubIsInstalled() = suspendingDatabaseTest {
        val person = makePerson()
        val result = store.setHasInstalledHub(id = person.idValue, value = true)
        assertThat(result).isTrue()
    }

    @Test
    fun `setHasSeenTutorial Intellij`() = suspendingDatabaseTest {
        val person = makePerson().asDataModel()
        assertThat(person.hasSeenTutorialIntellij).isFalse
        assertThat(person.hasSeenTutorial).isFalse

        val result = store.setHasSeenTutorial(id = person.id, value = true, agent = ProductAgentType.IntelliJ)
        assertThat(result).isTrue()

        val personFromDb = suspendedTransaction { PersonDAO.findById(person.id)?.asDataModel() } ?: error("missing person")
        assertThat(personFromDb.hasSeenTutorialIntellij).isTrue
        assertThat(personFromDb.hasSeenTutorial).isTrue
    }

    @Test
    fun `setHasSeenTopFile Intellij`() = suspendingDatabaseTest {
        val person = makePerson()
        val result = store.setHasSeenTopFile(id = person.idValue, value = true, agent = ProductAgentType.IntelliJ)
        assertThat(result).isTrue()
    }

    @Test
    fun `setHasCreatedNote Intellij`() = suspendingDatabaseTest {
        val person = makePerson()
        val result = store.setHasCreatedNote(id = person.idValue, value = true, agent = ProductAgentType.IntelliJ)
        assertThat(result).isTrue()
    }

    @Test
    fun `setHasDismissedToast Intellij`() = suspendingDatabaseTest {
        val person = makePerson()
        val result = store.setHasDismissedToast(id = person.idValue, value = true, agent = ProductAgentType.IntelliJ)
        assertThat(result).isTrue()
    }

    @Test
    fun `setting an onboarding flag does not edit the others`() = suspendingDatabaseTest {
        val personId = PersonId.random()
        makePerson(id = personId)

        assertThat(store.setHasCreatedNote(id = personId, value = true, agent = ProductAgentType.VSCode)).isTrue
        suspendedTransaction { PersonDAO.findById(personId)?.asDataModel() }?.also {
            assertThat(it.hasSeenTutorialVSCode).isFalse
            assertThat(it.hasCreatedNoteVSCode).isTrue
        } ?: error("missing person")

        val person = suspendedTransaction { PersonDAO.findById(personId)?.asDataModel() }?.also {
            assertThat(it.hasSeenTutorialVSCode).isFalse
            assertThat(it.hasCreatedNoteVSCode).isTrue
        } ?: error("missing person")

        // Should not touch the person model again
        assertThat(store.setHasCreatedNote(id = personId, value = true, agent = ProductAgentType.VSCode)).isFalse
        assertThat(suspendedTransaction { PersonDAO.findById(personId)?.asDataModel() }?.modifiedAt).isEqualTo(person.modifiedAt)
    }

    @Test
    fun countPeople() = suspendingDatabaseTest(
        {
            slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
        },
    ) {
        val now = Instant.nowWithMicrosecondPrecision()
        val personA = makePerson(createdAt = now.minus(2.seconds)).idValue
        val personB = makePerson(createdAt = now.minus(1.seconds)).idValue

        // createdBefore
        suspendedTransaction {
            assertThat(PersonDAO.count()).isEqualTo(2)
            assertThat(store.countPeople(createdBefore = now)).isEqualTo(2)
            assertThat(store.countPeople(createdBefore = now.minus(1.seconds))).isEqualTo(1)
            assertThat(store.countPeople(createdBefore = now.minus(2.seconds))).isEqualTo(0)
        }

        // exclude
        suspendedTransaction {
            assertThat(store.countPeople(createdBefore = now, excludeIds = listOf(personA, personB))).isEqualTo(0)

            assertThat(store.countPeople(createdBefore = now, excludeIds = listOf(personA))).isEqualTo(1)
            assertThat(store.countPeople(createdBefore = now, excludeIds = listOf(personB))).isEqualTo(1)

            assertThat(store.countPeople(createdBefore = now.minus(1.seconds), excludeIds = listOf(personB))).isEqualTo(1)
            assertThat(store.countPeople(createdBefore = now.minus(1.seconds), excludeIds = listOf(personA))).isEqualTo(0)
        }
    }

    @Test
    fun deletePerson() = suspendingDatabaseTest {
        val person = makePerson()
        assertThat(suspendedTransaction { PersonDAO.findById(person.id) }).isNotNull()
        store.deletePerson(person.idValue)
        assertThat(suspendedTransaction { PersonDAO.findById(person.id) }).isNull()
    }

    @Test
    fun `test finding persons by onboardingStatus`() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
        },
    ) {
        makePerson(hasCreatedNoteIntellij = true)
        val person2 = makePerson(sentOnboardingEmailCampaign = true)

        val foundPersons = store.findByOnboardingState(onboardingState = PersonOnboardingState.SentOnboardingEmailCampaign)

        assertThat(foundPersons).hasSize(1)
        assertThat(foundPersons.first().id).isEqualTo(person2.idValue)
    }

    @Test
    fun `test finding persons by excluding onboardingStatus`() = suspendingDatabaseTest(
        configure = {
            slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false)
        },
    ) {
        val person1 = makePerson(hasCreatedNoteIntellij = true)
        makePerson(sentOnboardingEmailCampaign = true)

        val foundPersons = store.findByExcludingOnboardingState(excludeOnboardingState = PersonOnboardingState.SentOnboardingEmailCampaign)

        assertThat(foundPersons).hasSize(1)
        assertThat(foundPersons.first().id).isEqualTo(person1.idValue)
    }

    @Test
    fun `release channel`() = suspendingDatabaseTest {
        val person = makePerson()
        assertThat(person.subscribedReleaseChannel).isNull()
        store.setSubscribedReleaseChannel(id = person.idValue, releaseChannel = ReleaseChannel.Beta)
        assertThat(
            suspendedTransaction {
                PersonDAO.findById(person.id)?.asDataModel()?.subscribedReleaseChannel
            },
        ).isEqualTo(ReleaseChannel.Beta)
        val releaseChannel = store.getSubscribedReleaseChannel(id = person.idValue)
        assertThat(releaseChannel).isEqualTo(ReleaseChannel.Beta)
    }

    @Test
    fun createdAccountAts() = suspendingDatabaseTest {
        val personA = makePerson()
        val personB = makePerson()
        val randomUUID = PersonId.random()
        val result = store.createdAccountAts(ids = listOf(personA.idValue, personB.idValue, randomUUID))
        assertThat(result[personB.idValue]).isEqualTo(personB.createdAt)
        assertThat(result[personA.idValue]).isEqualTo(personA.createdAt)
        assertThat(result).doesNotContainKey(randomUUID)
    }

    @Test
    fun getIdentitiesForPerson() = suspendingDatabaseTest {
        val person = makePerson()

        makeIdentity(person = person, provider = Provider.GitHub)
        makeIdentity(person = person, provider = Provider.Bitbucket)
        makeIdentity(person = person, provider = Provider.GitLab)
        makeIdentity(person = person, provider = Provider.Jira)

        store.getIdentitiesForPerson(personId = person.idValue).also { identities ->
            assertThat(identities).hasSize(3)
            assertThat(identities.map { it.provider }).containsExactlyInAnyOrder(Provider.GitHub, Provider.Bitbucket, Provider.GitLab)
        }
        store.getIdentitiesForPerson(personId = PersonId.random()).also { identities ->
            assertThat(identities).isEmpty()
        }
        store.getIdentitiesForPerson(personId = makePerson().idValue).also { identities ->
            assertThat(identities).isEmpty()
        }
    }

    @Test
    fun getOrgsForPerson() = suspendingDatabaseTest {
        val org = makeOrg()
        val bitbucketTeam = makeScmTeam(org = org, displayName = "Acme Bitbucket", provider = Provider.Bitbucket)
        val gitlabTeam = makeScmTeam(org = org, displayName = "Acme GitLab", provider = Provider.GitLab)

        val person = makePerson()
        val orgMember = makeOrgMember(org = org, person = person)

        val gitHubIdentity = makeIdentity(person = person, provider = Provider.GitHub)
        val bitbucketIdentity = makeIdentity(person = person, provider = Provider.Bitbucket)
        val gitlabIdentity = makeIdentity(person = person, provider = Provider.GitLab)
        val jiraIdentity = makeIdentity(person = person, provider = Provider.Jira)

        val gitHubOrg = makeOrg(enabledAt = Instant.nowWithMicrosecondPrecision())
        val gitHubOrgInstallation = makeInstallation(org = gitHubOrg, provider = Provider.GitHub)
        val gitHubTeam = makeScmTeam(
            displayName = "Acme GitHub",
            provider = Provider.GitHub,
            installation = gitHubOrgInstallation,
        )
        val githubOrgMember = makeOrgMember(org = gitHubOrg, person = person)

        makeMember(isPrimaryMember = true, identity = gitHubIdentity, scmTeam = gitHubTeam, orgMember = githubOrgMember)
        makeMember(isPrimaryMember = true, identity = bitbucketIdentity, scmTeam = bitbucketTeam, orgMember = orgMember)
        makeMember(isPrimaryMember = true, identity = gitlabIdentity, scmTeam = gitlabTeam, orgMember = orgMember)
        makeMember(isPrimaryMember = true, identity = jiraIdentity, scmTeam = gitHubTeam, orgMember = githubOrgMember)

        store.getOrgsForPeople(personIds = listOf(person.idValue)).also { orgsByPerson ->
            assertThat(orgsByPerson.keys).hasSize(1)
            assertThat(orgsByPerson.values.flatten()).hasSize(2)
            assertThat(orgsByPerson.values.flatten()).containsExactlyInAnyOrder(gitHubOrg.idValue, org.idValue)
        }

        Stores.installationStore.markForDeletion(orgId = gitHubOrg.idValue, installationId = gitHubOrgInstallation.idValue)

        store.getOrgsForPeople(personIds = listOf(person.idValue)).also { orgsByPerson ->
            assertThat(orgsByPerson.keys).hasSize(1)
            assertThat(orgsByPerson.values.flatten()).hasSize(1)
            assertThat(orgsByPerson.values.flatten()).containsExactlyInAnyOrder(org.idValue)
        }
    }

    @Test
    fun mergePeople() = suspendingDatabaseTest {
        val personA = makePerson().also {
            makeIdentity(person = it, provider = Provider.Bitbucket, username = "A-bitbucket")
            makeIdentity(person = it, provider = Provider.GitLab, username = "A-gitlab")
        }
        val personB = makePerson().also {
            makeIdentity(person = it, provider = Provider.Slack, username = "B-slack")
            makeIdentity(person = it, provider = Provider.GitLab, username = "B-gitlab")
        }

        store.mergePeople(fromPerson = personA.idValue, toPerson = personB.idValue)

        assertThat(PersonDAO.findById(personA.id)).isNull()
        assertThat(PersonDAO.findById(personB.id)).isNotNull()

        suspendedTransaction {
            IdentityModel
                .select(IdentityModel.username)
                .where { IdentityModel.person eq personA.id }
                .map { it[IdentityModel.username] }
        }.also { identities ->
            assertThat(identities).isEmpty()
        }

        suspendedTransaction {
            IdentityModel
                .select(IdentityModel.username)
                .where { IdentityModel.person eq personB.id }
                .map { it[IdentityModel.username] }
        }.also { identities ->
            assertThat(identities).containsExactlyInAnyOrder("B-slack", "B-gitlab", "A-bitbucket", "A-gitlab")
        }
    }

    @Test
    fun `findByOrgMemberId -- when no org member`() = suspendingDatabaseTest {
        val org = makeOrg()
        assertThat(
            store.findByOrgMemberId(
                orgId = org.idValue,
                orgMemberId = OrgMemberId.random(),
            ),
        ).isNull()
    }

    @Test
    fun `findByOrgMemberId -- when no org matching`() = suspendingDatabaseTest {
        val orgMemberWithoutPerson = makeOrgMember(createPerson = false)
        assertThat(
            store.findByOrgMemberId(
                orgId = OrgId.random(),
                orgMemberId = orgMemberWithoutPerson.idValue,
            ),
        ).isNull()
    }

    @Test
    fun `findByOrgMemberId -- when no person`() = suspendingDatabaseTest {
        val orgMemberWithoutPerson = makeOrgMember(createPerson = false)
        assertThat(
            store.findByOrgMemberId(
                orgId = orgMemberWithoutPerson.org.idValue,
                orgMemberId = orgMemberWithoutPerson.idValue,
            ),
        ).isNull()
    }

    @Test
    fun `findByOrgMemberId -- when has person`() = suspendingDatabaseTest {
        val orgMemberWithPerson = makeOrgMember(createPerson = true)
        assertThat(
            store.findByOrgMemberId(
                orgId = orgMemberWithPerson.org.idValue,
                orgMemberId = orgMemberWithPerson.idValue,
            ),
        ).isEqualTo(
            orgMemberWithPerson.person?.asDataModel(),
        )
    }

    @Nested
    inner class FindByOrgMemberIdsTest {
        @Test
        fun `findByOrgMemberIds -- when no members`() = suspendingDatabaseTest {
            val org = makeOrg()
            assertThat(
                store.findByOrgMemberIds(
                    orgId = org.idValue,
                    orgMemberIds = emptyList(),
                ),
            ).isEmpty()
        }

        @Test
        fun `findByOrgMemberIds -- when wrong org`() = suspendingDatabaseTest {
            val org = makeOrg()
            val orgMember1 = makeOrgMember(org = org)
            val orgMember2 = makeOrgMember(org = org)
            assertThat(
                store.findByOrgMemberIds(
                    orgId = OrgId.random(),
                    orgMemberIds = listOf(
                        orgMember1.idValue,
                        orgMember2.idValue,
                    ),
                ),
            ).isEmpty()
        }

        @Test
        fun `findByOrgMemberIds -- happy path`() = suspendingDatabaseTest {
            val org = makeOrg()
            val person1 = makePerson()

            val orgMember1 = makeOrgMember(org = org, person = person1)
            val orgMember2 = makeOrgMember(org = org, createPerson = true)
            makeOrgMember(org = org, createPerson = false)

            val person2 = orgMember2.person.required()

            assertThat(
                store.findByOrgMemberIds(
                    orgId = org.idValue,
                    orgMemberIds = listOf(
                        orgMember1.idValue,
                        orgMember2.idValue,
                    ),
                ),
            ).isEqualTo(
                mapOf(
                    orgMember1.idValue to person1.asDataModel(),
                    orgMember2.idValue to person2.asDataModel(),
                    // orgMember3 does not have person
                ),
            )
        }
    }

    @Test
    fun `getTrustedPersonEmails excludes no-reply emails`() = suspendingDatabaseTest {
        val person = makePerson(primaryEmail = EmailAddress.of("<EMAIL>"))
        makeIdentity(
            person = person,
            provider = Provider.Jira,
            emails = listOf("<EMAIL>", "<EMAIL>", "<EMAIL>").map { EmailAddress.of(it) },
        )
        makeIdentity(
            person = person,
            provider = Provider.GitLab,
            emails = listOf("<EMAIL>", "<EMAIL>").map { EmailAddress.of(it) },
        )
        makeIdentity(
            person = person,
            provider = Provider.Bitbucket,
            emails = listOf("<EMAIL>", "<EMAIL>").map { EmailAddress.of(it) },
        )
        makeIdentity(
            person = person,
            provider = Provider.Okta,
            emails = listOf("<EMAIL>", "<EMAIL>").map { EmailAddress.of(it) },
        )

        assertThat(store.getTrustedPersonEmails(personId = person.idValue)).containsExactlyInAnyOrder(
            EmailAddress.of("<EMAIL>"),
            EmailAddress.of("<EMAIL>"),
            EmailAddress.of("<EMAIL>"),
            EmailAddress.of("<EMAIL>"),
        )
    }

    @Test
    fun findCreatedBetween() = suspendingDatabaseTest {
        val now = Instant.nowWithMicrosecondPrecision()
        val personA = makePerson(createdAt = now.minus(10.days)).asDataModel()
        val personB = makePerson(createdAt = now.minus(5.days)).asDataModel()
        val personC = makePerson(createdAt = now.minus(1.days)).asDataModel()

        assertThat(store.findCreatedBetween(from = now.minus(12.days), to = now))
            .containsExactlyInAnyOrder(personA, personB, personC)

        assertThat(store.findCreatedBetween(from = now.minus(6.days), to = now))
            .containsExactly(personB, personC)

        assertThat(store.findCreatedBetween(from = now.minus(7.days), to = now.minus(2.days)))
            .containsExactly(personB)
    }

    @Nested
    inner class FindDuplicatePeopleTest {

        @Test
        fun `findDuplicatePeople returns empty when no duplicates`() = suspendingDatabaseTest(
            configure = { slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false) },
        ) {
            val p1 = makePerson(primaryEmail = EmailAddress.of("<EMAIL>"))
            val p2 = makePerson(primaryEmail = EmailAddress.of("<EMAIL>"))
            val p3 = makePerson(primaryEmail = EmailAddress.of("<EMAIL>"))

            makeIdentity(person = p1, primaryEmail = EmailAddress.of("<EMAIL>"), emails = listOf(EmailAddress.of("<EMAIL>")))
            makeIdentity(person = p2, primaryEmail = EmailAddress.of("<EMAIL>"), emails = listOf(EmailAddress.of("<EMAIL>")))
            makeIdentity(person = p3, primaryEmail = EmailAddress.of("<EMAIL>"), emails = listOf(EmailAddress.of("<EMAIL>")))

            val groups = store.findDuplicatePeople()
            assertThat(groups).isEmpty()
        }

        @Test
        fun `findDuplicatePeople groups by email across distinct people`() = suspendingDatabaseTest(
            configure = { slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false) },
        ) {
            val p1 = makePerson(primaryEmail = EmailAddress.of("<EMAIL>"))
            val p2 = makePerson(primaryEmail = EmailAddress.of("<EMAIL>"))
            val p3 = makePerson(primaryEmail = EmailAddress.of("<EMAIL>"))

            makeIdentity(
                person = p1,
                primaryEmail = EmailAddress.of("<EMAIL>"),
                emails = listOf(EmailAddress.of("<EMAIL>"), EmailAddress.of("<EMAIL>")),
            )
            makeIdentity(
                person = p2,
                primaryEmail = EmailAddress.of("<EMAIL>"),
                emails = listOf(EmailAddress.of("<EMAIL>"), EmailAddress.of("<EMAIL>")),
            )
            makeIdentity(
                person = p3,
                primaryEmail = EmailAddress.of("<EMAIL>"),
                emails = listOf(EmailAddress.of("<EMAIL>")),
            )

            val groups = store.findDuplicatePeople()

            // Expect exactly one duplicate group <NAME_EMAIL> containing p1 and p2
            assertThat(groups).hasSize(1)
            val onlyGroup = groups.first().toSet()
            assertThat(onlyGroup).containsExactlyInAnyOrder(p1.idValue, p2.idValue)
        }

        @Test
        fun `findDuplicatePeople find multiple groups`() = suspendingDatabaseTest(
            configure = { slowQueryConfig = SlowQueryConfig(throwOnSlowQuery = false) },
        ) {
            val now = Instant.nowWithMicrosecondPrecision()
            val p1 = makePerson(primaryEmail = EmailAddress.of("<EMAIL>"), createdAt = now.minus(1.days))
            val p2 = makePerson(primaryEmail = EmailAddress.of("<EMAIL>"), createdAt = now.minus(2.days))
            val p3 = makePerson(primaryEmail = EmailAddress.of("<EMAIL>"), createdAt = now.minus(4.days))
            val p4 = makePerson(primaryEmail = EmailAddress.of("<EMAIL>"), createdAt = now.minus(3.days))
            val p5 = makePerson(primaryEmail = EmailAddress.of("<EMAIL>"))

            // Group 1: <EMAIL> across p1 and p2
            makeIdentity(
                person = p1,
                primaryEmail = EmailAddress.of("<EMAIL>"),
                emails = listOf(EmailAddress.of("<EMAIL>"), EmailAddress.of("<EMAIL>")),
            )
            makeIdentity(
                person = p2,
                primaryEmail = EmailAddress.of("<EMAIL>"),
                emails = listOf(EmailAddress.of("<EMAIL>"), EmailAddress.of("<EMAIL>")),
            )

            // Group 2: <EMAIL> across p3 and p4
            makeIdentity(
                person = p3,
                primaryEmail = EmailAddress.of("<EMAIL>"),
                emails = listOf(EmailAddress.of("<EMAIL>"), EmailAddress.of("<EMAIL>")),
            )
            makeIdentity(
                person = p4,
                primaryEmail = EmailAddress.of("<EMAIL>"),
                emails = listOf(EmailAddress.of("<EMAIL>"), EmailAddress.of("<EMAIL>")),
            )

            // Non-duplicate
            makeIdentity(
                person = p5,
                primaryEmail = EmailAddress.of("<EMAIL>"),
                emails = listOf(EmailAddress.of("<EMAIL>")),
            )

            // Check that there are exactly two groups
            val groups = store.findDuplicatePeople()
            assertThat(groups).hasSize(2)

            // Check that the groups are correctly ordered by oldest person created at timestamp
            assertThat(groups).containsExactlyInAnyOrder(
                listOf(p2.idValue, p1.idValue),
                listOf(p3.idValue, p4.idValue),
            )
        }
    }
}
