package com.nextchaptersoftware.db.common.functions

import org.jetbrains.exposed.sql.CustomFunction
import org.jetbrains.exposed.sql.Expression
import org.jetbrains.exposed.sql.QueryBuilder
import org.jetbrains.exposed.sql.TextColumnType

/**
 * Exposes PostgreSQL json_array_elements_text as an Exposed expression.
 *
 * Renders: json_array_elements_text(<expr>::json)
 *
 * Note: This is a set-returning function (SRF). Use it in a SELECT list or FROM context
 * (e.g., via a subselect/CTE or lateral) so Postgres can expand rows.
 */
class JsonArrayElementsText(
    private val jsonTextExpr: Expression<*>,
) : CustomFunction<String>(
    functionName = "json_array_elements_text",
    columnType = TextColumnType(),
    jsonTextExpr,
) {
    override fun toQueryBuilder(queryBuilder: QueryBuilder) = queryBuilder {
        // Force cast to json
        append("json_array_elements_text(")
        append(jsonTextExpr)
        append("::json)")
    }
}

fun Expression<String?>.jsonArrayElementsText(): Expression<String> = JsonArrayElementsText(this)
