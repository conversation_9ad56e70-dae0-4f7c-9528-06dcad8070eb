import { AuthSidebar } from '@shared/ide/components';
import { AskSidebar } from '@shared/ide/sidebar/MainSidebar/AskSidebar';
import { MyQuestionsSidebar } from '@shared/ide/sidebar/MainSidebar/MyQuestionSidebar';
import { useStream } from '@shared/stores/DataCacheStream';
import { useStore } from '@shared/stores/useStore';
import { Loading } from '@shared/webComponents/Loading/Loading';
import { OfflineView } from '@shared/webComponents/OfflineView/OfflineView';
import { TeamDialogBlockers } from '@shared/webComponents/TeamDialogBlockers/TeamDialogBlockers';

import { DeprecatedClient } from '../components/DeprecatedClient/DeprecatedClient';
import { DiscussionThread } from '../insight/DiscussionThread/DiscussionThread';
import { InstallationView } from '../onboarding/InstallationView';
import { MissingReposSidebar } from '../sidebar/MissingReposSidebar/MissingReposSidebar';
import { PendingSidebar } from '../sidebar/PendingSidebar/PendingSidebar';
import { RequireSSO } from '../SSO/RequireSSO';
import { IDEViewRouterKey, IDEViewRouterStoreTraits } from './IDEViewRouterStoreTypes';
import { ViewRouterViewState } from './ViewRouterTypes';

function ViewRouterContent({ state }: { state: ViewRouterViewState | undefined }) {
    if (!state) {
        return null;
    }

    switch (state.$case) {
        case 'ask':
            return <AskSidebar />;
        case 'myQuestions':
            return <MyQuestionsSidebar />;
        case 'discussion':
            return (
                <TeamDialogBlockers teamId={state.discussionThreadState.key.teamId}>
                    <DiscussionThread
                        discussionKey={state.discussionThreadState.key}
                        targetUI={state.discussionThreadState.targetUI}
                    />
                </TeamDialogBlockers>
            );
        case 'auth':
            return <AuthSidebar />;
        case 'deprecated':
            return <DeprecatedClient />;
        case 'install':
            return <InstallationView />;
        case 'missingRepos':
            return <MissingReposSidebar />;
        case 'pending':
            return <PendingSidebar />;
        case 'loading':
            return <Loading centerAlign />;
        case 'requireSSO':
            return <RequireSSO />;
        case 'offline':
            return <OfflineView />;
        case 'empty':
            return null;
    }
}

export function IDEViewRouter({ key }: { key: IDEViewRouterKey }) {
    const store = useStore(IDEViewRouterStoreTraits, { key });
    const state = useStream(() => store.stream, [store]);

    return <ViewRouterContent state={state} />;
}
