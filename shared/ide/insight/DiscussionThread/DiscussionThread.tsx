import classNames from 'classnames';
import { RefObject, useEffect, useMemo, useRef, useState } from 'react';

import {
    DiscussionThreadData,
    DiscussionThreadStoreKey,
    DiscussionThreadTargetUI,
} from '@shared/ide/insight/DiscussionThread/DiscussionThreadTypes';
import { useStream } from '@shared/stores/DataCacheStream';
import { PersonStoreTraits } from '@shared/stores/PersonStoreTypes';
import { ThreadInfoAggregate } from '@shared/stores/ThreadInfoAggregate';
import { ThreadStoreTraits } from '@shared/stores/ThreadStoreTypes';
import { useStore } from '@shared/stores/useStore';
import { AnimatingHeightSizer } from '@shared/webComponents/AnimatingHeightSizer/AnimatingHeightSizer';
import { Banner } from '@shared/webComponents/Banner/Banner';
import { Button } from '@shared/webComponents/Button/Button';
import { ExpertButton } from '@shared/webComponents/Button/ExpertButton';
import { LoopInExpertsList } from '@shared/webComponents/Experts/LoopInExpertsList';
import { ExternalLink } from '@shared/webComponents/ExternalLink/ExternalLink';
import { Loading } from '@shared/webComponents/Loading/Loading';
import { MessageInput } from '@shared/webComponents/MessageInput/MessageInput';
import { ScrollContextProvider } from '@shared/webComponents/ScrollContext';
import { ScrollToBottomButton } from '@shared/webComponents/ScrollToBottomButton/ScrollToBottomButton';
import { SaveInput } from '@shared/webComponents/TextInput/SaveInput';
import { ArchivedText } from '@shared/webComponents/ThreadArchivedText/ThreadArchivedText';
import { ThreadSensitiveDataBanner } from '@shared/webComponents/ThreadSensitiveDataBanner/ThreadSensitiveDataBanner';
import { ThreadView } from '@shared/webComponents/ThreadView/ThreadView';
import { ThreadViewContextMenu } from '@shared/webComponents/ThreadView/ThreadViewContextMenu';
import { ToggleThreadPrivacyDropdown } from '@shared/webComponents/ToggleThreadPrivacyDropdown/ToggleThreadPrivacyDropdown';
import { findPersonTeamMember } from '@shared/webUtils/TeamMemberUtils';
import { canEditThreadTitle } from '@shared/webUtils/ThreadUtils';

import { MarkReference } from '../../../webComponents/MarkReference';
import { InsightHeader } from '../InsightHeader/InsightHeader';

import './DiscussionThread.scss';

interface Props {
    targetUI?: DiscussionThreadTargetUI;
    shouldRenderLoading?: boolean;
    shouldRenderHeader?: boolean;
    discussionKey: DiscussionThreadStoreKey;
}

interface DiscussionThreadBodyProps extends DiscussionThreadData {
    animateLaunch?: boolean;
    shouldRenderHeader: boolean;
}

const DiscussionThreadHeader = ({ threadInfo }: DiscussionThreadBodyProps) => {
    const store = useStore(ThreadStoreTraits, { teamId: threadInfo.thread.teamId, threadId: threadInfo.thread.id });
    const [editingTitle, setEditingTitle] = useState<boolean>(false);
    const prInfo = useMemo(() => {
        if (!threadInfo.pullRequest) {
            return null;
        }

        const { number, title: prTitle, htmlUrl: prUrl } = threadInfo.pullRequest;

        const commentUrl =
            threadInfo.messages[0]?.links.externalMessageUrl || threadInfo.thread.links.externalUrl || prUrl;

        return (
            <span>
                {prTitle}&nbsp;
                <ExternalLink className="discussion_thread__pr_number" href={commentUrl} withIcon={false}>
                    #{number}
                </ExternalLink>
            </span>
        );
    }, [threadInfo]);

    const slackInfo = useMemo(() => {
        if (!threadInfo.slack) {
            return null;
        }
        return (
            <span>
                <a
                    className="discussion_thread__slack_channel"
                    href={threadInfo.messages[0]?.links.externalMessageUrl || threadInfo.thread.links.externalUrl}
                >
                    #{threadInfo.slack.channelName}
                </a>
            </span>
        );
    }, [threadInfo]);

    const menu = (
        <ThreadViewContextMenu
            threadInfo={threadInfo}
            restoreThread={() => store.confirmRestore()}
            archiveThread={() => store.confirmArchive()}
            enableEditTitle={canEditThreadTitle(threadInfo) ? () => setEditingTitle(true) : undefined}
        />
    );

    const subheader = useMemo(() => {
        if (threadInfo.pullRequest) {
            return prInfo;
        }

        if (threadInfo.slack) {
            return slackInfo;
        }

        return null;
    }, [threadInfo, prInfo, slackInfo]);

    return editingTitle ? (
        <SaveInput
            placeholder={threadInfo.thread.title}
            value={threadInfo.thread.title}
            onSubmitPromise={async (title: string) => {
                await store.updateTitle(title);
                setEditingTitle(false);
            }}
            onCancel={() => setEditingTitle(false)}
            disabled={(title: string) => title === threadInfo.thread.title}
            autoFocus
            fullWidth
            autoSelect
        />
    ) : (
        <InsightHeader
            title={threadInfo.thread.title}
            titleTag={threadInfo.thread.title}
            subheader={subheader}
            menu={menu}
            share={<ToggleThreadPrivacyDropdown teamId={threadInfo.thread.teamId} threadId={threadInfo.thread.id} />}
        />
    );
};

const DiscussionThreadBody = (
    props: DiscussionThreadBodyProps & { scrollRef: RefObject<HTMLDivElement>; contentRef: RefObject<HTMLDivElement> }
) => {
    const personStore = useStore(PersonStoreTraits, {});
    const person = useStream(() => personStore.person, [personStore]);
    const { threadInfo, shouldRenderHeader } = props;
    const { thread, messages, experts } = threadInfo;
    const [initialPrivacyState] = useState<boolean | undefined>(threadInfo.thread.isPrivate);
    const [showSensitiveContentBanner, setShowSensitiveContentBanner] = useState<boolean>(false);

    useEffect(() => {
        // Banner is only shown when thread starts off public but is transitioned to private due to sensitive source data
        if (
            !initialPrivacyState &&
            threadInfo.thread.isPrivate &&
            threadInfo.sensitiveDataSources &&
            threadInfo.sensitiveDataSources.length > 0
        ) {
            setShowSensitiveContentBanner(true);
        }
    }, [initialPrivacyState, threadInfo]);
    const currentTeamMember = useMemo(
        () => findPersonTeamMember(person, thread.teamId, thread.provider),
        [person, thread]
    );

    const anchorSourceMarkBlock = useMemo(() => {
        if (!threadInfo.mark) {
            return null;
        }

        return <MarkReference threadInfo={threadInfo} variant="secondary" filePathDisplay="base" />;
    }, [threadInfo]);

    const threadClass = classNames({
        discussion_thread: true,
        'discussion_thread--animate-launch': props.animateLaunch,
    });

    const banner = useMemo(() => {
        if (threadInfo.thread.archivedAt) {
            return <RestorationBanner threadInfo={threadInfo} />;
        }
        if (showSensitiveContentBanner) {
            return (
                <ThreadSensitiveDataBanner
                    threadInfo={threadInfo}
                    closeBanner={() => setShowSensitiveContentBanner(false)}
                />
            );
        }
        return null;
    }, [threadInfo, showSensitiveContentBanner]);

    const bannerContainer = useMemo(() => {
        return (
            <AnimatingHeightSizer isShown={!!banner}>
                <div className="discussion_thread__banner">{banner}</div>
            </AnimatingHeightSizer>
        );
    }, [banner]);

    return (
        <div className={threadClass}>
            <div className="discussion_thread__content">
                {bannerContainer}
                {shouldRenderHeader && <DiscussionThreadHeader {...props} />}
                <div className="discussion_thread__thread_section" ref={props.scrollRef}>
                    <div ref={props.contentRef}>
                        {anchorSourceMarkBlock}
                        <ThreadView thread={threadInfo} messages={messages} provider={threadInfo.thread.provider} />
                    </div>
                </div>

                {threadInfo.capabilities.canReply ? (
                    <div className="discussion_thread__footer">
                        <ScrollToBottomButton />
                        <div className="discussion_thread__reply_section">
                            <MessageInput
                                teamId={thread.teamId}
                                threadId={thread.id}
                                className="discussion_thread__primary_input"
                            />
                        </div>
                        <LoopInExpertsList
                            teamId={thread.teamId}
                            experts={experts}
                            currentTeamMemberId={currentTeamMember?.id}
                            threadAuthorId={messages[0].authorTeamMemberId}
                            buttonTemplate={(expert) => (
                                <ExpertButton key={expert.id} expert={expert} variant="secondary" />
                            )}
                        />
                    </div>
                ) : (
                    <div className="discussion_thread__bottom_button_container">
                        <ScrollToBottomButton />
                    </div>
                )}
            </div>
        </div>
    );
};

const RestorationBanner = ({ threadInfo }: { threadInfo: ThreadInfoAggregate }) => {
    const store = useStore(ThreadStoreTraits, { teamId: threadInfo.thread.teamId, threadId: threadInfo.thread.id });
    const thread = threadInfo.thread;
    const archivedAt = thread.archivedAt;
    const archivedBy = thread.archivedBy;
    if (!archivedAt) {
        return null;
    }

    const restoreButton = (
        <Button as="link" onClick={() => store.confirmRestore()}>
            Restore Discussion
        </Button>
    );

    return (
        <Banner icon={false} actions={restoreButton}>
            <ArchivedText
                archivedBy={archivedBy}
                archivedAt={archivedAt}
                teamParticipants={threadInfo.teamParticipants}
            />
        </Banner>
    );
};

export const DiscussionThread = ({
    discussionKey: key,
    shouldRenderLoading,
    targetUI,
    shouldRenderHeader = true,
}: Props) => {
    const scrollRef = useRef<HTMLDivElement>(null);
    const contentRef = useRef<HTMLDivElement>(null);

    const store = useStore(ThreadStoreTraits, { teamId: key.teamId, threadId: key.threadId });

    const state = useStream(() => store.stream, [store], { $case: 'loading' });

    switch (state.$case) {
        case 'loading':
            return shouldRenderLoading ? <Loading /> : null;
        case 'ready':
            const threadInfo = state.value;
            return (
                <ScrollContextProvider key={key.threadId} scrollElement={scrollRef} contentElement={contentRef}>
                    <DiscussionThreadBody
                        teamId={key.teamId}
                        threadInfo={threadInfo}
                        shouldRenderHeader={shouldRenderHeader}
                        animateLaunch={targetUI === 'sidebar'}
                        scrollRef={scrollRef}
                        contentRef={contentRef}
                    />
                </ScrollContextProvider>
            );
    }
};
