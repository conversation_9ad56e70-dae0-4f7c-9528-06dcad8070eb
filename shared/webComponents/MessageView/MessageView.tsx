import classNames from 'classnames';
import { CSSProperties, forwardRef, Ref, useCallback, useEffect, useMemo, useState } from 'react';

import { FeedbackType, MessageAggregate, MessageDataSourcePreset, Provider } from '@shared/api/generatedExtraApi';

import { DateTime, generateIconProtocol } from '@shared/webUtils';
import { HandleInlineSourceFileReference } from '@shared/webUtils/MessageReferenceUtils';

import { ClientWorkspace } from '../ClientWorkspace/ClientWorkspace';
import { Icon } from '../Icon/Icon';
import { RelativeTime } from '../RelativeTime/RelativeTime';
import { useScrollContext } from '../ScrollContext';
import { UserIcon } from '../UserIcon/UserIcon';
import { MessageActions } from './MessageActions';
import { MessageAttachments } from './MessageAttachments';
import { MessageContentBody } from './MessageContentBody';
import { MessageDataSourcePresetTooltip } from './MessageDataSourcePresetTooltip';
import { MessageFeedbackButtons } from './MessageFeedbackButtons';
import { MessageFeedbackDescriptions } from './MessageFeedbackDescriptions';
import { MessageFollowupSuggestions } from './MessageFollowupSuggestions';
import { MessageReferenceList } from './MessageReferenceList';

import './MessageView.scss';

interface Props {
    teamId: string;
    threadId: string;
    message: MessageAggregate;
    index: number;
    onNavigateAuthor?: (teamMemberId: string) => void;
    provider: Provider | undefined;
    lastMessageBanner?: React.ReactNode;
}

export interface MessageViewFeedbackRequest {
    messageId: string;
    feedbackType: FeedbackType | undefined;
    description?: string | undefined;
}

const MessageViewBase = (
    { teamId, threadId, index, message, onNavigateAuthor, provider, lastMessageBanner }: Props,
    ref: Ref<HTMLDivElement>
) => {
    const [isEditing, setIsEditing] = useState(false);

    const feedback = message.feedbackTeamMembers;

    const message_classnames = classNames({
        message_view: true,
        message_view__author_navigable: onNavigateAuthor,
    });

    const message_style: CSSProperties | undefined =
        index !== undefined
            ? ({
                  '--animation-order': index,
              } as CSSProperties)
            : undefined;

    // True if we are currently rendering with interpolation
    const [isInterpolating, setIsInterpolating] = useState(false);

    const isProcessing = message.state === 'loading' || isInterpolating;

    const { isAddingContent } = useScrollContext();

    // When loading or streaming, keep the expanded spacer size constant, if we
    // added one.
    useEffect(() => {
        if (isProcessing) {
            isAddingContent(true);

            return () => isAddingContent(false);
        }
    }, [isProcessing, isAddingContent]);

    const actions = useMemo(() => {
        return (
            <MessageActions
                index={index}
                teamId={teamId}
                threadId={threadId}
                message={message}
                provider={provider}
                onEdit={() => setIsEditing(true)}
            />
        );
    }, [index, message, provider, teamId, threadId]);

    const feedbackBlock = useMemo(() => {
        if (!feedback || isProcessing) {
            return null;
        }

        return (
            <div className="message_view__feedback_container">
                <div className="message_view__feedback">
                    <span>This was:</span>
                    <MessageFeedbackButtons
                        teamId={teamId}
                        threadId={threadId}
                        messageId={message.id}
                        feedback={feedback}
                    />
                </div>
                <MessageFeedbackDescriptions feedback={feedback} />
            </div>
        );
    }, [feedback, isProcessing, teamId, threadId, message.id]);

    const suggestionsBlock = useMemo(() => {
        if (!message.suggestions || isProcessing) {
            return null;
        }

        return <MessageFollowupSuggestions teamId={teamId} threadId={threadId} suggestions={message.suggestions} />;
    }, [message.suggestions, isProcessing, teamId, threadId]);

    const referencesBlock = useMemo(() => {
        if (!message.referenceAggregates || message.referenceAggregates.length === 0 || isInterpolating) {
            return null;
        }

        return (
            // Hack to get the tutorial to align correctly
            <div className="message_reference__parent">
                <MessageReferenceList
                    teamId={teamId}
                    threadId={threadId}
                    messageId={message.id}
                    messageReferences={message.referenceAggregates}
                    messageDataSourcePreset={message.dataSourcePreset}
                />
            </div>
        );
    }, [message.referenceAggregates, message.id, message.dataSourcePreset, isInterpolating, teamId, threadId]);

    const lastMessageBannerBlock = useMemo(() => {
        if (!lastMessageBanner || isInterpolating || message.state === 'loading') {
            return null;
        }
        return lastMessageBanner;
    }, [lastMessageBanner, isInterpolating, message.state]);

    const onNavigateLink = useCallback(
        (url: string) => {
            if (!message.references) {
                ClientWorkspace.instance().handleAction({
                    $case: 'openUrl',
                    url,
                });
                return;
            }
            HandleInlineSourceFileReference(message.references, url);
        },
        [message.references]
    );

    const body = useMemo(() => {
        return (
            <>
                <div className="message_view__content">
                    <MessageContentBody
                        messageId={message.id}
                        teamId={teamId}
                        threadId={threadId}
                        messageContent={message.messageContent}
                        isEditing={isEditing}
                        onEditCancel={() => setIsEditing(false)}
                        onPostSubmit={() => setIsEditing(false)}
                        autofocus
                        onNavigateLink={onNavigateLink}
                        messageState={message.state}
                        isStreaming={message.isStreaming}
                        setIsInterpolating={setIsInterpolating}
                        initialAttachments={message.attachments}
                    />
                    {referencesBlock}
                    {suggestionsBlock}
                    {feedbackBlock}
                </div>

                {lastMessageBannerBlock}
            </>
        );
    }, [
        message.id,
        message.messageContent,
        message.state,
        message.isStreaming,
        message.attachments,
        teamId,
        threadId,
        isEditing,
        onNavigateLink,
        referencesBlock,
        suggestionsBlock,
        feedbackBlock,
        lastMessageBannerBlock,
    ]);

    const onNavigate = onNavigateAuthor ? () => onNavigateAuthor(message.author.id) : undefined;

    return (
        <div className={message_classnames} style={message_style} id={message.id} ref={ref}>
            {message.dataSourcePreset && message.dataSourcePreset.avatarUrl ? (
                <MessageDataSourcePresetIconTooltip
                    teamId={teamId}
                    messageDataSourcePreset={message.dataSourcePreset}
                    avatarUrl={message.dataSourcePreset.avatarUrl}
                />
            ) : (
                <UserIcon
                    className="message_view__icon"
                    user={generateIconProtocol(message.author)}
                    size="xLarge"
                    onClick={onNavigate}
                />
            )}
            <div className="message_view__author" onClick={onNavigate}>
                {message.author.identity.displayName}
            </div>
            <div className="message_view__timestamp">
                <RelativeTime date={message.createdAt} />
                {message.editedAt && (
                    <span className="message_view__edited" title={DateTime.absolute(message.editedAt)}>
                        (edited)
                    </span>
                )}
            </div>
            <div className="message_view__actions">{actions}</div>
            {message.attachments && message.attachments.length > 0 && !isEditing && (
                <div className="message_view__attachments">
                    <MessageAttachments
                        teamId={teamId}
                        attachments={message.attachments.map((attachment) => ({
                            key: attachment.asset.id,
                            $case: 'ready',
                            value: attachment,
                        }))}
                    />
                </div>
            )}
            <div className="message_view__message">{body}</div>
        </div>
    );
};

const MessageDataSourcePresetIconTooltip = ({
    teamId,
    messageDataSourcePreset,
    avatarUrl,
}: {
    teamId: string;
    messageDataSourcePreset: MessageDataSourcePreset;
    avatarUrl: string;
}) => {
    return (
        <MessageDataSourcePresetTooltip
            teamId={teamId}
            messageDataSourcePreset={messageDataSourcePreset}
            className="message_view__preset_icon__container"
        >
            {({ ref }) => <Icon ref={ref} icon={avatarUrl} className="message_view__preset_icon" size="xLarge" />}
        </MessageDataSourcePresetTooltip>
    );
};

export const MessageView = forwardRef(MessageViewBase);
