import { MessageAggregate } from '@shared/api/generatedExtraApi';

// import { Icon, IconSrc } from '../Icon/Icon';
import { Provider } from '../../api/models';
import { MessageViewContextMenu } from './MessageViewContextMenu';

import './MessageActions.scss';

interface Props {
    teamId: string;
    threadId: string;
    message: MessageAggregate;
    index: number;
    provider: Provider | undefined;
    onEdit?: () => void;
}

export const MessageActions = (props: Props) => {
    return (
        <div className="message_actions">
            <MessageViewContextMenu {...props} />
        </div>
    );
};
