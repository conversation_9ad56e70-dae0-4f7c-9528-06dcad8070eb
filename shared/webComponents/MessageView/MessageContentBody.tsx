import { useCallback, useEffect, useMemo, useState } from 'react';

import { MessageAttachment, MessageContent, MessageState } from '../../api/models';
import { MessageTransformer } from '../../webUtils';
import { MessageInput } from '../MessageInput/MessageInput';
import { MessageViewLoading } from './MessageViewLoading';
import { MessageBlockRenderer } from './renderers';
import { InterpolatedBlockRenderer } from './renderers/InterpolatedBlockRenderer';

import './MessageContentBody.scss';

interface BaseProps {
    messageId: string;
    teamId: string;
    threadId: string;
    messageContent: MessageContent;
    onNavigateLink?: (url: string) => void;
    messageState?: MessageState;
    isStreaming?: boolean;
    initialAttachments?: MessageAttachment[];

    setIsInterpolating: (isInterpolating: boolean) => void;
}

type ReadOnlyProps = BaseProps & {
    readOnly: true;
};

type MutableProps = BaseProps & {
    readOnly?: never;
    isEditing: boolean;
    onEditCancel: () => void;
    onPostSubmit: () => void;
    autofocus?: boolean;
};

type Props = ReadOnlyProps | MutableProps;

export const MessageContentBody = ({
    messageId,
    teamId,
    threadId,
    messageContent,
    messageState,
    isStreaming,
    initialAttachments,
    onNavigateLink,
    setIsInterpolating,
    ...props
}: Props) => {
    const content = useMemo(
        () => MessageTransformer.fromBytesToMessage(messageContent.content),
        [messageContent.content]
    );

    const [renderInterpolated, setRenderInterpolated] = useState(false);

    useEffect(() => {
        // Enable interpolated rendering as soon as the message begins streaming once
        if (isStreaming) {
            setRenderInterpolated(true);
            setIsInterpolating(true);
        }
    }, [isStreaming, setIsInterpolating]);

    const onDoneInterpolation = useCallback(() => {
        setRenderInterpolated(false);
        setIsInterpolating(false);
    }, [setIsInterpolating]);

    const transformedContent = useMemo(() => {
        const blocks = content.blocks;

        if (renderInterpolated) {
            return (
                <InterpolatedBlockRenderer
                    blocks={blocks}
                    teamId={teamId}
                    isStreaming={isStreaming ?? false}
                    onNavigateLink={onNavigateLink}
                    onDone={onDoneInterpolation}
                />
            );
        }

        return blocks.map((block, index) => (
            <MessageBlockRenderer key={index} teamId={teamId} block={block} onNavigateLink={onNavigateLink} />
        ));
    }, [teamId, content.blocks, renderInterpolated, isStreaming, onNavigateLink, onDoneInterpolation]);

    if (messageState === 'loading' && !isStreaming) {
        return (
            <div className="message_content_body">
                <div className="message_content_body--loading">
                    <MessageViewLoading /> {transformedContent}
                </div>
            </div>
        );
    }

    switch (props.readOnly) {
        case true:
            return <div className="message_content_body">{transformedContent}</div>;
        default:
            const { isEditing, onEditCancel, onPostSubmit, autofocus } = props;

            if (!isEditing) {
                return <div className="message_content_body">{transformedContent}</div>;
            }

            return (
                <div className="message_content_body">
                    <MessageInput
                        threadId={threadId}
                        messageId={messageId}
                        autofocus={autofocus}
                        onPostSubmit={onPostSubmit}
                        onCancel={onEditCancel}
                        teamId={teamId}
                        initialContent={content.blocks}
                        initialAttachments={initialAttachments}
                    />
                </div>
            );
    }
};
