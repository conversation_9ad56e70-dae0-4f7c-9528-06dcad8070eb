import { MouseEvent, useCallback } from 'react';

import { FollowOnSuggestion, MessageSuggestions } from '@shared/api/models';

import { getStore } from '@shared/stores/getStore';
import { ThreadStoreTraits } from '@shared/stores/ThreadStoreTypes';

import { faPaperPlaneTop } from '@fortawesome/pro-regular-svg-icons/faPaperPlaneTop';

import { ContextRow } from '../ContextRow/ContextRow';
import { Icon } from '../Icon/Icon';
import { RowsList } from '../Rows/RowsList';
import { useScrollContext } from '../ScrollContext';
import { ScrollForFollowOnQuestion } from './MessageViewUtils';

import './MessageFollowupSuggestions.scss';

interface Props {
    teamId: string;
    threadId: string;
    suggestions?: MessageSuggestions;
}

export const MessageFollowupSuggestions = ({ teamId, threadId, suggestions }: Props) => {
    const scrollContext = useScrollContext();

    const askSuggestion = useCallback(
        async (query: FollowOnSuggestion, e: MouseEvent) => {
            // Note that we pass in the target (row) client height here, to offset when scrolling.
            // This is needed for suggested questions, since the row is going to be removed immediately
            // when we ask the question
            ScrollForFollowOnQuestion(scrollContext, e.currentTarget.clientHeight);

            const store = getStore(ThreadStoreTraits, { teamId, threadId });
            await store.addMessageForSuggestedQuestion({
                query: query.question,
                suggestedQuestionId: query.questionId,
            });
        },
        [scrollContext, teamId, threadId]
    );

    const followupSuggestions = suggestions?.followOn.filter((followOn) => {
        switch (followOn.hasBeenAsked) {
            case true:
            case undefined:
                return false;
            case false:
                return true;
        }
    });

    if (!followupSuggestions?.length) {
        return null;
    }

    return (
        <div className="message_view__followup">
            <div className="message_view__followup__label">Suggested follow-up questions:</div>

            <RowsList className="message_view__followup__suggestions">
                {followupSuggestions.map((suggestion, idx) => (
                    <ContextRow
                        key={idx}
                        as="button"
                        className="message_view__followup__suggestion"
                        rightContent={
                            <Icon
                                className="message_view__followup__suggestion_icon"
                                icon={faPaperPlaneTop}
                                size="small"
                            />
                        }
                        onClick={(e) => askSuggestion(suggestion, e)}
                    >
                        {suggestion.question}
                    </ContextRow>
                ))}
            </RowsList>
        </div>
    );
};
