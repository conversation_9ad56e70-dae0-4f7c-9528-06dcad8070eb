import { useCallback } from 'react';

import { MessageAggregate, Provider } from '@shared/api/generatedExtraApi';

import { getStore } from '@shared/stores/getStore';
import { ThreadStoreTraits } from '@shared/stores/ThreadStoreTypes';
import { CopyToClipboard } from '@shared/webUtils/CopyUtils';
import { RenderMessageMarkdown } from '@shared/webUtils/MarkdownRenderer';
import { getViewInProvider } from '@shared/webUtils/ProviderUtils';

import { faEllipsisH } from '@fortawesome/pro-regular-svg-icons/faEllipsisH';

import { useSetString } from '../../hooks/useSetString';
import { ArrayUtils, DashboardUrls, MessageTransformer } from '../../webUtils';
import { ClientWorkspace } from '../ClientWorkspace/ClientWorkspace';
import { ContextMenuItem } from '../ContextMenuProvider/ContextMenuItem';
import { DropdownContextMenu } from '../ContextMenuProvider/DropdownContextMenu';
import { DropdownDivider, DropdownItem } from '../Dropdown/Dropdown';
import { Icon } from '../Icon/Icon';

interface Props {
    teamId: string;
    threadId: string;
    index: number;
    provider: Provider | undefined;
    message: MessageAggregate;
    onEdit?: () => void;
}

export const MessageViewContextMenu = ({ teamId, threadId, index, provider, message, onEdit }: Props) => {
    const dashboardUrl = index === 0 ? DashboardUrls.thread(teamId, threadId) : message.links.dashboardUrl;
    const externalMessageUrl = message.links.externalMessageUrl;

    const onCopyLinkCallback = useCallback(() => {
        if (dashboardUrl) {
            CopyToClipboard(dashboardUrl);
        }
    }, [dashboardUrl]);

    const { text: copyLinkText, onChange: onCopyLink } = useSetString(onCopyLinkCallback, {
        base: 'Copy Link',
        result: 'Copied!',
    });

    const onCopyMessageContentCallback = useCallback(() => {
        const content = MessageTransformer.fromBytesToMessage(message.messageContent.content);
        const markdown = RenderMessageMarkdown(content.blocks);
        CopyToClipboard(markdown);
    }, [message.messageContent.content]);

    const onArchiveThread = useCallback(
        () => getStore(ThreadStoreTraits, { teamId, threadId }).confirmArchive(),
        [teamId, threadId]
    );

    const onDeleteMessage = useCallback(async () => {
        const store = getStore(ThreadStoreTraits, { teamId, threadId });
        await store.confirmDeleteMessage(message.id);
    }, [teamId, threadId, message.id]);

    const { text: copyMessageText, onChange: onCopyMessageContent } = useSetString(onCopyMessageContentCallback, {
        base: 'Copy Message',
        result: 'Copied!',
    });

    const firstMessage = index === 0;

    const mutatingOptions = ArrayUtils.compact([
        message.canEdit && !!onEdit
            ? {
                  item: {
                      id: 'edit',
                      title: 'Edit Question',
                  },
                  callback: onEdit,
                  dropdownItem: (
                      <DropdownItem onClick={onEdit} key="archive">
                          Edit Question
                      </DropdownItem>
                  ),
              }
            : null,

        firstMessage
            ? {
                  item: {
                      id: 'archive',
                      title: 'Archive Discussion',
                  },
                  callback: onArchiveThread,
                  dropdownItem: (
                      <DropdownItem textStyle="danger" onClick={onArchiveThread} key="archive">
                          Archive Discussion
                      </DropdownItem>
                  ),
              }
            : null,
        message.canDelete && !firstMessage
            ? {
                  item: {
                      id: 'delete',
                      title: 'Delete Message',
                  },
                  callback: onDeleteMessage,
                  dropdownItem: (
                      <DropdownItem textStyle="danger" onClick={onDeleteMessage} key="delete">
                          Delete Message
                      </DropdownItem>
                  ),
              }
            : null,
    ]);

    const items: ContextMenuItem[] = ArrayUtils.compact([
        dashboardUrl
            ? {
                  item: {
                      id: 'copyDashboardUrl',
                      title: copyLinkText,
                  },
                  callback: onCopyLink,
                  dropdownItem: (
                      <DropdownItem key={`copyLink`} onClick={onCopyLink}>
                          {copyLinkText}
                      </DropdownItem>
                  ),
              }
            : null,
        message.messageContent
            ? {
                  item: {
                      id: 'copyDashboardMessage',
                      title: copyMessageText,
                  },
                  callback: onCopyMessageContent,
                  dropdownItem: (
                      <DropdownItem key={`copyContent`} onClick={onCopyMessageContent}>
                          {copyMessageText}
                      </DropdownItem>
                  ),
              }
            : null,
        externalMessageUrl
            ? {
                  item: {
                      id: 'externalLink',
                      title: getViewInProvider(provider),
                  },
                  callback: () => {
                      ClientWorkspace.instance().handleAction({
                          $case: 'openUrl',
                          url: externalMessageUrl,
                      });
                  },
                  dropdownItem: (
                      <DropdownItem
                          key={`externalLink`}
                          as="a"
                          href={externalMessageUrl}
                          target="_blank"
                          rel="noreferrer"
                      >
                          {getViewInProvider(provider)}
                      </DropdownItem>
                  ),
              }
            : null,

        mutatingOptions.length > 0
            ? {
                  item: {
                      id: 'separator',
                      title: '',
                      itemType: 'separator',
                  },
                  dropdownItem: <DropdownDivider key="separator" />,
              }
            : null,
        ...mutatingOptions,
    ]);

    return (
        <DropdownContextMenu
            header={<Icon size="small" icon={faEllipsisH} />}
            className="message_view__context_menu"
            withCaret={false}
            portal
            placement="bottom-end"
            items={items}
        />
    );
};
