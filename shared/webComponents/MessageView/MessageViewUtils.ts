import { logger } from '@shared/webUtils/log';

import { ScrollContextProps } from '../ScrollContext';

const log = logger('MessageViewUtils');

/**
 * Scroll a message listing so that the view is ready to display a follow-on question.
 * This finds the last message content (.message_view__content) and scrolls so that element is
 * above the top edge of the scroll view.
 */
export function ScrollForFollowOnQuestion(scrollContext: ScrollContextProps, offsetToRemove?: number) {
    const nodes = document.querySelectorAll('.message_view__content');
    if (nodes.length > 0) {
        const lastNode = nodes[nodes.length - 1];
        scrollContext.scrollBelowElement(lastNode, offsetToRemove);
    } else {
        log.warn('Could not scroll to bottom of message view, content not found');
    }
}
