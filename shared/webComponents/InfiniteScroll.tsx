import { CSSProperties, ReactNode } from 'react';

import { PaginationSource } from '@shared/stores/PaginationSource';
import { useInfiniteScroll } from '@shared/webComponents/ScrollContext';
import { Spinner } from '@shared/webComponents/Spinner/Spinner';

import './InfiniteScroll.scss';

interface Props {
    children: ReactNode;
    spinnerStyle?: CSSProperties;
}

export function InfiniteScrollImpl({ source, children, spinnerStyle }: Props & { source: PaginationSource }) {
    const showLoader = useInfiniteScroll(source);

    return (
        <>
            {children}
            {showLoader && (
                <div className="infinite_scroll__show_more" key="threadListShowMore" style={spinnerStyle}>
                    <Spinner size="small" />
                </div>
            )}
        </>
    );
}

/**
 * Provide infinite scrolling on the given pagination source
 */
export function InfiniteScroll({ source, children, spinnerStyle }: Props & { source?: PaginationSource }) {
    return source ? (
        <InfiniteScrollImpl source={source} spinnerStyle={spinnerStyle}>
            {children}
        </InfiniteScrollImpl>
    ) : (
        children
    );
}
