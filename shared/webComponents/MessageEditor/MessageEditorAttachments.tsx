import { forwardRef, useImperativeHandle, useMemo } from 'react';

import { MessageAttachment } from '@shared/api/generatedApi';

import { useStream } from '@shared/stores/DataCacheStream';
import { logger } from '@shared/webUtils/log';
import { StringsHelper } from '@shared/webUtils/StringsHelper/StringsHelper';
import { getUserVisibleError } from '@shared/webUtils/UserVisibleError';

import { AnimateContent } from '../Animation/AnimateContent';
import { ClientWorkspace } from '../ClientWorkspace/ClientWorkspace';
import { MessageAttachments } from '../MessageView/MessageAttachments';
import { MessageEditorAttachmentsStore } from './MessageEditorAttachmentsStore';

export interface MessageAttachmentsForwardedRef {
    addAttachment: (fileList: FileList) => void;
    getAttachments: () => MessageAttachment[];
    clearAttachments: () => void;

    setAttachments: (attachments: MessageAttachment[]) => void;
}

interface Props {
    teamId: string;
    threadId: string;
    messageId: string;

    onAttachmentsChanged?: () => void;
}

const log = logger('MessageEditorAttachments');

export const MessageEditorAttachments = forwardRef<MessageAttachmentsForwardedRef, Props>(
    function MessageAttachmentsImpl({ teamId, threadId, messageId, onAttachmentsChanged }, ref) {
        const store = useMemo(
            () => new MessageEditorAttachmentsStore(teamId, threadId, messageId, () => onAttachmentsChanged?.()),
            [teamId, threadId, messageId, onAttachmentsChanged]
        );

        const attachments = useStream(() => store.stream, [store], []);

        useImperativeHandle(ref, () => ({
            addAttachment: (files) => {
                // We don't want the button to run the async spinner
                const doAction = async () => {
                    try {
                        await store.add(files);
                    } catch (error) {
                        const userVisibleError = await getUserVisibleError(error);
                        if (userVisibleError && userVisibleError.title && userVisibleError.detail) {
                            void ClientWorkspace.instance().dialog({
                                title: userVisibleError.title,
                                description: userVisibleError.detail,
                            });
                        } else {
                            log.error('Could not add attachments', error);
                            void ClientWorkspace.instance().notify({
                                title: `An error occurred attaching the ${StringsHelper.count({ singular: 'file', plural: 'files' }, files.length)}.`,
                                type: 'error',
                            });
                        }
                    } finally {
                        onAttachmentsChanged?.();
                    }
                };
                void doAction();
            },
            getAttachments: () => store.get(),
            clearAttachments: () => store.clear(),

            setAttachments: (attachments) => store.set(attachments),
        }));

        return (
            <AnimateContent axis="height" animateIn clipContent={false}>
                {attachments.length > 0 && (
                    <MessageAttachments
                        teamId={teamId}
                        attachments={attachments}
                        onDelete={(attachment) => store.delete(attachment.key)}
                    />
                )}
            </AnimateContent>
        );
    }
);
