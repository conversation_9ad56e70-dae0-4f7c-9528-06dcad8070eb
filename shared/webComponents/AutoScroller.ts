// export class AutoScroller {

import { PromiseUtils } from '@shared/webUtils';
import { MS } from '@shared/webUtils/TimeUtils';

interface ScrollToEdgeOptions extends ScrollOptions {
    duration?: number;
}

/**
 * Manages animated vertical scrolling on a scrollable element.  This is used to hold all the scrolling state
 * for a ScrollContext.
 *
 * This class allows the following:
 *   * Animated scrolling to the top/bottom of an element, at a particular scrolling rate
 *   * "Stick to bottom" functionality, where new content added to the bottom of a view is auto-scrolled
 */
export class AutoScroller {
    private abortController?: AbortController;
    private onScrollEvent = () => this.onScroll();
    private contentResizeObserver = new ResizeObserver(() => this.onContentSizeChange());

    // When we are animating scrolling, we track the positions we've scrolled to, so that we ignore
    // scroll events to those positions
    private animatedScrollPositions = new Set<number>();

    private isAtBottom = true;

    private spacerHeight = 0;

    private isAddingContent_ = false;

    constructor(
        private scrollElement: HTMLElement,
        private contentElement: HTMLElement,
        private isAtBottomChanged: (isAtBottom: boolean) => void
    ) {
        scrollElement.addEventListener('scroll', this.onScrollEvent);
        this.contentResizeObserver.observe(contentElement);

        this.updateIsAtBottom();
    }

    // Called when view is unmounted, to stop element monitoring
    destroy() {
        this.scrollElement.removeEventListener('scroll', this.onScrollEvent);
        this.contentResizeObserver.disconnect();
    }

    private updateIsAtBottom() {
        const innerHeight = this.scrollElement.clientHeight;
        const scrollTop = this.scrollElement.scrollTop;
        const scrollHeight = this.scrollElement.scrollHeight;

        const isAtBottom = scrollHeight - (innerHeight + scrollTop) <= 10;
        this.setIsAtBottom(isAtBottom);
    }

    private setIsAtBottom(value: boolean) {
        if (value !== this.isAtBottom) {
            this.isAtBottom = value;
            this.isAtBottomChanged(value);
        }
    }

    private onScroll() {
        // Scroll events are always processed asynchronously.
        // This is important when the scroll position changes due to a change in the content size,
        // this timeout ensures that we always process scroll events *after* the content resize event,
        // which is required to handle auto-scrolling correctly.
        // Some browsers (Safari) don't issue any scroll events on content change, which is what the spec
        // says should happen.  Some browsers (Chromium-based) sometimes issue an unexpected scroll event
        // before the content size event.
        setTimeout(() => {
            // Update the is-at-bottom state
            if (!this.animatedScrollPositions.has(Math.round(this.scrollElement.scrollTop))) {
                this.updateIsAtBottom();
            }

            // Resize the min-height
            this.resizeHeightSpacerDown();
        }, 0);
    }

    // The web dashboard has a particular problem where the header is pinned on top of the
    // scrollable area, so we need to scroll past the header area to make sure
    // content is positioned below it.
    // We identify the header element for the dashboard by class.
    private getHeaderHeight() {
        const headerElement = document.querySelector('.auto_scroller__header_target');
        const headerHeight = Math.floor(headerElement?.clientHeight ?? 0);
        return headerHeight;
    }

    private resizeHeightSpacerDown() {
        if (this.abortController || this.isAddingContent_) {
            return;
        }

        // When the user scrolls, if we previously had extra spacing added, we will
        // clip the view to the new scroll position.  This "eats" the extra space.
        const headerHeight = this.getHeaderHeight();
        const scrollBottom = this.scrollElement.scrollTop + this.scrollElement.clientHeight;
        const spacerHeight = this.spacerHeight - headerHeight;

        if (spacerHeight - scrollBottom > 5) {
            const newSpacerHeight = scrollBottom;
            this.contentElement.style.minHeight = `${newSpacerHeight}px`;
        }
    }

    private async onContentSizeChange() {
        // As long as we aren't currently scrolling to top/bottom, update the "at bottom" state
        if (!this.abortController) {
            this.updateIsAtBottom();
        }
    }

    async scrollToTop(options?: ScrollToEdgeOptions) {
        await this.scrollTo(() => 0, options);
    }

    async scrollToBottom(options?: ScrollToEdgeOptions) {
        if (await this.scrollTo(() => this.scrollElement.scrollHeight - this.scrollElement.clientHeight, options)) {
            this.setIsAtBottom(true);
        }
    }

    private getRelativeElementOffset(element: Element) {
        const elementBox = element.getBoundingClientRect();
        const scrollBox = this.contentElement.getBoundingClientRect();

        return elementBox.top - scrollBox.top;
    }

    // Set to true if the scrolled UI is in the middle of adding content.
    // When this is true, we will not scale the spacer down when scrolling,
    // so that we don't snap to content size when streaming a message response.
    set isAddingContent(newValue: boolean) {
        this.isAddingContent_ = newValue;
    }

    // Scroll so that the given element appears immediately above the scrolled view
    async scrollBelowElement(element: Element, offsetToRemove?: number) {
        // Get the y-point immediately below the given element -- this is what we want
        // to position at the top of the view.
        const elementOffset = Math.floor(this.getRelativeElementOffset(element));
        const elementBottom = (elementOffset ?? element.clientTop) + element.clientHeight - (offsetToRemove ?? 0);

        const headerHeight = this.getHeaderHeight();
        const scrollTo = elementBottom - headerHeight;

        // If this point is near the bottom of the document, we will need to add extra
        // padding below the content so there is new scrollable space.  We do this by
        // setting a min height on the content element.
        const minHeight = elementBottom + this.scrollElement.clientHeight;
        this.contentElement.style.minHeight = `${minHeight}px`;
        await this.scrollTo(() => scrollTo, { duration: MS.milliseconds(200) });

        this.spacerHeight = minHeight;
    }

    private async scrollTo(yFn: () => number, options?: ScrollToEdgeOptions): Promise<boolean> {
        // Cancel any existing scroll
        this.abortController?.abort();
        this.abortController = new AbortController();

        const abortSignal = this.abortController.signal;

        const startY = this.scrollElement.scrollTop;

        const duration = options?.duration ?? (options?.behavior === 'smooth' ? MS.milliseconds(200) : 0);
        const start = performance.now();
        const end = start + duration;

        const scrollToPct = (pct: number) => {
            const diff = yFn() - startY;
            const thisY = Math.round(startY + diff * pct);

            this.animatedScrollPositions.add(thisY);
            this.scrollElement.scrollTo(0, thisY);
        };

        for (let now = start; now < end && !abortSignal.aborted; now = performance.now()) {
            scrollToPct((now - start) / duration);
            await new Promise(requestAnimationFrame);
        }

        if (abortSignal.aborted) {
            return false;
        }

        // Do one final scroll to snap to the final position
        scrollToPct(1.0);

        // Wait a short period for scroll events to settle
        await PromiseUtils.wait(MS.milliseconds(50));

        if (abortSignal.aborted) {
            return false;
        }

        this.animatedScrollPositions.clear();
        this.abortController = undefined;
        return true;
    }
}
