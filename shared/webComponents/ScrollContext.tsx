import { LRUCache } from 'lru-cache';
import { createContext, useCallback, useContext, useEffect, useRef, useState } from 'react';

import { useStream } from '@shared/stores/DataCacheStream';
import { PaginationSource } from '@shared/stores/PaginationSource';
import { EmptyFn } from '@shared/webUtils/TypeUtils';

import { AutoScroller } from './AutoScroller';

export interface ScrollContextProps {
    scrollToTop: (options?: ScrollOptions) => void;
    scrollToBottom: (options?: ScrollOptions) => void;

    // Scroll below the given element, expanding the page height to allow extra space
    // so that the element appears at the top
    scrollBelowElement: (element: Element, offsetToRemove?: number) => void;

    isPageBottom: boolean;

    storeScroll: (key: string) => void;
    restoreScroll: (key: string) => void;

    isAddingContent: (isAdding: boolean) => void;
}

const ScrollContext = createContext<ScrollContextProps>({
    scrollToTop: EmptyFn,
    scrollToBottom: EmptyFn,
    scrollBelowElement: EmptyFn,
    isPageBottom: false,
    storeScroll: EmptyFn,
    restoreScroll: EmptyFn,
    isAddingContent: EmptyFn,
});

export function useScrollContext() {
    return useContext(ScrollContext);
}

interface ScrollContextProviderProps {
    scrollElement: React.RefObject<HTMLElement>;
    contentElement: React.RefObject<HTMLElement>;
    children: React.ReactNode;
}

export function ScrollContextProvider({ scrollElement, contentElement, children }: ScrollContextProviderProps) {
    // By default we are at the bottom, until we've rendered enough to know we have extra scrollable contnet
    const [isPageBottom, setIsPageBottom] = useState(true);

    const autoScroller = useRef<AutoScroller>();

    // Create AutoScroller instance whenever scroll or content element change
    useEffect(() => {
        if (scrollElement.current && contentElement.current) {
            autoScroller.current = new AutoScroller(scrollElement.current, contentElement.current, setIsPageBottom);
            return () => {
                autoScroller.current?.destroy();
                autoScroller.current = undefined;
            };
        }
    }, [scrollElement, contentElement]);

    const scrollToTop = useCallback((options?: ScrollOptions) => autoScroller.current?.scrollToTop(options), []);
    const scrollToBottom = useCallback((options?: ScrollOptions) => autoScroller.current?.scrollToBottom(options), []);
    const scrollBelowElement = useCallback(
        (element: Element, offsetToRemove?: number) =>
            autoScroller.current?.scrollBelowElement(element, offsetToRemove),
        []
    );

    const scrollLocations = useRef<LRUCache<string, number>>(new LRUCache({ max: 20 }));
    const storeScroll = useCallback(
        (key: string) => {
            if (scrollElement.current) {
                scrollLocations.current.set(key, scrollElement.current.scrollTop);
            }
        },
        [scrollElement]
    );

    const restoreScroll = useCallback(
        (key: string) => {
            const value = scrollLocations.current.get(key);
            if (value && scrollElement.current) {
                scrollElement.current.scrollTop = value;
            }
        },
        [scrollElement]
    );

    const [, setIsAddingContentCount] = useState(0);

    const isAddingContent = useCallback(
        (isAdding: boolean) =>
            setIsAddingContentCount((previousAddingContentCount) => {
                const newCount = previousAddingContentCount + (isAdding ? 1 : -1);

                const wasAdding = previousAddingContentCount > 0;
                const isNowAdding = newCount > 0;

                if (wasAdding !== isNowAdding && autoScroller.current) {
                    autoScroller.current.isAddingContent = isNowAdding;
                }
                return newCount;
            }),
        []
    );

    return (
        <ScrollContext.Provider
            value={{
                scrollToTop,
                scrollToBottom,
                scrollBelowElement,
                storeScroll,
                restoreScroll,
                isPageBottom,
                isAddingContent,
            }}
        >
            {children}
        </ScrollContext.Provider>
    );
}

/**
 * Loads additional data on demand from the given scroll source, whenever scrolling runs to
 * the bottom of the ScrollContext.
 */
export function useInfiniteScroll(source: PaginationSource): boolean {
    const { isPageBottom } = useScrollContext();
    const hasMore = useStream(() => source.hasMore, [source], false);

    useEffect(() => {
        if (isPageBottom && hasMore) {
            source.loadMore();
        }
    }, [isPageBottom, hasMore, source]);

    return hasMore;
}
