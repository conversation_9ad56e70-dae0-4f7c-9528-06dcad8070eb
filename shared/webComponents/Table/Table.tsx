import classNames from 'classnames';
import { CSSProperties, MouseEvent, ReactNode, useCallback, useMemo, useRef, useState } from 'react';

import { useResizeObserver } from '@shared/hooks/useResizeObserver';
import { PaginationSource } from '@shared/stores/PaginationSource';
import { InfiniteScroll } from '@shared/webComponents/InfiniteScroll';
import { SearchInput } from '@shared/webComponents/TextInput/SearchInput';

import { Loading } from '../Loading/Loading';
import { HeaderCell } from './HeaderCell';
import { TableAction, TableActionButton } from './TableActionButton';
import { TableHeader } from './TableHeader';
import { RowState, TableItem } from './TableTypes';

import './Table.scss';

export type ColumnVariant = 'default' | 'secondary' | 'tertiary';

// Define a single column
export interface ColumnProps<T extends TableItem, StateT = void, SortT = void> {
    // Renderer for column header
    header?: (state: StateT, sort: SortT | undefined) => ReactNode;

    // Renderer for column cell
    cell: (t: T, state: StateT, sort: SortT | undefined) => ReactNode;

    // Column width, in pixels or using any width definition.  Will be 1fr (equal spacing) if not defined.
    width?: string;

    // Define whether this column should be shown
    display?: ({ width }: { width: number }) => boolean;

    // When true, the column will overflow with ellipsis
    ellipsis?: boolean;

    // Column variant
    variant?: ColumnVariant;

    // When true, only visible on row hover
    visibleOnHover?: boolean;

    // How the content horizontally aligns in the column
    alignment?: 'left' | 'center' | 'right' | 'stretch';

    // Click handler for this cell
    onClick?: (t: T, event: MouseEvent, state: StateT) => void;
}

interface RowAction<T> {
    action: TableAction;
    onClick: (item: T) => void;
}

type RowActionType<T extends TableItem, StateT> = RowAction<T> | ((item: T, state: StateT) => RowAction<T> | undefined);

export interface Props<T extends TableItem, StateT, SortT = void> {
    items: T[];
    sort?: SortT;
    state: StateT;
    columns: ColumnProps<T, StateT, SortT>[];
    banner?: ReactNode;
    rowActionButton?: RowActionType<T, StateT>;
    onRowClick?: (item: T, event: MouseEvent) => void;
    onSearch?: (value: string) => void;
    searchPlaceholder?: string;
    emptyView?: ReactNode;
    getRowState?: (item: T) => RowState;
    rowDividers?: boolean;
    verticalDividers?: boolean;
    verticalSpacing?: 'none' | 'small';
    borderless?: boolean;
    className?: string;
    isLoading?: boolean;
    paginationSource?: PaginationSource;
}

// Determines if row clicking should be processed
// We don't allow row-clicking when the target or its parent is a clickable
// element (button or anchor).
function ShouldHandleRowClickEvent(event: MouseEvent) {
    if (!(event.target instanceof HTMLElement)) {
        return true;
    }

    let element: HTMLElement | null | undefined = event.target as HTMLElement;
    while (element !== event.currentTarget && element) {
        if (IsClickTarget(element)) {
            return false;
        }

        element = element.parentElement;
    }
    return true;
}

function IsClickTarget(element: HTMLElement) {
    return element instanceof HTMLButtonElement || element instanceof HTMLAnchorElement;
}

// Simple strings or numbers will be auto-wrapped in a HeaderCell
// Otherwise custom header cells can be used directly
const GenerateHeader = (header: ReactNode): ReactNode => {
    if (header === undefined || header === null || header === false) {
        return header;
    }

    if (typeof header === 'string' || typeof header === 'number') {
        return (
            <HeaderCell className="table__header_cell" key={header}>
                {header}
            </HeaderCell>
        );
    }

    return <div className="table__header_cell">{header}</div>;
};

export const Table = <T extends TableItem, StateT = void, SortT = void>({
    items,
    sort,
    state,
    columns,
    rowActionButton,
    onSearch,
    searchPlaceholder,
    banner,
    onRowClick,
    emptyView,
    getRowState,
    rowDividers = true,
    verticalDividers,
    verticalSpacing = 'none',
    borderless,
    className,
    isLoading,
    paginationSource,
}: Props<T, StateT, SortT>) => {
    const classes = classNames({
        table: true,
        'table--borderless': borderless,
        table__vertical_dividers: verticalDividers,
        [`table__spacing__${verticalSpacing}`]: verticalSpacing && verticalSpacing !== 'none',
        [`${className}`]: !!className,
    });

    const ref = useRef<HTMLDivElement>(null);

    const tableSize = useResizeObserver(ref);
    const width = tableSize?.[0] ?? 0;

    return (
        <div ref={ref} className={classes}>
            {isLoading && <Loading centerAlign />}
            {!isLoading && (
                <TableBody<T, StateT, SortT>
                    items={items}
                    state={state}
                    onSearch={onSearch}
                    searchPlaceholder={searchPlaceholder}
                    sort={sort}
                    columns={columns}
                    banner={banner}
                    rowActionButton={rowActionButton}
                    emptyView={emptyView}
                    getRowState={getRowState}
                    onRowClick={onRowClick}
                    width={width}
                    paginationSource={paginationSource}
                    rowDividers={rowDividers}
                />
            )}
        </div>
    );
};

const TableBody = <T extends TableItem, StateT, SortT>({
    items,
    state,
    sort,
    onSearch,
    searchPlaceholder,
    columns,
    banner,
    rowActionButton,
    onRowClick,
    emptyView,
    getRowState,
    width,
    paginationSource,
    rowDividers,
}: {
    items: T[];
    state: StateT;
    sort?: SortT;
    onSearch?: (value: string) => void;
    searchPlaceholder?: string;
    columns: ColumnProps<T, StateT, SortT>[];
    banner?: ReactNode;
    rowActionButton?: RowActionType<T, StateT>;
    onRowClick?: (item: T, event: MouseEvent) => void;
    emptyView?: ReactNode;
    getRowState?: (item: T) => RowState;
    width: number;
    paginationSource?: PaginationSource;
    rowDividers?: boolean;
}) => {
    const columnsToDisplay = columns.filter((column) => (column.display ? column.display({ width }) : true));

    const shouldAnimateActionButton = useMemo(() => {
        const lastColumn = columnsToDisplay.at(-1);
        if (!lastColumn || !rowActionButton) {
            return false;
        }

        // Do not use animated if last column is not flexible
        return !lastColumn.width?.endsWith('px');
    }, [columnsToDisplay, rowActionButton]);

    const [searchValue, setSearchValue] = useState('');

    const onChangeSearchValue = useCallback(
        (value: string) => {
            setSearchValue(value);
            onSearch?.(value);
        },
        [onSearch]
    );

    const gridTemplateColumns = useMemo(
        () => columnsToDisplay.map((column) => column.width ?? 'minmax(0, 1fr)').join(' '),
        [columnsToDisplay]
    );
    const columnCss: CSSProperties = { gridTemplateColumns };
    const allGridColumnCss: CSSProperties = { gridColumn: `1 / ${columnsToDisplay.length + 1}` };
    const hasHeader = columns.some((column) => column.header);

    return (
        <div className="table_grid" style={columnCss}>
            {/* Header */}
            <div className="table__header_section" style={allGridColumnCss}>
                {onSearch && (
                    <div className="table__input" style={allGridColumnCss}>
                        <SearchInput
                            className="table__input__input"
                            searchType="filter"
                            placeholder={searchPlaceholder}
                            value={searchValue}
                            onValueChange={onChangeSearchValue}
                        />
                    </div>
                )}

                {hasHeader && (
                    <TableHeader className="table__header" style={allGridColumnCss}>
                        {columnsToDisplay.map((column) => GenerateHeader(column.header?.(state, sort)))}
                    </TableHeader>
                )}

                {banner && <div style={allGridColumnCss}>{banner}</div>}
            </div>

            <InfiniteScroll source={paginationSource} spinnerStyle={allGridColumnCss}>
                {items.length === 0 && (
                    <div className="table__empty" style={allGridColumnCss}>
                        {emptyView ?? 'No data available.'}
                    </div>
                )}

                {/* Rows */}
                {items.map((item) => {
                    const rowState = getRowState ? getRowState(item) : undefined;
                    return (
                        <div
                            className={classNames({
                                table__row: true,
                                [`table__row--${rowState}`]: !!rowState,
                                'table__row--clickable': !!onRowClick,
                                'table__row--animated_action': shouldAnimateActionButton,
                                'table__row--dividers': rowDividers,
                            })}
                            key={item.id}
                            onClick={(e: MouseEvent) => {
                                if (onRowClick && ShouldHandleRowClickEvent(e)) {
                                    onRowClick(item, e);
                                }
                            }}
                            style={allGridColumnCss}
                        >
                            {columnsToDisplay.map((column, index) => {
                                const isLast = index === columnsToDisplay.length - 1;
                                let rowAction: RowAction<T> | undefined;
                                if (rowActionButton) {
                                    rowAction =
                                        typeof rowActionButton === 'function'
                                            ? rowActionButton(item, state)
                                            : rowActionButton;
                                }
                                const includeActionButton = isLast && !!rowAction;

                                const {
                                    ellipsis,
                                    variant,
                                    visibleOnHover,
                                    alignment = 'start',
                                    cell,
                                    onClick,
                                } = column;

                                const onCellClick = onClick
                                    ? (e: MouseEvent) => {
                                          e.preventDefault();
                                          e.stopPropagation();
                                          onClick(item, e, state);
                                      }
                                    : undefined;

                                return (
                                    <div
                                        key={index}
                                        className={classNames('table__cell', {
                                            'table__cell--ellipsis': ellipsis !== false ? true : ellipsis,
                                            [`table__cell--${variant}`]: variant && variant !== 'default',
                                            'table__cell--visible-on-hover': visibleOnHover,
                                            [`table__cell__align_${alignment}`]: alignment,
                                            'table__cell--clickable': !!onClick,

                                            table_row__column_static_action_button:
                                                includeActionButton && !shouldAnimateActionButton,
                                            table_row__column_animated_action_button:
                                                includeActionButton && shouldAnimateActionButton,
                                        })}
                                        onClick={onCellClick}
                                    >
                                        {cell(item, state, sort)}
                                        {includeActionButton && rowAction && (
                                            <TableActionButton
                                                action={rowAction.action}
                                                onClick={(e) => {
                                                    e.preventDefault();
                                                    e.stopPropagation();
                                                    rowAction.onClick(item);
                                                }}
                                            />
                                        )}
                                    </div>
                                );
                            })}
                        </div>
                    );
                })}
            </InfiniteScroll>
        </div>
    );
};
