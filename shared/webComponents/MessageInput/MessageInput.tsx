import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { EmailInvite, TeamMember } from '@shared/api/models';

import { useStream } from '@shared/stores/DataCacheStream';
import { DraftMessageStoreTraits } from '@shared/stores/DraftMessageStoreTraits';
import { DsacSettingsStoreTraits } from '@shared/stores/DsacSettingsTypes';
import { getStore } from '@shared/stores/getStore';
import { filterReady } from '@shared/stores/StreamOperators';
import { TeamMemberStoreTraits } from '@shared/stores/TeamMemberStoreTypes';
import { TeamStoreTraits } from '@shared/stores/TeamStoreTypes';
import { ThreadStoreTraits } from '@shared/stores/ThreadStoreTypes';
import { UpsellStore } from '@shared/stores/UpsellStore';
import { useStore } from '@shared/stores/useStore';
import { useStreamEffect } from '@shared/stores/useStreamEffect';
import { useUserSettingState } from '@shared/stores/useUserSettingState';
import { MessageTransformer } from '@shared/webUtils';
import { ArrayUtils } from '@shared/webUtils/collection/ArrayUtils';

import { ClientWorkspace } from '../ClientWorkspace/ClientWorkspace';
import { FocusActionStreamTraits } from '../ClientWorkspace/ClientWorkspaceStreamTraits';
import { InviteContributorDialog } from '../ContributorsList/InviteContributorDialog';
import { IncognitoAlwaysOnDialog } from '../Incognito/IncognitoAlwaysOnDialog';
import { useRequestLicense } from '../Licenses/useRequestLicense';
import { EditorToBlockTranslator, MessageEditor } from '../MessageEditor';
import { MessageEditorForwardedRef, MessageEditorProps } from '../MessageEditor/MessageEditor';
import { MessageData } from '../MessageEditor/MessageEditorTypes';
import { ScrollForFollowOnQuestion } from '../MessageView/MessageViewUtils';
import { useModalContext } from '../Modal/ModalContext';
import { useScrollContext } from '../ScrollContext';
import { UpsellDialogIncognitoMode } from '../UpsellDialog/UpsellDialogIncognitoMode';

import './MessageInput.scss';

export type MessageInputProps = {
    teamId: string;
    threadId?: string;
    messageId?: string;
    onPostSubmit?: (props: { teamId: string; threadId: string }) => void;
    onCancel?: () => void;
} & Omit<MessageEditorProps, 'onSubmit' | 'isPrivate' | 'onSetPrivate' | 'isDsacEnabled'>;

async function ResolveTeamMembers(teamId: string, teamMemberIds: string[]): Promise<Map<string, TeamMember>> {
    const store = getStore(TeamMemberStoreTraits, { teamId });
    const members = await store.getTeamMembers(teamMemberIds);
    return new Map(members.map((member) => [member.id, member]));
}

export const MessageInput = ({
    messageId: propsMessageId,
    threadId: propsThreadId,
    onPostSubmit,
    onCancel,
    autofocus = false,
    placeholder: propsPlaceholder,
    onEditorContentChanged,
    ...props
}: MessageInputProps) => {
    const { teamId } = props;
    const [messageId, setMessageId] = useState(propsMessageId ?? crypto.randomUUID());
    const threadId = useMemo(() => propsThreadId ?? crypto.randomUUID(), [propsThreadId]);
    const store = useStore(ThreadStoreTraits, { teamId, threadId });
    const [isPrivate, setIsPrivate] = useUserSettingState('incognitoMode');
    const upsellState = useStream(() => UpsellStore.get(teamId).stream, [teamId]);

    const messageEditorRef = useRef<MessageEditorForwardedRef>(null);
    const { openModal } = useModalContext();
    const { checkForLicense } = useRequestLicense(teamId ?? '');
    const scrollContext = useScrollContext();

    const teamStore = useStore(TeamStoreTraits, {});
    const teamSupportsIncognito = useStream(
        () =>
            teamStore.teams
                .compose(filterReady)
                .map((state) => state.value.find((team) => team.id === teamId)?.capabilities.showIncognito),
        [teamStore, teamId]
    );
    const privateable = teamSupportsIncognito && !propsThreadId;

    const dsacSettingsStore = useStore(DsacSettingsStoreTraits, { teamId });
    const dsacSettings = useStream(() => dsacSettingsStore.stream, [dsacSettingsStore], { $case: 'loading' });
    const isDsacEnabled = useMemo(() => dsacSettings.$case === 'ready' && dsacSettings.isEnabled, [dsacSettings]);
    const isAllowedToUpdate = useMemo(
        () => dsacSettings.$case === 'ready' && dsacSettings.isAllowedToUpdate,
        [dsacSettings]
    );

    useStreamEffect(
        () =>
            ClientWorkspace.instance()
                .getStream(FocusActionStreamTraits, { $case: 'focusAction' })
                .filter((action) => action.$case === 'messageInput'),
        [],
        () => {
            messageEditorRef.current?.focus();
        }
    );

    // Message drafts are stored by thread (if we're editing messages for a thread), or by 'ask' by default
    const draftMessageStore = useStore(DraftMessageStoreTraits, {});
    const draftMessageKey = propsThreadId ?? 'ask';

    useEffect(() => {
        const loadDraft = async () => {
            const content = await draftMessageStore.getDraft(draftMessageKey);
            if (content) {
                messageEditorRef.current?.setContent(content);
            }
        };

        void loadDraft();
    }, [draftMessageKey, draftMessageStore]);

    const onContentChanged = useCallback(
        async (content: MessageData) => {
            void draftMessageStore.setDraft(draftMessageKey, content);
            onEditorContentChanged?.(content);
        },
        [draftMessageKey, draftMessageStore, onEditorContentChanged]
    );

    const togglePrivate = useCallback(
        (isPrivate: boolean) => {
            if (!privateable) {
                return;
            }

            if (isDsacEnabled) {
                openModal(<IncognitoAlwaysOnDialog teamId={teamId} canConfigure={isAllowedToUpdate} />);
                return;
            }

            if (upsellState?.$case === 'ready' && upsellState.incognitoMode && isPrivate) {
                openModal(<UpsellDialogIncognitoMode teamId={teamId} template={upsellState.incognitoMode} />);
            } else {
                setIsPrivate(isPrivate);
            }
        },
        [privateable, isDsacEnabled, upsellState, openModal, teamId, isAllowedToUpdate, setIsPrivate]
    );

    const checkForInvites = useCallback(
        async (mentions: string[]): Promise<EmailInvite[] | undefined> => {
            const teamMembers = await ResolveTeamMembers(teamId, mentions);

            const nonMembers = ArrayUtils.compact(
                mentions.map((id) => {
                    const matchingTeamMember = teamMembers.get(id);
                    if (!matchingTeamMember?.hasAccount) {
                        return matchingTeamMember;
                    }
                })
            );

            if (!nonMembers.length) {
                return;
            }

            return new Promise<EmailInvite[] | undefined>((resolve) =>
                openModal(
                    <InviteContributorDialog
                        nonMembers={nonMembers}
                        onInvite={resolve}
                        onCancel={() => resolve(undefined)}
                        buttonText="Invite and Post Comment"
                        requireAllFields
                        stacked
                    />
                )
            );
        },
        [openModal, teamId]
    );

    const onSubmitClicked = useCallback(
        async ({ content, mentions, attachments }: MessageData) => {
            const blocks = EditorToBlockTranslator.translateToBlock(content);
            const licenseOk = await checkForLicense();
            if (!licenseOk) {
                return;
            }

            const emails = await checkForInvites(mentions);
            const emailAddress = emails?.map((invite) => invite.email);
            const messageBytes = MessageTransformer.fromBlocksToBytes(blocks);

            // If this is a new thread, create it now
            if (!propsThreadId) {
                try {
                    await store.createQa({
                        query: {
                            $case: 'blocks',
                            contentBytes: messageBytes,
                            mentions,
                        },
                        isThreadPrivate: isPrivate,
                        mentions,
                        attachments,
                        emails: emailAddress,
                    });
                } catch {
                    await ClientWorkspace.instance().notify({
                        title: propsThreadId ? 'Could not create Discussion' : 'Could not add the reply',
                        content: propsThreadId
                            ? 'An unexpected error occurred creating the discussion'
                            : 'An unexpected error occurred adding the reply',
                        type: 'error',
                    });
                }
            }

            // Otherwise we're upserting a message in a thread
            else {
                // Add new message
                if (!propsMessageId) {
                    try {
                        ScrollForFollowOnQuestion(scrollContext);

                        await store.addMessage({
                            messageId,
                            messageBytes,
                            mentions,
                            attachments,
                            emails: emailAddress,
                        });

                        // We created a new message -- clear the message ID and begin editing a new message
                        // This is needed for the message reply UI
                        setMessageId(crypto.randomUUID());
                    } catch {
                        await ClientWorkspace.instance().notify({
                            title: 'Could not add the reply',
                            content: 'An unexpected error occurred adding the reply',
                            type: 'error',
                        });
                    }
                }

                // Update existing message
                else {
                    try {
                        await store.editQuestion({
                            messageId,
                            messageBytes,
                            mentions,
                            emails: emailAddress,
                            attachments,
                        });
                    } catch {
                        await ClientWorkspace.instance().notify({
                            title: 'Could not edit the discussion',
                            content: 'An unexpected error editing the discussion',
                            type: 'error',
                        });
                    }
                }
            }

            messageEditorRef.current?.clear();
            await draftMessageStore.clearDraft(draftMessageKey);
            onPostSubmit?.({ teamId, threadId });
        },
        [
            checkForLicense,
            checkForInvites,
            propsThreadId,
            draftMessageStore,
            draftMessageKey,
            onPostSubmit,
            teamId,
            threadId,
            store,
            isPrivate,
            propsMessageId,
            scrollContext,
            messageId,
        ]
    );

    const onCancelClicked = useCallback(() => {
        messageEditorRef.current?.clear();
        onCancel?.();
    }, [onCancel]);

    const placeholder =
        (propsPlaceholder ?? propsThreadId)
            ? 'Continue the discussion'
            : privateable && (isPrivate || isDsacEnabled)
              ? 'Ask a question privately'
              : 'Ask a question';

    return (
        <div className="message_input">
            <div className="message_input__editor">
                <MessageEditor
                    {...props}
                    threadId={threadId}
                    messageId={messageId}
                    ref={messageEditorRef}
                    placeholder={placeholder}
                    autofocus={autofocus}
                    onSubmit={onSubmitClicked}
                    onCancel={onCancel ? onCancelClicked : undefined}
                    isPrivate={privateable ? (isDsacEnabled ? true : isPrivate) : undefined}
                    onSetPrivate={privateable ? togglePrivate : undefined}
                    isDsacEnabled={isDsacEnabled}
                    onEditorContentChanged={onContentChanged}
                />
            </div>
        </div>
    );
};
