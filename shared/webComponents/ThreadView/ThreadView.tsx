import { useEffect, useMemo } from 'react';

import { MessageAggregate, Provider, ThreadInfoAggregate, ThreadSlackInfo } from '@shared/api/models';

import { ArrayUtils } from '@shared/webUtils/collection/ArrayUtils';
import { MessageUtils } from '@shared/webUtils/MessageUtils';
import { isQaThread } from '@shared/webUtils/ThreadUtils';

import { ClientWorkspace } from '../ClientWorkspace/ClientWorkspace';
import { ConnectAccountsBanner } from '../ConnectAccountsWarning/ConnectAccountsBanner';
import { ExternalLink } from '../ExternalLink/ExternalLink';
import { MessageView } from '../MessageView/MessageView';

import './ThreadView.scss';

interface ThreadViewProps {
    thread: ThreadInfoAggregate;
    messages: MessageAggregate[];
    provider: Provider | undefined;
    onNavigateUser?: (teamMemberId: string) => void;
    slackInfo?: ThreadSlackInfo;
}

export const ThreadView = ({ thread, messages, provider, onNavigateUser }: ThreadViewProps) => {
    // If this thread has unread messages, mark them as read now
    useEffect(
        () => {
            if (thread.unread && thread.unread.latestMessage !== thread.unread.latestReadMessage) {
                ClientWorkspace.instance().handleAction({
                    $case: 'updateThreadUnread',
                    teamId: thread.thread.teamId,
                    threadId: thread.thread.id,
                    updateThreadRequest: { latestReadMessage: thread.unread.latestMessage },
                });
            }
        },
        // We only trigger marking as read on first view (when thread/team IDs change)
        // thread.unread ought to be a dependency here, but we explicitly don't use it, so that if
        // the read state is manually changed while viewing, we don't immediately mark as viewed again
        // eslint-disable-next-line react-hooks/exhaustive-deps
        [thread.thread.id, thread.thread.teamId]
    );

    const showDsacBanner = useMemo(
        () => ArrayUtils.firstOrUndefined(messages)?.isAuthorCurrentPerson && isQaThread(thread),
        [thread, messages]
    );

    // Show the DSAC link banner on the last message, as long as it's an unbot response.
    const lastBotMessageId = useMemo(() => {
        const lastMessage = messages.at(-1);
        return lastMessage && MessageUtils.isUnbotAuthor(lastMessage) ? lastMessage.id : undefined;
    }, [messages]);

    return (
        <div className="thread_view">
            {messages.map((message, idx) => {
                const { author } = message;
                const navigateId = author.isPrimary ? author.id : (author.primaryMemberId ?? undefined);

                return (
                    <MessageView
                        key={message.id}
                        index={idx}
                        teamId={thread.thread.teamId}
                        threadId={thread.thread.id}
                        message={message}
                        onNavigateAuthor={navigateId && onNavigateUser ? () => onNavigateUser(navigateId) : undefined}
                        provider={provider}
                        lastMessageBanner={
                            message.id === lastBotMessageId && showDsacBanner ? (
                                <ConnectAccountsBanner
                                    teamId={thread.thread.teamId}
                                    className="message_list_view__dsac_banner"
                                    compact
                                    buttonText="Link Accounts"
                                />
                            ) : null
                        }
                    />
                );
            })}

            {thread.thread.provider === 'slack' ? (
                <div className="thread_view__slack_external">
                    <ExternalLink
                        href={thread.messages.at(-1)?.links.externalMessageUrl ?? thread.thread.links.externalUrl}
                    >
                        Continue discussion in Slack
                    </ExternalLink>
                </div>
            ) : null}
        </div>
    );
};
