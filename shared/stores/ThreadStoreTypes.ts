import {
    ArchiveReasonType,
    FeedbackType,
    Message,
    MessageAttachment,
    Thread,
    ThreadContext,
} from '@shared/api/generatedApi';

import {
    CreateStoreProxyTraits,
    StoreProxyAction0Args,
    StoreProxyAction1Args,
    StoreProxyStream,
} from '@shared/proxy/StoreProxy/StoreProxyTypes';
import { AskQuestionContent } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { LocalContext } from '@shared/webComponents/ClientWorkspace/LocalContextProvider';

import { ApiDataStreamState } from './ApiDataStream';
import { ArchiveReferenceId } from './ArchiveReferenceUtilTypes';
import { ThreadInfoAggregate } from './ThreadInfoAggregate';

export type CreateQaProps = {
    query: AskQuestionContent;
    isThreadPrivate?: boolean | undefined;
    mentions?: string[];
    attachments?: MessageAttachment[];
    localContext?: LocalContext;
    emails?: string[];
};

export type AddMessageProps = {
    messageId: string;
    messageBytes: string;
    mentions: string[];
    attachments?: MessageAttachment[];
    emails?: string[];
    context?: ThreadContext | undefined;
    suggestedQuestionId?: string;
};

export type UpdateMessageProps = {
    messageId: string;
    messageBytes: string;
    mentions: string[];
    emails?: string[];
    attachments?: MessageAttachment[];
};

export type UpdateMessageFeedbackProps = {
    messageId: string;
    feedbackType: FeedbackType | undefined;
    feedbackDescription?: string;
};

export type AddMessageForSuggestedQuestionProps = {
    query: string;
    suggestedQuestionId: string | undefined;
    context?: ThreadContext | undefined;
};

export type ArchiveMessageReferenceProps = {
    messageId: string;
    id: ArchiveReferenceId;
    reason?: ArchiveReasonType;
    comment?: string;
};

export const ThreadStoreTraits = CreateStoreProxyTraits({
    category: 'ThreadStore',
    keyToString: (key: { teamId: string; threadId: string }) => `${key.teamId}.${key.threadId}`,
    actions: {
        // Thread operations
        createQa: StoreProxyAction1Args<CreateQaProps, Thread>(),
        updateTitle: StoreProxyAction1Args<string, Thread>(),
        togglePrivacy: StoreProxyAction1Args<boolean, void>(),
        confirmArchive: StoreProxyAction0Args<boolean>(),
        archive: StoreProxyAction0Args<Thread>(),
        confirmRestore: StoreProxyAction0Args<boolean>(),
        restore: StoreProxyAction0Args<Thread>(),
        delete: StoreProxyAction0Args<void>(),

        // Message operations
        addMessage: StoreProxyAction1Args<AddMessageProps, Message>(),
        editQuestion: StoreProxyAction1Args<UpdateMessageProps, Message>(),
        deleteMessage: StoreProxyAction1Args<string, void>(),
        confirmDeleteMessage: StoreProxyAction1Args<string, boolean>(),
        updateMessageFeedback: StoreProxyAction1Args<UpdateMessageFeedbackProps, void>(),
        addMessageForSuggestedQuestion: StoreProxyAction1Args<AddMessageForSuggestedQuestionProps, Message>(),
        archiveMessageReference: StoreProxyAction1Args<ArchiveMessageReferenceProps, void>(),
    },
    streams: {
        stream: StoreProxyStream<ApiDataStreamState<ThreadInfoAggregate>>(),
    },
});
