import equal from 'fast-deep-equal';
import { v4 as uuid } from 'uuid';
import { Stream } from 'xstream';
import dropRepeats from 'xstream/extra/dropRepeats';

import { ApiCallWithLastModified } from '@shared/api/ApiUtils';

import { ClientWorkspace } from '@shared/webComponents/ClientWorkspace/ClientWorkspace';
import { BlockUtils } from '@shared/webUtils/BlockUtils';
import { ClientOverride } from '@shared/webUtils/ClientOverride';
import { ArrayUtils } from '@shared/webUtils/collection/ArrayUtils';
import { LazyValueCache } from '@shared/webUtils/LazyValueCache';
import { logger } from '@shared/webUtils/log';
import { MessageFeedbackUtils } from '@shared/webUtils/MessageFeedbackUtils';
import { MessageTransformer } from '@shared/webUtils/MessageTransformer';
import { StreamAsyncFetcher } from '@shared/webUtils/StreamUtils';
import { MS } from '@shared/webUtils/TimeUtils';
import { TriggeredTimeout } from '@shared/webUtils/TriggeredTimeout';

import { API } from '../api';
import {
    Block,
    FeedbackType,
    Mark,
    Message,
    MessageAttachment,
    Provider,
    Thread,
    ThreadContext,
    ThreadInfo,
    ThreadParticipant,
    ThreadType,
} from '../api/models';
import { ApiDataStreamState } from './ApiDataStream';
import { archiveReference, hasArchivedReference, updateReferencesWithArchive } from './ArchiveReferenceUtils';
import { sendThreadEmailInvites } from './EmailStore';
import { LoadableState } from './LoadableState';
import { getCurrentTeamMember } from './PersonStore';
import { PollingDataStream } from './PollingDataStream';
import { ProductFeedbackStore } from './ProductFeedbackStore';
import { OverlayInstance, StreamOverlay, withOverlay } from './StreamOverlay';
import { TeamMemberBotStore } from './TeamMemberBotStore';
import { TeamMemberStore } from './TeamMemberStore';
import { aggregateThreadInfo, getThreadStreamTeamMembers, ThreadInfoAggregate } from './ThreadInfoAggregate';
import { TeamThreadInfoStreams } from './ThreadListStore';
import {
    AddMessageForSuggestedQuestionProps,
    AddMessageProps,
    ArchiveMessageReferenceProps,
    CreateQaProps,
    UpdateMessageFeedbackProps,
    UpdateMessageProps,
} from './ThreadStoreTypes';
import { createValueStream } from './ValueStream';

const log = logger('ThreadStore');

export type SingleThreadMutationFn = (threadInfo: ThreadInfo) => ThreadInfo;
export type SingleThreadCancelFn = (threadInfo: ThreadInfo) => boolean;

// A stream that publishes events whenever the local user creates or archives a stream.
// This is used so that the FileSourceMarkStream can publish the new stream immediately.
const createOrArchiveThreadStreamBase = createValueStream<{ threadId: string; thread?: ThreadInfoAggregate }>();
export const createThreadStream = createOrArchiveThreadStreamBase.stream;

export interface CreateThreadArgs {
    title: string;
    blocks: Block[];
    repoId?: string;
    sourcemark?: Mark;
    threadParticipants: ThreadParticipant[];
    mentions?: string[];
    attachments?: MessageAttachment[];
    fileMarks?: Mark[];
    isPrivate?: boolean;
    context?: ThreadContext | undefined;
}

const VERSION = '1';

type MessageListMutationFn = (messages: Message[]) => Message[];
type MessageListCancelFn = (messages: Message[]) => boolean;

const SLOW_POLL_PERIOD = MS.seconds(10);
const FAST_POLL_PERIOD = MS.seconds(2);

export class ThreadStore {
    // static get = StoreCache(
    //     ThreadStoreTraits,
    //     ({ teamId, threadId }: { teamId: string; threadId: string }) => new ThreadStore(teamId, threadId),
    //     {
    //         cachingPolicy: { $case: 'ttl', minLifetime: MS.minutes(5) },
    //     }
    // );

    static get = LazyValueCache(
        ({ teamId, threadId }: { teamId: string; threadId: string }) => new ThreadStore(teamId, threadId),
        ({ teamId, threadId }: { teamId: string; threadId: string }) => `${teamId}.${threadId}`
    );

    private pollingStream;
    readonly stream: Stream<LoadableState<ThreadInfoAggregate>>;

    // This stream will issue true while we want increased polling velocity
    // While we want fast polling, reduce the polling frequency, and poll regardless
    // of whether we have focus or not
    private fastPollingTimeout = new TriggeredTimeout(MS.minutes(1), (fastPoll) => {
        this.pollingStream.period = fastPoll ? FAST_POLL_PERIOD : SLOW_POLL_PERIOD;
        this.pollingStream.requireFocus = !fastPoll;
    });

    private overlay = new StreamOverlay<LoadableState<ThreadInfo>>();

    constructor(
        private teamId: string,
        private threadId: string
    ) {
        this.pollingStream = new PollingDataStream<ThreadInfo>({
            pollFn: ApiCallWithLastModified({
                apiRequest: (request) => API.threads.getThreadRaw(request),
                requestGenerator: () => ({ teamId: this.teamId, threadId: this.threadId }),
                onError: (request, error) => log.error('Error occurred fetching thread', { request, error }),
            }),
            period: SLOW_POLL_PERIOD,
            teamId: this.teamId,
            pollOnRefocus: true,
        });
        this.stream = this.pollingStream
            .map<LoadableState<ThreadInfo>>((threadInfo) => ({ $case: 'ready', value: threadInfo }))
            .startWith({ $case: 'loading' })
            .compose(this.overlay.overlayOnStream)
            .compose(TeamMemberStore.get(teamId).createJoinOperation(getThreadStreamTeamMembers))
            .map(([state, teamMemberMap]) => {
                if (state.$case === 'loading') {
                    return {
                        $case: 'loading',
                    };
                }

                // We keep channel polling active while any message in the thread is loading or streaming

                const isStreaming = state.value.messages.some(
                    (message) => message.state === 'loading' || message.isStreaming
                );

                if (isStreaming) {
                    this.fastPollingTimeout.trigger();
                }

                return {
                    $case: 'ready',
                    value: aggregateThreadInfo(state.value, teamMemberMap),
                };
            })
            .compose(dropRepeats(equal))
            .remember();
    }

    /**
     * Same as withOverlay -- specifies an overlay, but automatically re-polls the thread after the operation completes.
     * If mayCauseStreaming is true, the poller will go into high-frequency-polling mode for 10 seconds after the
     * operation completes.  This is useful when an operation will likely cause streaming to occur, so that we
     * catch the streaming state.
     */
    async withThreadOverlay<T, U>(
        streamOverlays: StreamOverlay<T> | StreamOverlay<T>[] | null | undefined,
        overlay: OverlayInstance<T>,
        operationFn: () => Promise<U>,
        options?: { mayCauseStreaming: boolean }
    ): Promise<U> {
        return withOverlay(streamOverlays, overlay, async () => {
            const result = await operationFn();

            try {
                await this.pollingStream.trigger();
            } catch {
                /* no need to log */
            }

            if (options?.mayCauseStreaming) {
                this.fastPollingTimeout.trigger();
            }

            return result;
        });
    }

    public async togglePrivacy(isPrivate: boolean) {
        const overlay: OverlayInstance<ApiDataStreamState<ThreadInfo>> = {
            overlayMutationFn: (state): ApiDataStreamState<ThreadInfo> => {
                if (state.$case !== 'ready') {
                    return state;
                }

                const thread: Thread = {
                    ...state.value.thread,
                    isPrivate,
                };
                return {
                    $case: 'ready',
                    value: {
                        ...state.value,
                        thread,
                    },
                };
            },
            shouldCancel: (state): boolean => {
                return state.$case === 'ready' && state.value.thread.isPrivate === isPrivate;
            },
            uniqueId: this.threadId,
        };

        await this.withThreadOverlay([this.overlay], overlay, async () => {
            await API.threads.updateThreadPrivacy({
                teamId: this.teamId,
                threadId: this.threadId,
                updateThreadPrivacyRequest: {
                    isPrivate,
                },
            });
        });
    }

    // An overlay that adds or updates a message in a thread
    private createMessageOverlay(
        mutationFn: MessageListMutationFn,
        cancelFn: MessageListCancelFn,
        uniqueId?: string
    ): OverlayInstance<ApiDataStreamState<ThreadInfo>> {
        const overlayMutationFn = (state: ApiDataStreamState<ThreadInfo>): ApiDataStreamState<ThreadInfo> => {
            if (state.$case === 'loading') {
                return state;
            }

            // Mutate the message list and apply to a new overlay Thread
            const messages = mutationFn(state.value.messages.slice());
            const overlayThread: ThreadInfo = { ...state.value, messages };
            return { ...state, value: overlayThread };
        };

        const shouldCancel = (state: ApiDataStreamState<ThreadInfo>) =>
            state.$case === 'ready' && cancelFn(state.value.messages);

        return { overlayMutationFn, shouldCancel, uniqueId };
    }

    private createLocalMessage(messageBytes: string, mentions: string[], attachments?: MessageAttachment[]): Message {
        const currentTeamMember = getCurrentTeamMember(this.teamId);
        if (!currentTeamMember) {
            throw new Error('Missing current team member');
        }

        const id = uuid();
        const messageContent = {
            content: messageBytes,
            version: VERSION,
        };

        const createdMessage: Message = {
            id,
            authorTeamMemberId: currentTeamMember.id,
            messageContent,
            createdAt: new Date(),
            links: {
                dashboardUrl: '',
            },
            mentions,
            attachments,
        };

        return createdMessage;
    }

    async addMessage({
        messageId,
        messageBytes,
        mentions,
        emails,
        attachments,
        context,
        suggestedQuestionId,
    }: AddMessageProps) {
        const currentTeamMember = getCurrentTeamMember(this.teamId);
        if (!currentTeamMember) {
            throw new Error('Missing current team member');
        }

        if (!!emails?.length) {
            void sendThreadEmailInvites(this.teamId, this.threadId, emails);
        }

        const messageContent = {
            content: messageBytes,
            version: VERSION,
        };

        const createdMessage: Message = this.createLocalMessage(messageBytes, mentions, attachments);

        const overlay = this.createMessageOverlay(
            (messages) => [...messages, createdMessage],
            (messages) => messages.some((message) => message.id === messageId)
        );

        const messages = await this.withThreadOverlay(
            this.overlay,
            overlay,
            async () => {
                const newMessage = await API.messages.createMessageV3({
                    teamId: this.teamId,
                    messageId,
                    createMessageRequestV3: {
                        id: messageId,
                        threadId: this.threadId,
                        messageContent,
                        mentions,
                        attachments,
                        context,
                        // Currently we do not support {sourcemark, fileMarks, videoMetadata} fields on message creation.
                        // The API does allow for this, so we could support this in the future.
                        sourcemark: undefined,
                        fileMarks: [],
                        isSuggestedFollowOn: !!suggestedQuestionId,
                        suggestedQuestionId,
                    },
                });
                return [newMessage];
            },
            { mayCauseStreaming: true }
        );

        if (messages.length !== 1) {
            throw new Error('Unexpected message creation error');
        }
        return messages[0];
    }

    async editQuestion({
        messageId,
        messageBytes,
        mentions,
        emails,
        attachments,
    }: UpdateMessageProps): Promise<Message> {
        if (!!emails?.length) {
            void sendThreadEmailInvites(this.teamId, this.threadId, emails);
        }

        // Get the current thread
        const threadState = await StreamAsyncFetcher(this.stream, (n) => n);

        // Should never happen
        if (threadState.$case === 'loading') {
            throw new Error('Thread could not be edited');
        }

        const thread = threadState.value;
        let messageIdx = thread.messages.findIndex((message) => message.id === messageId);
        const messagesToRemove = messageIdx === -1 ? [] : thread.messages.slice(messageIdx + 1);

        if (messageIdx === -1) {
            messageIdx = thread.messages.length;
        }

        const messageContent = {
            content: messageBytes,
            version: VERSION,
        };

        const fakeBotLoadingMessage = await this.createFakeBotLoadingMessage();

        const createdMessage: Message = this.createLocalMessage(messageBytes, mentions, attachments);

        const overlay = this.createMessageOverlay(
            (messages) => {
                const idx = messages.findIndex((message) => message.id === messageId);
                if (idx < 0) {
                    return messages;
                }
                const newMessages = messages.slice(0, idx);
                newMessages.push(createdMessage);
                newMessages.push(fakeBotLoadingMessage);
                return newMessages;
            },
            (messages) =>
                messages.some((message) => message.id === messageId && message.messageContent.content === messageBytes)
        );

        const messages = await this.withThreadOverlay(this.overlay, overlay, async () => {
            // Delete all old messages
            // We do this before asking the new message, otherwise the bot will not respond
            for (const message of messagesToRemove) {
                await API.messages.deleteMessage({ teamId: this.teamId, messageId: message.id });
            }

            // Ask new message
            const newMessage = await API.messages.updateMessageV3({
                teamId: this.teamId,
                messageId,
                updateMessageRequestV3: {
                    id: messageId,
                    messageContent,
                    mentions,
                    onlyNewSourceMarks: [],
                    attachments,
                },
            });

            return [newMessage];
        });

        if (messages.length !== 1) {
            throw new Error('Unexpected message update error');
        }
        return messages[0];
    }

    async confirmDeleteMessage(messageId: string): Promise<boolean> {
        const { response } = await ClientWorkspace.instance().dialog({
            title: 'Are you sure you want to delete this message?',
            description: 'Messages cannot be undeleted.',
            okTitle: 'Delete',
            cancelTitle: 'Cancel',
            destructive: true,
        });

        if (response === 'ok') {
            await this.deleteMessage(messageId);
            return true;
        }

        return false;
    }

    async deleteMessage(messageId: string): Promise<void> {
        const overlay = this.createMessageOverlay(
            (messages) => messages.filter((message) => message.id !== messageId),
            (messages) => messages.every((message) => message.id !== messageId)
        );

        const messages = await this.withThreadOverlay(this.overlay, overlay, async () => {
            const updated = await API.messages.deleteMessage({
                teamId: this.teamId,
                messageId,
            });
            return [updated];
        });

        if (messages.length !== 1) {
            throw new Error('Unexpected message delete error');
        }
        return messages[0];
    }

    async updateMessageFeedback({
        messageId,
        feedbackType,
        feedbackDescription,
    }: UpdateMessageFeedbackProps): Promise<void> {
        const currentTeamMember = getCurrentTeamMember(this.teamId);
        if (!currentTeamMember) {
            throw new Error('Missing current team member');
        }

        const feedbackMatches = (message: Message, feedbackType: FeedbackType | undefined): boolean => {
            const feedback = message.feedback;
            if (!feedback) {
                return false;
            }

            return MessageFeedbackUtils.hasMatchingFeedback(feedback, currentTeamMember.id, feedbackType);
        };

        const overlay = this.createMessageOverlay(
            (messages) => {
                const idx = messages.findIndex((message) => message.id === messageId);
                if (idx < 0) {
                    return messages;
                }
                const newMessages = messages.slice();
                const feedback = newMessages[idx].feedback;
                const newFeedback = feedback
                    ? MessageFeedbackUtils.setFeedback(
                          feedback,
                          currentTeamMember.id,
                          feedbackType,
                          feedbackDescription
                      )
                    : undefined;

                newMessages[idx] = { ...newMessages[idx], feedback: newFeedback };
                return newMessages;
            },
            (messages) =>
                messages.some((message) => message.id === messageId && feedbackMatches(message, feedbackType)),
            `ChangeMessageFeedback-${messageId}`
        );

        await this.withThreadOverlay(this.overlay, overlay, async () => {
            return await API.messages.updateMessageFeedback({
                teamId: this.teamId,
                messageId,
                updateMessageFeedbackRequest: { feedbackType, feedbackDescription },
            });
        });
    }

    async addMessageForSuggestedQuestion({
        query,
        suggestedQuestionId,
        context,
    }: AddMessageForSuggestedQuestionProps): Promise<Message> {
        const botTeamMemberId = await TeamMemberBotStore.instance().getBotTeamMemberId(this.teamId);
        const block = BlockUtils.createTextBlock(query);

        const mapQuestions = (message: Message): Message => {
            if (!message.suggestions) {
                return message;
            }

            const idx = message.suggestions.followOn.findIndex(
                (suggestion) => suggestion.questionId === suggestedQuestionId
            );
            if (idx < 0) {
                return message;
            }
            const followOn = message.suggestions.followOn[idx];
            const overlayFollowOn = message.suggestions.followOn.slice();
            overlayFollowOn[idx] = {
                ...followOn,
                hasBeenAsked: true,
            };

            return {
                ...message,
                suggestions: { followOn: overlayFollowOn },
            };
        };

        const hasQuestionBeenAsked = (message: Message): boolean =>
            !!message.suggestions?.followOn.some(
                (suggestion) => suggestion.questionId === suggestedQuestionId && suggestion.hasBeenAsked
            );

        // Overlay that temporarily sets the "hasBeenAsked" flag on the given question, so it disappears immediately.
        const overlay = this.createMessageOverlay(
            (messages) => messages.map(mapQuestions),
            (messages) => messages.some(hasQuestionBeenAsked)
        );
        const messageBytes = MessageTransformer.fromBlocksToBytes([block]);

        return await this.withThreadOverlay(
            this.overlay,
            overlay,
            async () => {
                return await this.addMessage({
                    messageId: uuid(),
                    messageBytes,
                    mentions: [botTeamMemberId],
                    suggestedQuestionId,
                    context,
                });
            },
            { mayCauseStreaming: true }
        );
    }

    async archiveMessageReference({ messageId, id, reason, comment }: ArchiveMessageReferenceProps): Promise<void> {
        const currentTeamMember = getCurrentTeamMember(this.teamId);
        if (!currentTeamMember) {
            throw new Error('Missing current team member');
        }

        const referenceMatches = (message: Message): boolean => {
            const references = message.references;
            if (!references) {
                return false;
            }

            return hasArchivedReference(references, id);
        };

        const overlay = this.createMessageOverlay(
            (messages) => {
                const idx = messages.findIndex((message) => message.id === messageId);
                if (idx < 0) {
                    return messages;
                }
                const newMessages = messages.slice();
                const references = newMessages[idx].references;
                const updatedReferences = references
                    ? updateReferencesWithArchive(currentTeamMember.id, references, id, reason, comment)
                    : undefined;

                newMessages[idx] = { ...newMessages[idx], references: updatedReferences };
                return newMessages;
            },
            (messages) => messages.some((message) => message.id === messageId && referenceMatches(message)),
            `ChangeMessageReferences-${messageId}`
        );

        await this.withThreadOverlay(this.overlay, overlay, async () => {
            return await archiveReference(this.teamId, messageId, id, reason, comment);
        });
    }

    private async createFakeBotLoadingMessage(): Promise<Message> {
        const botTeamMemberId = await TeamMemberBotStore.instance().getBotTeamMemberId(this.teamId);

        return {
            id: crypto.randomUUID(),
            authorTeamMemberId: botTeamMemberId,
            createdAt: new Date(),
            links: {},
            state: 'loading',
            messageContent: {
                content: MessageTransformer.fromBlocksToBytes([]),
                version: '1',
            },
        };
    }

    private async createThread(createThreadRequest: CreateThreadArgs): Promise<Thread> {
        const {
            title,
            blocks,
            repoId,
            sourcemark,
            fileMarks,
            threadParticipants,
            mentions,
            attachments,
            isPrivate,
            context,
        } = createThreadRequest;

        const version = '1';
        const encodedMessage = MessageTransformer.fromMessageToBytes({
            blocks,
            version,
        });

        const userIdentity = getCurrentTeamMember(this.teamId);

        const messageId = crypto.randomUUID();

        // Make a mock ThreadInfo to temporarily overlay in the UI
        const threadInfo: ThreadInfo = {
            thread: {
                id: this.threadId,
                teamId: this.teamId,
                title,
                threadType: ThreadType.UnknownDefaultOpenApi,
                isPrivate,
                provider: Provider.Unblocked,
                lastMessageCreatedAt: new Date(),
                links: {
                    dashboardUrl: '',
                    webExtensionUrl: '',
                },
            },
            repoId,
            messages: [
                {
                    id: messageId,
                    authorTeamMemberId: userIdentity?.id ?? crypto.randomUUID(),
                    createdAt: new Date(),
                    links: {},
                    attachments,
                    messageContent: {
                        content: encodedMessage,
                        version: '1',
                    },
                },
                await this.createFakeBotLoadingMessage(),
            ],
            participants: ArrayUtils.compactMap(
                [...threadParticipants, { teamMemberId: userIdentity?.id }],
                (participant) => participant.teamMemberId
            ),
            mark: sourcemark,
            modifiedAt: new Date().getMilliseconds() * 1_000,
            capabilities: {
                canReply: true,
                canUpdatePrivacy: true,
            },
        };

        const threadsListOverlay: OverlayInstance<ApiDataStreamState<ThreadInfo[]>> = {
            overlayMutationFn: (state) =>
                state.$case === 'ready' ? { ...state, value: [...state.value, threadInfo] } : state,
            shouldCancel: (state) =>
                state.$case === 'ready' && state.value.some((threadInfo) => threadInfo.thread.id === this.threadId),
        };

        const singleThreadOverlay: OverlayInstance<ApiDataStreamState<ThreadInfo>> = {
            overlayMutationFn: () => ({ $case: 'ready', value: threadInfo }),
            shouldCancel: (state) => state.$case === 'ready' && state.value.thread.id === this.threadId,
        };

        const addedThread = await this.withThreadOverlay(
            TeamThreadInfoStreams.getOverlays((key) => key.teamId === this.teamId && key.type === 'mine'),
            threadsListOverlay,
            async () =>
                await withOverlay(this.overlay, singleThreadOverlay, async () => {
                    return await API.threads.createThreadV3({
                        threadId: this.threadId,
                        teamId: this.teamId,
                        createThreadRequestV3: {
                            title,
                            message: {
                                id: messageId,
                                repoId,
                                threadId: this.threadId,
                                attachments,
                                sourcemark,
                                fileMarks,
                                messageContent: {
                                    content: encodedMessage,
                                    version,
                                },
                                mentions,
                            },
                            threadParticipants,
                            isPrivate,
                            context,
                        },
                    });
                }),
            { mayCauseStreaming: true }
        );

        // Trigger updates for FileSourceMarkStream
        const threadInfoAggregate: ThreadInfoAggregate = {
            thread: addedThread,
            repoId: threadInfo.repoId,
            messages: [],
            participants: [],
            teamParticipants: [],
            mark: sourcemark ?? fileMarks?.[0],
            experts: [],
            capabilities: {
                canReply: true,
                canUpdatePrivacy: false,
            },
        };

        createOrArchiveThreadStreamBase.updateValue({ threadId: this.threadId, thread: threadInfoAggregate });

        return addedThread;
    }

    async updateTitle(title: string): Promise<Thread> {
        const overlay: OverlayInstance<ApiDataStreamState<ThreadInfo[]>> = {
            overlayMutationFn: (state) => {
                if (state.$case === 'loading') {
                    return state;
                }
                const updatedThreads = state.value.map((threadInfo) =>
                    threadInfo.thread.id === this.threadId
                        ? { ...threadInfo, thread: { ...threadInfo.thread, title } }
                        : threadInfo
                );
                return { ...state, value: updatedThreads };
            },
            shouldCancel: (state) =>
                state.$case === 'ready' &&
                state.value.some(
                    (threadInfo) => threadInfo.thread.id === this.threadId && threadInfo.thread.title === title
                ),
        };

        return await this.withThreadOverlay(
            TeamThreadInfoStreams.getOverlays((key) => key.teamId === this.teamId),
            overlay,
            () =>
                API.threads.updateThread({
                    threadId: this.threadId,
                    teamId: this.teamId,
                    updateThreadRequest: { title },
                })
        );
    }

    async confirmArchive() {
        const description = ClientOverride(
            'Archived discussions will be removed, and can be found in the Recently Archived section of the web dashboard.',
            { dashboard: 'Archived discussions can be found in the Recently Archived section.' }
        );

        const response = await ClientWorkspace.instance().dialog({
            title: 'Are you sure you want to archive this discussion?',
            description,
            okTitle: 'Archive',
            cancelTitle: 'Cancel',
            destructive: true,
        });

        if (response.response === 'ok') {
            {
                await this.archive();
                void ClientWorkspace.instance().notify({
                    type: 'info',
                    title: 'The discussion has been archived.',
                });

                return true;
            }
        }
        return false;
    }

    async archive(): Promise<Thread> {
        const overlay: OverlayInstance<ApiDataStreamState<ThreadInfo[]>> = {
            overlayMutationFn: (state) => {
                if (state.$case === 'loading') {
                    return state;
                }

                const threads = state.value.filter((thread) => thread.thread.id !== this.threadId);
                return { ...state, value: threads };
            },
            shouldCancel: (state) =>
                state.$case === 'ready' && state.value.every((threadInfo) => threadInfo.thread.id !== this.threadId),
        };

        const thread = await this.withThreadOverlay(
            TeamThreadInfoStreams.getOverlays((key) => key.teamId === this.teamId),
            overlay,
            async () =>
                API.threads.archiveThread({
                    threadId: this.threadId,
                    teamId: this.teamId,
                })
        );

        // Trigger updates for FileSourceMarkStream
        createOrArchiveThreadStreamBase.updateValue({ threadId: this.threadId });

        return thread;
    }

    async confirmRestore() {
        const response = await ClientWorkspace.instance().dialog({
            title: 'Are you sure you want to restore this discussion?',
            description: 'Restored discussions will re-appear in the source code.',
            okTitle: 'Restore',
            cancelTitle: 'Cancel',
        });

        if (response.response === 'ok') {
            {
                await this.restore();
                void ClientWorkspace.instance().notify({
                    type: 'info',
                    title: 'The discussion has been restored.',
                });

                return true;
            }
        }
        return false;
    }

    async restore(): Promise<Thread> {
        const overlay: OverlayInstance<ApiDataStreamState<ThreadInfo[]>> = {
            overlayMutationFn: (state) => {
                if (state.$case === 'loading') {
                    return state;
                }

                const threads = state.value.filter((thread) => thread.thread.id !== this.threadId);
                return { ...state, value: threads };
            },
            shouldCancel: (state) =>
                state.$case === 'ready' && state.value.every((threadInfo) => threadInfo.thread.id !== this.threadId),
        };

        TeamThreadInfoStreams.refresh((key) => key.teamId === this.teamId && key.type === 'archived');
        const thread = await this.withThreadOverlay(
            TeamThreadInfoStreams.getOverlays((key) => key.teamId === this.teamId && key.type === 'archived'),
            overlay,
            async () =>
                await API.threads.restoreThread({
                    threadId: this.threadId,
                    teamId: this.teamId,
                })
        );

        // Trigger updates for FileSourceMarkStream
        const threadInfoAggregate: ThreadInfoAggregate = {
            thread,
            messages: [],
            participants: [],
            teamParticipants: [],
            experts: [],
            capabilities: {
                canReply: false,
                canUpdatePrivacy: false,
            },
        };

        createOrArchiveThreadStreamBase.updateValue({ threadId: this.threadId, thread: threadInfoAggregate });

        return thread;
    }

    async delete(): Promise<void> {
        const overlay: OverlayInstance<ApiDataStreamState<ThreadInfo[]>> = {
            overlayMutationFn: (state) => {
                if (state.$case === 'loading') {
                    return state;
                }

                const threads = state.value.filter((threadInfo) => threadInfo.thread.id !== this.threadId);
                return { ...state, value: threads };
            },
            shouldCancel: (state) => {
                return (
                    state.$case === 'ready' && state.value.every((threadInfo) => threadInfo.thread.id !== this.threadId)
                );
            },
        };

        TeamThreadInfoStreams.refresh((key) => key.teamId === this.teamId && key.type === 'archived');

        return await this.withThreadOverlay(
            TeamThreadInfoStreams.getOverlays((key) => key.teamId === this.teamId && key.type === 'archived'),
            overlay,
            async () =>
                API.threads.deleteThread({
                    threadId: this.threadId,
                    teamId: this.teamId,
                })
        );
    }

    async createQa({
        query,
        isThreadPrivate,
        mentions = [],
        attachments,
        localContext,
        emails,
    }: CreateQaProps): Promise<Thread> {
        const botTeamMemberId = await TeamMemberBotStore.instance().getBotTeamMemberId(this.teamId);
        const context =
            localContext ??
            (await ClientWorkspace.instance().localContextProvider?.getContext(this.teamId, this.threadId)) ??
            undefined;
        let title: string;
        let blocks: Block[];

        switch (query.$case) {
            case 'blocks':
                blocks = MessageTransformer.fromBytesToMessage(query.contentBytes).blocks;
                title = BlockUtils.getTitleText(blocks);
                break;

            case 'string':
                title = query.content;
                blocks = BlockUtils.createTextBlocks(query.content);
                break;
        }

        const newThread = await this.createThread({
            title,
            blocks,
            threadParticipants: [],
            mentions: [botTeamMemberId, ...mentions],
            attachments,
            isPrivate: isThreadPrivate,
            context: context?.threadContext,
            sourcemark: context?.sourceMark,
            repoId: context?.repoId,
        });
        await ProductFeedbackStore.get(this.teamId).refresh();

        if (!!emails?.length) {
            void sendThreadEmailInvites(this.teamId, this.threadId, emails);
        }

        return newThread;
    }
}
