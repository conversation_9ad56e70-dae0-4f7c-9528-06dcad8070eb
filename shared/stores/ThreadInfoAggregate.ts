import { getDistinctTeamMembers } from '@shared/webUtils/TeamMemberUtils';

import { Message, MessageAggregate, TeamMember, ThreadInfo, ThreadInfoAggregate } from '../api/models';
import { ArrayUtils, generateUnknownTeamMember } from '../webUtils';
import { ApiDataStreamState } from './ApiDataStream';
import { getCurrentTeamMember } from './PersonStore';

export type { MessageAggregate, ThreadInfoAggregate };

export function aggregateThreadInfo(
    thread: ThreadInfo,
    // repo: RepoAggregate | undefined,
    teamMembers: Map<string, TeamMember>
): ThreadInfoAggregate {
    const currentTeamMemberId = getCurrentTeamMember(thread.thread.teamId)?.id;

    const teamMemberParticipants = ArrayUtils.compact([...thread.participants, thread.thread.archivedBy]).map(
        (teamMemberId) => {
            return teamMembers.get(teamMemberId) ?? generateUnknownTeamMember(teamMemberId);
        }
    );

    const firstUneditableMessageIdx = thread.messages.findIndex(
        (message) =>
            currentTeamMemberId !== message.authorTeamMemberId &&
            !teamMembers.get(message.authorTeamMemberId)?.isUnblockedBot
    );

    const messageAggregates = thread.messages.map((message, idx) => {
        const isAuthorCurrentPerson = message.authorTeamMemberId === currentTeamMemberId;
        const isAuthorUnbot = teamMembers.get(message.authorTeamMemberId)?.isUnblockedBot;

        const canDelete = idx !== 0 && !thread.thread.archivedAt && (isAuthorCurrentPerson || !!isAuthorUnbot);
        const canEdit = isAuthorCurrentPerson && idx > firstUneditableMessageIdx;

        return joinMessageTeamMembers(message, isAuthorCurrentPerson, canEdit, canDelete, teamMembers);
    });

    const teamMemberExperts = getDistinctTeamMembers(thread.experts ?? [], teamMembers);
    const unreadMentioned = currentTeamMemberId ? getThreadUnreadMentioned(thread, currentTeamMemberId) : 0;

    return {
        ...thread,
        teamParticipants: teamMemberParticipants,
        messages: messageAggregates,
        unreadMentioned,
        experts: teamMemberExperts,
    };
}

export function getThreadTeamMembers(threadInfo: ThreadInfo): string[] {
    // This should mirror the items joined in aggregateThreadInfo above
    return ArrayUtils.distinct([
        ...threadInfo.participants,
        ...threadInfo.messages.map((message) => message.authorTeamMemberId),
        ...threadInfo.messages.flatMap((message) =>
            ArrayUtils.compactMap(message.archivedReferences ?? [], (reference) => reference.archivedBy)
        ),
        ...(threadInfo.experts ?? []),
        ...ArrayUtils.compact([threadInfo.thread.archivedBy]),
        ...threadInfo.messages.flatMap(
            (message) => message.feedback?.items.map((feedback) => feedback.teamMemberId) ?? []
        ),
    ]);
}

export function getThreadStreamTeamMembers(state: ApiDataStreamState<ThreadInfo>): string[] {
    return state.$case === 'ready' ? getThreadTeamMembers(state.value) : [];
}

export function getThreadsTeamMembers(threadInfos: ThreadInfo[]): string[] {
    return ArrayUtils.distinct(threadInfos.flatMap((threadInfo) => getThreadTeamMembers(threadInfo)));
}

export function getThreadsStreamTeamMembers(state: ApiDataStreamState<ThreadInfo[]>): string[] {
    return state.$case === 'ready' ? getThreadsTeamMembers(state.value) : [];
}

/**
 * TODO: Figure out if we should be doing this on service side
 * @param threadInfo
 * @param currentTeamMemberId
 */
function getThreadUnreadMentioned(threadInfo: ThreadInfo, currentTeamMemberId: string): number {
    let mentionCount = 0;
    if (threadInfo.unread?.latestMessage !== threadInfo.unread?.latestReadMessage) {
        const msgIdx = threadInfo.messages.findIndex((message) => message.id === threadInfo?.unread?.latestReadMessage);
        const unreadMessages = threadInfo.messages.slice(msgIdx + 1);
        mentionCount = unreadMessages.filter((message) =>
            message.mentions?.find((mention) => mention === currentTeamMemberId)
        ).length;
    }
    return mentionCount;
}

function joinMessageTeamMembers(
    message: Message,
    isAuthorCurrentPerson: boolean,
    canEdit: boolean,
    canDelete: boolean,
    teamMembers: Map<string, TeamMember>
): MessageAggregate {
    const author = teamMembers.get(message.authorTeamMemberId);

    return {
        ...message,
        author: author ?? generateUnknownTeamMember(message.authorTeamMemberId),
        isAuthorCurrentPerson,
        canDelete,
        canEdit,
        feedbackTeamMembers: message.feedback
            ? {
                  items: message.feedback.items.map((item) => ({
                      feedbackType: item.feedbackType,
                      teamMember: teamMembers.get(item.teamMemberId) ?? generateUnknownTeamMember(item.teamMemberId),
                      feedbackDescription: item.feedbackDescription,
                  })),
              }
            : undefined,
        referenceAggregates: message.references
            ? ArrayUtils.compactMap(message.references, (reference) => {
                  const archivedReferenceId = reference.archivedReferenceId;
                  if (!archivedReferenceId) {
                      return reference;
                  }
                  const archivedReference = (message.archivedReferences ?? []).find(
                      (archivedReference) => archivedReference.id === archivedReferenceId
                  );
                  if (!archivedReference) {
                      return reference;
                  }
                  const { archivedAt, archivedBy, archiveReason, archiveComment } = archivedReference;
                  return {
                      ...reference,
                      archivedAt,
                      archivedBy,
                      archivedByTeamMember: archivedBy
                          ? (teamMembers.get(archivedBy) ?? generateUnknownTeamMember(archivedBy))
                          : undefined,
                      archiveReason,
                      archiveComment,
                  };
              })
            : undefined,
        dataSourcePreset: message.dataSourcePreset,
        attachments: message.attachments,
    };
}

export function joinThreadInfoAggregateStreams(
    state: ApiDataStreamState<ThreadInfo[]>,
    teamMembers: Map<string, TeamMember>
): ApiDataStreamState<ThreadInfoAggregate[]> {
    if (state.$case === 'loading') {
        return {
            $case: 'loading',
        };
    }

    const threads = state.value.map((threadInfo) => {
        return aggregateThreadInfo(threadInfo, teamMembers);
    });

    return {
        $case: 'ready',
        value: threads,
    };
}
