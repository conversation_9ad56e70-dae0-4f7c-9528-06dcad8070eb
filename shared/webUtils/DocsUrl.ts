const baseUrl = 'https://docs.getunblocked.com';
function makeUrl(path: string): string {
    return baseUrl + path;
}

export const DocsUrl = {
    home: () => makeUrl('/'),

    publicApi: () => makeUrl('/api-reference'),
    addDocuments: () => makeUrl('/api-reference/documents/put-document'),
    publicApiQuickstart: () => makeUrl('/api-reference/quickstart'),
    mcp: () => makeUrl('/unblocked-mcp'),

    answerPreferences: () => makeUrl('/writing-questions#custom-answer-preferences'),
    azureDevOps: () => makeUrl('/data-sources/azure-devops'),
    azureDevOpsConnect: () => makeUrl('/data-sources/azure-devops#connect-your-organization-and-repositories'),
    azureDevOpsOAuth: () => makeUrl('/data-sources/azure-devops#enable-third-party-application-access-via-oauth'),
    buildkite: () => makeUrl('/data-sources/buildkite'),
    ci: () => makeUrl('/unblocked-for-ci'),
    codeReview: () => makeUrl('/code-review'),
    bitbucketPipelines: () => makeUrl('/data-sources/bitbucket-pipelines'),
    circleCi: () => makeUrl('/data-sources/circleci'),
    gitlabPipelines: () => makeUrl('/data-sources/gitlab-pipelines'),
    coda: () => makeUrl('/data-sources/coda'),
    codaApiToken: () => makeUrl('/data-sources/coda#generate-an-api-token'),
    confluenceDataCenterPatCreation: () =>
        makeUrl('/data-sources/confluence-data-center#creating-a-personal-access-token'),
    connectAccounts: () => makeUrl('/user-settings/connecting-accounts'),
    dataShield: () => makeUrl('/team-settings/data-shield'),
    dataSourcePresets: () => makeUrl('/data-sources/data-source-presets'),
    githubActions: () => makeUrl('/data-sources/github-actions'),
    googleDriveWorkspaces: () => makeUrl('/data-sources/google-drive-for-workspaces'),
    googleDriveWorkspacesEnablingKeyCreation: () =>
        makeUrl('/data-sources/google-drive-for-workspaces#enabling-key-creation'),
    googleDriveWorkspacesCustomAdminRole: () =>
        makeUrl('/data-sources/google-drive-for-workspaces#creating-a-custom-admin-role'),
    incognitoMode: () => makeUrl('/unblocked-chat/incognito-mode'),
    jiraDataCenterPatCreation: () => makeUrl('/data-sources/jira-data-center#creating-a-personal-access-token'),

    licensing: () => makeUrl('/billing/how-licensing-works'),

    notion: () => makeUrl('/data-sources/notion'),
    notionConfigureAccess: () => makeUrl('/data-sources/notion#configuring-or-updating-access-to-notion'),

    sso: () => makeUrl('/team-settings/sso'),
    ssoAwsIdentityCenter: () => makeUrl('/team-settings/sso/aws-identity-center'),
    ssoGoogleWorkspace: () => makeUrl('/team-settings/sso/google-workspace'),
    ssoMicrosoftEntra: () => makeUrl('/team-settings/sso/entra-id'),
    ssoOkta: () => makeUrl('/team-settings/sso/okta'),
    ssoPingOne: () => makeUrl('/team-settings/sso/ping-one'),

    scimGeneric: () => makeUrl('/team-settings/sso/other-saml#user-and-group-provisioning'),
    scimMicrosoftEntra: () => makeUrl('/team-settings/sso/entra-id#user-and-group-provisioning'),
    scimOkta: () => makeUrl('/team-settings/sso/okta#user-and-group-provisioning'),
    scimPingOne: () => makeUrl('/team-settings/sso/ping-one#user-and-group-provisioning'),

    slack: () => makeUrl('/data-sources/slack'),
    slackOverview: () => makeUrl('/unblocked-for-slack'),
    slackPrivateChannels: () => makeUrl('/data-sources/slack#connecting-private-channels'),
    slackChannelPreferences: () => makeUrl('/data-sources/slack#channel-preferences'),
    writingQuestions: () => makeUrl('/writing-questions'),
};
