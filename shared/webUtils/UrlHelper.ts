import { environment } from '@shared/config';

import { PathUtils } from './PathUtils/PathUtils';

const PROTOCOL_REGEX = new RegExp(/^([a-zA-Z]*:\/\/)?(www\.)?/);

export class UrlHelper {
    static MakeSearchQuery(items: Record<string, string | string[] | boolean | undefined | null>): string {
        const urlSearch = new URLSearchParams();
        Object.entries(items).forEach(([key, value]) => {
            if (typeof value === 'string') {
                urlSearch.append(key, value);
            }

            if (typeof value === 'boolean' && value) {
                urlSearch.append(key, 'true');
            }

            if (Array.isArray(value)) {
                value.forEach((subValue) => urlSearch.append(key, subValue));
            }
        });

        return urlSearch.toString();
    }

    static AddUrlSearchParams(url: string, searchParams: URLSearchParams): string {
        const mapping = urlSearchParamsToRecord(searchParams);
        return this.AddQueryParameter(url, mapping);
    }

    static AddQueryParameter(url: string, mapping: Record<string, string | string[] | undefined>): string {
        let urlObj: URL;
        const hasProtocol = url.includes('://');

        if (hasProtocol) {
            urlObj = new URL(url);
        } else {
            urlObj = new URL(url, 'http://dummy-base'); // Use a dummy base URL
        }

        const searchParams = urlObj.searchParams;

        Object.entries(mapping).forEach(([key, value]) => {
            if (Array.isArray(value)) {
                value.forEach((val) => searchParams.append(key, val));
            } else if (!!value) {
                searchParams.append(key, value);
            }
        });

        if (!hasProtocol) {
            // Reconstruct the URL manually to remove the dummy base
            return `${urlObj.pathname}${urlObj.search}${urlObj.hash}`;
        }

        return urlObj.toString();
    }

    static GetDefaultFileName(url: string): string | undefined {
        try {
            const urlObj = new URL(url);
            return PathUtils.getBasename(urlObj.pathname);
        } catch {}
    }
}

function urlSearchParamsToRecord(params: URLSearchParams): Record<string, string | string[]> {
    const record: Record<string, string | string[]> = {};

    params.forEach((value, key) => {
        if (record[key]) {
            if (Array.isArray(record[key])) {
                (record[key] as string[]).push(value);
            } else {
                record[key] = [record[key] as string, value];
            }
        } else {
            record[key] = value;
        }
    });

    return record;
}

export function withProtocol(url: string): string {
    return url.includes('://') ? url : `http://${url}`;
}

export function stripProtocol(url: string): string {
    return url.replace(PROTOCOL_REGEX, '');
}

export function createUrl(str: string, protocol?: boolean): URL {
    const urlString = protocol ? withProtocol(str) : str;

    return new URL(urlString);
}

export function isUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

export function makeUrlIfNecessary(url: string, base?: string) {
    const baseUrl = base ?? environment.webDashboardBaseUrl;
    if (isUrl(url)) {
        return url;
    }
    return `${baseUrl}${url}`;
}

export function ReplaceDashboardPathTeam(path: string, newTeamId: string): string | undefined {
    const teamPathRegex = /\/team\/[^/]+(\/.*)?$/;

    if (!teamPathRegex.test(path)) {
        return undefined;
    }

    // Replace just the teamId part
    return path.replace(teamPathRegex, `/team/${newTeamId}$1`);
}

export const generateCsvDownloadUrl = (csv: string) => {
    const csvData = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    return URL.createObjectURL(csvData);
};
