import dayjs, { Dayjs } from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import relativeTime from 'dayjs/plugin/relativeTime';
import updateLocale from 'dayjs/plugin/updateLocale';
import utc from 'dayjs/plugin/utc';

dayjs.extend(relativeTime);
dayjs.extend(isBetween);
dayjs.extend(isSameOrAfter);
dayjs.extend(updateLocale);
dayjs.extend(utc);

dayjs.updateLocale('en', {
    relativeTime: {
        future: 'in %s',
        past: '%s ago',
        s: 'a few secs',
        m: '1 min',
        mm: '%d mins',
        h: '1 hr',
        hh: '%d hrs',
        d: '1d',
        dd: '%dd',
        M: '1 mo',
        MM: '%d mos',
        y: '1 yr',
        yy: '%d yrs',
    },
});

const DateTime = {
    // ISO8601 format
    absolute: (date: Date): string => {
        return dayjs(date).format();
    },

    absolutePretty: (date: Date): string => {
        return dayjs(date).format('YYYY-MMM-D h:mmA');
    },

    numberPretty: (date: Date): string => {
        const dayjsObj = dayjs(date);
        return dayjsObj.format('M/D/YY');
    },

    shortPretty: (date: Date, withYear?: boolean): string => {
        const dayjsObj = dayjs(date);
        return withYear ? dayjsObj.format('MMM D, YYYY') : dayjsObj.format('MMM D');
    },

    shortTimePretty: (date: Date): string => {
        const dayjsObj = dayjs(date);
        return dayjsObj.format('MMM D, YYYY, h:mm A');
    },

    relativeTime: (date: Date): string => {
        return dayjs(date).fromNow();
    },

    isSameDay: (date: Date, from?: Date): boolean => {
        const fromObj = dayjs(from);
        const dayjsObj = dayjs(date);
        return fromObj.isSame(dayjsObj, 'day');
    },

    isDayAgo: (date: Date, daysAgo: number = 1): boolean => {
        const now = dayjs();
        const dayjsObj = dayjs(date);
        return now.add(-daysAgo, 'day').isSame(dayjsObj, 'day');
    },

    isBetweenDays: (date: Date, daysAgo: number = 1, from?: Date): boolean => {
        const now = dayjs(from);
        const dayjsObj = dayjs(date);
        return dayjsObj.isBetween(now.add(-daysAgo, 'day'), now, 'date', '(]');
    },

    isSameYear: (date: Date, from?: Date): boolean => {
        const fromObj = dayjs(from);
        const dayjsObj = dayjs(date);
        return fromObj.isSame(dayjsObj, 'year');
    },

    sinceEpoch: (epochMs: number): Date => {
        return new Date(Math.floor(epochMs / 1000));
    },

    /*
        If the given date happened recently (today),
        return a relative string ("2 hours ago").  If it was
        earlier then that, return a short absolute date string
        ("April 12"). If it's been >1 year, return the shortened 
        month and year ("Oct 2021").
    */
    recentRelative: (date: Date, from?: Date): string => {
        const now = from ? dayjs(from) : dayjs();
        const dayjsObj = dayjs(date);
        const timeAgo = from ? `${dayjsObj.from(from, true)} ago` : `${dayjsObj.fromNow(true)} ago`;

        if (now.isSame(dayjsObj, 'day')) {
            return timeAgo;
        }

        if (now.add(-1, 'day').isSame(dayjsObj, 'day')) {
            return 'yesterday';
        }

        if (!now.isSame(dayjsObj, 'year')) {
            return dayjsObj.format('MMM YYYY');
        }

        return DateTime.shortPretty(date);
    },

    getMonth: (date?: Date, withYear?: boolean): string => {
        const dayjsObj = dayjs(date);
        return withYear ? dayjsObj.format('MMMM YYYY') : dayjsObj.format('MMMM');
    },

    utc: (date: Date): Dayjs => {
        return dayjs.utc(date);
    },

    /**
     * Returns the later (more recent) of two dates.
     */
    latest: (date1: Date, date2: Date): Date => {
        return dayjs(date1).isAfter(dayjs(date2)) ? date1 : date2;
    },
};

export default DateTime;
