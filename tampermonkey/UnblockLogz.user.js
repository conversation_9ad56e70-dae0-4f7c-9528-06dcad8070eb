// ==UserScript==
// @name         Unblock Logz
// @namespace    http://tampermonkey.net/
// @author:      <EMAIL>
// @version      2.2
// @description  Highlights DEBUG/INFO/WARN/ERROR, linkifies UUIDs to point to admin console, linkifies platform.version (in separate cell) to GitHub commit, adds link to Honeycomb data
// @match        https://app.logz.io/*
// @updateURL    https://github.com/NextChapterSoftware/unblocked/raw/refs/heads/main/tampermonkey/UnblockLogz.user.js
// @downloadURL  https://github.com/NextChapterSoftware/unblocked/raw/refs/heads/main/tampermonkey/UnblockLogz.user.js
// @grant        none
// ==/UserScript==

(function () {
    'use strict';

    // Colors for each log level, better fitting Logz.io's scheme
    const COLORS = {
        DEBUG: '#9E9E9E',   // Grey
        INFO: '#2196F3',   // Blue
        WARN: '#FFC107',   // Amber
        ERROR: '#F44336'    // Red
    };

    // Regex for each level
    const DEBUG_REGEX = /(?<!\w)(DEBUG)(?!\w)/g;
    const INFO_REGEX = /(?<!\w)(INFO)(?!\w)/g;
    const WARN_REGEX = /(?<!\w)(WARN)(?!\w)/g;
    const ERROR_REGEX = /(?<!\w)(ERROR)(?!\w)/g;

    // Regex for UUID
    const UUID_REGEX = /[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi;

    // For environment detection
    function environmentForTable(table) {
        let env = 'prod';
        const rows = table.querySelectorAll('tr[data-logz-test-context="table-row"]');
        for (const row of rows) {
            const cells = row.querySelectorAll('td');
            if (cells.length < 2) {
                continue;
            }
            const fieldName = cells[0].innerText.trim();
            const fieldValue = cells[1].innerText.trim().toLowerCase();
            if (fieldName === 'environment') {
                if (fieldValue === 'dev') {
                    env = 'dev';
                } else if (fieldValue === 'prod') {
                    env = 'prod';
                }
            }
        }
        return env;
    }

    function getBaseUrlFor(element) {
        const table = element.closest('table');
        if (!table) {
            return 'https://admin.prod.getunblocked.com';
        }
        const env = environmentForTable(table);
        return env === 'dev'
            ? 'https://admin.dev.getunblocked.com'
            : 'https://admin.prod.getunblocked.com';
    }

    /**
     * Special logic for platform.version:
     * If the row's first cell is literally "platform.version", then linkify
     * the second cell's text (if it looks like a hex commit).
     */
    function linkifyPlatformVersion(row) {
        const cells = row.querySelectorAll('td');
        if (cells.length < 2) {
            return;
        }

        const firstCellText = cells[0].innerText.trim();
        if (firstCellText === 'platform.version') {
            const secondCell = cells[1];
            const versionText = secondCell.innerText.trim();

            // If it looks like a hex commit (7..40 hex chars), replace just that text
            if (/^[0-9a-f]{7,40}$/i.test(versionText)) {
                const anchorHTML = `<a href="https://github.com/NextChapterSoftware/unblocked/commit/${versionText}" target="_blank" style="color: #0070f3; text-decoration: underline;">${versionText}</a>`;
                secondCell.innerHTML = secondCell.innerHTML.replace(versionText, anchorHTML);
            }
        }
    }

    /**
     * Builds a Honeycomb trace URL from the provided parameters
     * @param {string} span_id - The span ID from the log entry
     * @param {string} trace_id - The trace ID from the log entry
     * @param {string} timestampStr - The timestamp string from the log entry (e.g., "Mar 28 10:54:34.986" or "2025-03-28T18:30:26.328+0000")
     * @param {string} service - The service name from the log entry
     * @returns {string|null} - The complete Honeycomb URL or null if parsing failed
     */
    function buildHoneycombLink(span_id, trace_id, timestampStr, service) {
        let date;

        // First try to parse the timestamp as an ISO 8601 format (e.g., "2025-03-28T18:30:26.328+0000")
        if (timestampStr.includes('T') && (timestampStr.includes('Z') || timestampStr.includes('+') || timestampStr.includes('-'))) {
            // This looks like an ISO format timestamp, try to parse it directly
            date = new Date(timestampStr);
        } else {
            // This is likely the short format (e.g., "Mar 28 10:54:34.986"), so add the current year
            const currentYear = new Date().getFullYear();

            // Parse timestamp: "Mar 28 10:54:34.986" -> "Mar 28 2025 10:54:34.986"
            const parts = timestampStr.split(' ');
            if (parts.length >= 2) {
                // Insert the current year after the day
                parts.splice(2, 0, currentYear.toString());
                const timestampWithYear = parts.join(' ');

                date = new Date(timestampWithYear);
            }
        }

        // If we successfully parsed the date
        if (date && !isNaN(date.getTime())) {
            // Create a time window of +/- 15 minutes (900000 ms) around the event for a wider search
            const timeMs = date.getTime();
            const startMs = timeMs - 900000; // 15 minutes earlier
            const endMs = timeMs + 900000;  // 15 minutes later

            // Convert to seconds for Honeycomb's timestamp format
            const startTs = Math.floor(startMs / 1000);
            const endTs = Math.ceil(endMs / 1000);

            // Build and return the complete Honeycomb trace URL
            return `https://ui.honeycomb.io/unblocked/environments/production/datasets/${service}/trace?trace_id=${trace_id}&span=${span_id}&trace_start_ts=${startTs}&trace_end_ts=${endTs}`;
        }

        // If parsing failed
        console.warn(`Failed to parse timestamp: "${timestampStr}"`);
        return null;
    }

    /**
     * Searches a log details table for Honeycomb trace information and adds a link row if found
     * @param {HTMLElement} table - The log details table to process
     */
    function gatherHoneycombDataAndLink(table) {
        // Skip if we've already processed this table to avoid duplication
        if (table.dataset.honeycombProcessed === 'true') {
            return;
        }

        // The four fields we need to build a Honeycomb trace link
        let spanId = null;
        let traceId = null;
        let timestampStr = null;
        let serviceVal = null;

        // Scan all rows in the table to find the fields we need
        const rows = table.querySelectorAll('tr[data-logz-test-context="table-row"]');
        for (const row of rows) {
            const cells = row.querySelectorAll('td');
            if (cells.length < 2) {
                continue;
            }

            const fieldName = cells[0].innerText.trim();
            const fieldValue = cells[1].innerText.trim();

            // Collect the fields needed for the Honeycomb link
            switch (fieldName) {
                case 'span_id':
                    spanId = fieldValue;
                    break;
                case 'trace_id':
                    traceId = fieldValue;
                    break;
                case '@timestamp':
                    timestampStr = fieldValue;
                    break;
                case 'service':
                    serviceVal = fieldValue;
                    break;
            }
        }

        // Only create a link if we found all the required information
        if (spanId && traceId && timestampStr && serviceVal) {
            const honeycombUrl = buildHoneycombLink(spanId, traceId, timestampStr, serviceVal);

            if (honeycombUrl) {
                // Create a new table row with the Honeycomb link
                const newRow = document.createElement('tr');
                newRow.setAttribute('data-logz-test-context', 'table-row');
                newRow.innerHTML = `
          <td style="padding: 4px 8px; border-bottom: 1px solid #e0e0e0;">Honeycomb Trace</td>
          <td style="padding: 4px 8px; border-bottom: 1px solid #e0e0e0;">
            <a href="${honeycombUrl}" target="_blank" style="color: #0070f3; text-decoration: underline;">
              View trace in Honeycomb
            </a>
          </td>
        `;

                // Add the new row to the table
                const tableBody = table.querySelector('tbody') || table;
                tableBody.appendChild(newRow);
            }
        }

        // Mark the table as processed to avoid reprocessing
        table.dataset.honeycombProcessed = 'true';
    }

    // Transform text (highlight + linkify) in a single text node
    function transformText(text, baseUrl) {
        let changed = false;
        let output = text;

        // Highlight log level keywords
        if (
            DEBUG_REGEX.test(output) ||
            INFO_REGEX.test(output) ||
            WARN_REGEX.test(output) ||
            ERROR_REGEX.test(output)
        ) {
            DEBUG_REGEX.lastIndex = 0;
            INFO_REGEX.lastIndex = 0;
            WARN_REGEX.lastIndex = 0;
            ERROR_REGEX.lastIndex = 0;
            output = output
                .replace(DEBUG_REGEX, `<span style="color: ${COLORS.DEBUG}; font-weight: bold;">$1</span>`)
                .replace(INFO_REGEX, `<span style="color: ${COLORS.INFO};  font-weight: bold;">$1</span>`)
                .replace(WARN_REGEX, `<span style="color: ${COLORS.WARN};  font-weight: bold;">$1</span>`)
                .replace(ERROR_REGEX, `<span style="color: ${COLORS.ERROR}; font-weight: bold;">$1</span>`);
            changed = true;
        }

        // Linkify UUIDs
        if (UUID_REGEX.test(output)) {
            UUID_REGEX.lastIndex = 0;
            output = output.replace(UUID_REGEX, match => {
                return `<a href="${baseUrl}/search?q=${match}" target="_blank" style="color: #0070f3; text-decoration: underline;">${match}</a>`;
            });
            changed = true;
        }

        return changed ? output : null;
    }

    // Rewrite a single text node
    function processTextNode(textNode, baseUrl) {
        const parent = textNode.parentNode;
        if (!parent) {
            return;
        }

        const original = textNode.nodeValue || '';
        if (!original) {
            return;
        }

        const changedHTML = transformText(original, baseUrl);
        if (changedHTML) {
            const span = document.createElement('span');
            span.innerHTML = changedHTML;
            parent.replaceChild(span, textNode);
        }
    }

    // Rewrite all text nodes within a single row
    function highlightRow(row) {
        // First handle platform.version in separate cells
        linkifyPlatformVersion(row);

        // Then check for Honeycomb data and create link if available
        // This needs to find the parent table that contains this row
        const table = row.closest('table');
        if (table) {
            gatherHoneycombDataAndLink(table);
        }

        // Then do normal text scanning (log levels, UUID)
        const baseUrl = getBaseUrlFor(row);
        const walker = document.createTreeWalker(row, NodeFilter.SHOW_TEXT, null);
        let node;
        while (node = walker.nextNode()) {
            processTextNode(node, baseUrl);
        }

        row.setAttribute('data-unblocked', true)
    }

    // IntersectionObserver callback
    function onIntersect(entries, observer) {
        for (const entry of entries) {
            if (entry.isIntersecting) {
                const row = entry.target;
                row.attributes['data-unblocked'] || highlightRow(row);
                observer.unobserve(row); // Only highlight once
            }
        }
    }

    // Create a single intersection observer
    const rowObserver = new IntersectionObserver(onIntersect, {
        root: null,
        rootMargin: '0px',
        threshold: 0,  // As soon as the row is even slightly visible
    });

    // Poll for new rows periodically
    const updateRowsEvery = (millis) => {
        let clock = Date.now()
        const frame = () => {
            const delta = Date.now() - clock
            if (delta > millis) {
                updateRows()
                clock = Date.now()
            }
            window.requestAnimationFrame(frame)
        }
        return frame
    }

    function updateRows() {
        document.querySelectorAll('tr[data-logz-test-context="table-row"]').forEach(row => {
            row.attributes['data-unblocked'] || rowObserver.observe(row)
        })
    }

    // Check for new rows at intervals
    window.requestAnimationFrame(updateRowsEvery(100));
})();
