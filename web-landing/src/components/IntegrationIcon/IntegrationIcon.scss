@use 'layout' as *;
@use 'colors' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'layout-mixin' as *;

.integration_icon {
    width: 60px;
    height: 60px;
    border-radius: 18px;
    background-color: rgb(0 0 0 / 20%);
    border: 1px solid rgb(0 0 0 / 20%);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    @include backdrop-filter;

    @include breakpoint(lg) {
        width: 70px;
        height: 70px;
    }

    img {
        height: 32px;

        @include breakpoint(lg) {
            height: 40px;
        }
    }
}
