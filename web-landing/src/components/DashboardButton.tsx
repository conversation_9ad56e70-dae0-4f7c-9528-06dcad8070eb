import { useCallback, useMemo } from 'react';

import { environment } from '@shared/config';
import { Events } from '@shared/metrics';
import { Button, ButtonVariant, Props as ButtonProps } from '@shared/webComponents/Button/Button';
import { DashboardUrls } from '@shared/webUtils';
import { UmamiTrackEvent } from '@shared/webUtils/Trackers/UmamiTracker';
import { makeUrlIfNecessary } from '@shared/webUtils/UrlHelper';

import { LandingButton } from './LandingButton';

export type TrackerEvent = 'getStarted' | 'signIn' | 'mcp';

interface DashboardButtonProps extends Omit<ButtonProps, 'onClick' | 'variant'> {
    dashboardQuery?: { key: string; value: string };
    trackerEvent?: TrackerEvent;
    variant?: ButtonVariant | 'landing';
}

export const DashboardButton = ({
    dashboardQuery,
    trackerEvent = 'getStarted',
    variant = 'landing',
    ...props
}: DashboardButtonProps) => {
    const dashboardUrl = useMemo(() => {
        if (trackerEvent === 'getStarted') {
            return makeUrlIfNecessary(DashboardUrls.getStarted());
        }

        if (trackerEvent === 'mcp') {
            return makeUrlIfNecessary(DashboardUrls.getStartedMcp());
        }

        return environment.webDashboardBaseUrl;
    }, [trackerEvent]);

    const onClick = useCallback(async () => {
        // Have to wait for tracking to complete before navigating, or the network
        // request will be dropped
        await UmamiTrackEvent(trackerEvent);
        await Events.session.triggerAction(window.location.href, trackerEvent);

        window.location.href = `${dashboardUrl}${
            dashboardQuery ? `?${dashboardQuery.key}=${dashboardQuery.value}` : ''
        }`;
    }, [dashboardUrl, dashboardQuery, trackerEvent]);

    if (variant === 'landing') {
        return <LandingButton onClick={onClick} ignoreLoading {...props} />;
    }
    return <Button variant={variant} onClick={onClick} ignoreLoading {...props} />;
};
