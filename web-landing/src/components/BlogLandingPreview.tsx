import Link from 'next/link';

import { Icon } from '@shared/webComponents/Icon/Icon';

import { faArrowRight } from '@fortawesome/pro-solid-svg-icons/faArrowRight';

import './BlogLandingPreview.scss';

interface BlogLandingPreviewProps {
    pictureUrl: string;
    title: string;
    articleUrl: string;
    description: string;
}

export function BlogLandingPreview({ pictureUrl, title, articleUrl, description }: BlogLandingPreviewProps) {
    return (
        <div className="blog_landing_preview">
            <div className="blog_landing_preview__info">
                <Link href={articleUrl} className="blog_article__title">
                    {title}
                </Link>
                <div className="blog_article__description">{description}</div>
                <div className="blog_article__readmore">
                    <Link href={articleUrl}>
                        Read more
                        <Icon icon={faArrowRight} size={13} className="integrations_icon" />
                    </Link>
                </div>
            </div>
            <img className="blog_article__picture" src={pictureUrl} />
        </div>
    );
}
