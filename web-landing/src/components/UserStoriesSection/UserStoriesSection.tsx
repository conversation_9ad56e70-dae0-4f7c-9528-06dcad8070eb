import Link from 'next/link';

import { Button } from '@shared/webComponents/Button/Button';
import { Icon } from '@shared/webComponents/Icon/Icon';

import appDirectColor from '@clientAssets/landing/logo-app-direct.svg';
import appDirect from '@clientAssets/landing/logo-app-direct-mono.svg';
import bigCartel from '@clientAssets/landing/logo-big-cartel-mono.svg';
import clio from '@clientAssets/landing/logo-clio-mono.svg';
import criblColor from '@clientAssets/landing/logo-cribl.svg';
import cribl from '@clientAssets/landing/logo-cribl-mono.svg';
import drataColor from '@clientAssets/landing/logo-drata.svg';
import drata from '@clientAssets/landing/logo-drata-mono.svg';

import { faArrowRight } from '@fortawesome/pro-regular-svg-icons/faArrowRight';

import { EmphasisText } from '../EmphasisText/EmphasisText';

import './UserStoriesSection.scss';

const UserStoryBlock = ({
    company,
    imgUrl,
    route,
    children,
}: {
    company: string;
    imgUrl: string;
    route: string;
    children: React.ReactNode;
}) => {
    return (
        <Link href={route} className="user_story_block">
            <img className="user_story_block__logo" src={imgUrl} title={company} />
            <h3 className="user_story_block__quote">{children}</h3>
            <Button as="link" className="user_story_block__button">
                <span className="landing__description">Read more</span>
                <Icon icon={faArrowRight} />
            </Button>
        </Link>
    );
};

export const UserStoriesSection = () => {
    return (
        <div className="user_stories_section">
            <div className="user_stories_section__header">
                <h2>
                    Teams who use Unblocked have <EmphasisText>saved 300,000 hours</EmphasisText> (and counting).
                </h2>
                <p className="landing__description user_stories_section__subheader">
                    There&rsquo;s a lot of hype around AI &mdash; we get it. But Unblocked actually works, and you
                    don&rsquo;t just have to take our word for it.
                </p>
                <a
                    href="https://getunblocked.com/blog/?categories=Customer+stories"
                    className="user_stories_section__viewall"
                >
                    <span className="landing__description">See all customer stories</span>
                    <Icon icon={faArrowRight} />
                </a>
            </div>

            <div className="users_stories_section__blocks user_stories_section__blocks_mobile">
                <UserStoryBlock company="Cribl" imgUrl={criblColor.src} route="/blog/customer-story-cribl">
                    &ldquo;Unblocked is our number one tool for finding the information we should know but
                    don&rsquo;t.&rdquo;
                </UserStoryBlock>
                <UserStoryBlock company="Drata" imgUrl={drataColor.src} route="/blog/customer-story-drata">
                    &ldquo;Our team saves 1 to 2 hours per engineer each day with Unblocked.&rdquo;
                </UserStoryBlock>
                <UserStoryBlock company="AppDirect" imgUrl={appDirectColor.src} route="/blog/customer-story-app-direct">
                    &ldquo;Unblocked has been a game-changer for efficiency and focus.&rdquo;
                </UserStoryBlock>
            </div>

            <div className="user_stories_section__companies">
                <img src={drata.src} />
                <img src={bigCartel.src} />
                <img src={clio.src} />
                <img src={cribl.src} />
                <img src={appDirect.src} />
            </div>

            <div className="user_stories_section__grid">
                <div className="user_stories_section__grid__column user_stories_section__grid__column_left">
                    <div className="user_stories_section_grid__spacer_block" />
                    <UserStoryBlock company="Cribl" imgUrl={criblColor.src} route="/blog/customer-story-cribl">
                        &ldquo;Unblocked is our number one tool for finding the information we should know but
                        don&rsquo;t.&rdquo;
                    </UserStoryBlock>
                    <div className="user_stories_section_grid__spacer_block" />
                </div>
                <div className="user_stories_section__grid__column user_stories_section__grid__column_right">
                    <div className="user_stories_section_grid__spacer_block" />
                    <UserStoryBlock company="Drata" imgUrl={drataColor.src} route="/blog/customer-story-drata">
                        &ldquo;Our team saves 1 to 2 hours per engineer each day with Unblocked.&rdquo;
                    </UserStoryBlock>
                    <UserStoryBlock
                        company="AppDirect"
                        imgUrl={appDirectColor.src}
                        route="/blog/customer-story-app-direct"
                    >
                        &ldquo;Unblocked has been a game-changer for efficiency and focus.&rdquo;
                    </UserStoryBlock>
                    <div className="user_stories_section_grid__spacer_block" />
                </div>
            </div>
        </div>
    );
};
