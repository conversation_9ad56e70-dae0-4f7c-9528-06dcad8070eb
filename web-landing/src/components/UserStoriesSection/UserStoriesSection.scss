@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;

@use '../../app/landing-colors.scss' as *;
@use '../../app/landing-mixin.scss' as *;

.user_stories_section {
    @include flex-column-center;
    @include fill-available;

    &:after {
        content: '';
        position: absolute;
        height: 1.5px;
        width: 90%;
        left: calc(50% - 45%);
        bottom: -20px;
        background: $landing-magenta-20;
        mask-image: linear-gradient(to right, transparent, black 25%, black 75%, transparent);
    }

    @include breakpoint(sm) {
        &:after {
            width: 50%;
            left: calc(50% - 25%);
        }
    }

    h2 {
        color: themed($text);
        text-align: center;
        margin-bottom: $spacer-20;
    }

    .user_stories_section__header {
        max-width: 1024px;
    }

    .user_stories_section__subheader {
        text-align: center;
    }

    .user_stories_section__viewall {
        @include flex-center-center;
        gap: $spacer-12;
        font-weight: 300;
        text-decoration: none;
        margin-top: $spacer-20;

        @include breakpoint(lg) {
            gap: $spacer-12;
            justify-content: left;
        }

        .icon {
            margin-top: $spacer-2;
            height: 14px;
            width: 14px;

            @include breakpoint(md) {
                margin-top: $spacer-4;
                height: 16px;
                width: 16px;
            }
        }
    }

    .user_stories_section__grid {
        display: none;
    }

    .users_stories_section__blocks {
        display: grid;
        grid-template-columns: 1fr;
        gap: $spacer-24;
        align-items: stretch;
        margin: $spacer-24 0 $spacer-48;

        @include breakpoint(md) {
            margin: $spacer-48 0;
        }

        @include breakpoint(lg) {
            display: none;
        }
    }

    .user_stories_section__companies {
        @include flex-center-center;

        gap: $spacer-8 $spacer-20;
        flex-wrap: wrap;
        opacity: 0.5;
        max-width: 400px;

        img {
            height: 50px;
            width: auto;
        }

        @include breakpoint(sm) {
            gap: $spacer-8 $spacer-32;
        }

        @include breakpoint(md) {
            img {
                height: 60px;
                width: auto;
            }
        }

        @include breakpoint(lg) {
            max-width: 500px;
            gap: $spacer-8 $spacer-40;
        }

        @include breakpoint(xl) {
            gap: $spacer-8 $spacer-60;
        }
    }

    @include breakpoint(lg) {
        display: grid;
        grid-template:
            'header quotes' auto
            'logos quotes' auto / 1fr 1.5fr;
        gap: $spacer-40;
        align-items: center;
        position: relative;

        &:after {
            content: '';
            position: absolute;
            height: 1.5px;
            width: 800px;
            left: calc(50% - 400px);
            bottom: $spacer-12;
            background: $landing-magenta-20;
            mask-image: linear-gradient(to right, transparent, black 25%, black 75%, transparent);
        }

        .user_stories_section__header {
            grid-area: header;
            align-self: end;

            * {
                text-align: left;
            }
        }

        .user_stories_section__grid {
            grid-area: quotes;
            display: grid;
            gap: $spacer-28;
            grid-template-columns: 1fr 1fr;

            .user_stories_section__grid__column {
                @include flex-column;
                gap: $spacer-28;
            }

            .user_stories_section_grid__spacer_block {
                border-radius: $border-radius-12;
                box-shadow: 0 0 8px 0 $landing-magenta-8;
                border: 1px solid transparent;
                background-color: $white-15;
                min-height: 80px;
                flex: 1;

                @include fill-available;
            }
        }

        .user_stories_section__companies {
            grid-area: logos;
            align-self: start;
            @include flex-center-start;
        }
    }

    @include breakpoint(xl) {
        gap: $spacer-60;
        grid-template:
            'header quotes' auto
            'logos quotes' auto / 1fr 1.2fr;
    }
}

.user_story_block {
    display: grid;
    grid-template:
        'icon' 37px
        'quote' 1fr
        'link' auto / 1fr;
    gap: $spacer-12;
    padding: $spacer-16;
    background-color: $white-15;
    border-radius: $border-radius-12;
    box-shadow:
        0px 16px 100px 0px $landing-magenta-8,
        0px 1px 2px 0px $landing-magenta-24;
    transition: border-color 0.2s ease-in;
    border: 1px solid transparent;
    text-decoration: none;

    @include breakpoint(md) {
        padding: $spacer-24;
        gap: $spacer-24;
    }

    &:hover {
        border-color: $landing-magenta-100;
        text-decoration: none;
    }

    .user_story_block__logo {
        grid-area: icon;
        height: 37px;
        width: auto;
    }

    h3.user_story_block__quote {
        grid-area: quote;
        font-size: 19px;
        line-height: 27px;
        letter-spacing: -0.276px;
        font-family: $sofia-sans;
        font-style: italic;
        font-weight: $font-weight-normal;
        color: $landing-dark-purple-100;

        @include breakpoint(md) {
            font-size: 24px;
            line-height: 32px;
            letter-spacing: -0.348px;
        }
    }

    .user_story_block__button {
        grid-area: link;

        @include flex-center($spacer-12);
        justify-self: start;
        align-self: end;

        span {
            font-weight: $font-weight-light;
        }

        .icon {
            margin-top: $spacer-2;
            height: 14px;
            width: 14px;

            @include breakpoint(md) {
                margin-top: $spacer-4;
                height: 16px;
                width: 16px;
            }
        }

        &:hover {
            text-decoration: none;
        }

        @include breakpoint(md) {
            .landing__description {
                font-size: 24px;
                line-height: 32px;
            }
        }
    }
}
