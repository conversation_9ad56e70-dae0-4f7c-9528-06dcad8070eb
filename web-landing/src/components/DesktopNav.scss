@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'colors' as *;

@use '../app/landing-colors.scss' as *;

.desktop_nav {
    &.desktop_nav__dark {
        .popover__canvas {
            backdrop-filter: blur(30px);
        }
    }
    @include flex-center-between;
    transition: background 0.2s ease-in-out;
    padding: $spacer-16 $spacer-20;
    position: relative;

    .desktop_nav__home {
        z-index: 1;
        display: flex;
        gap: $spacer-32;
    }

    .desktop_nav__menu {
        @include flex-center-center;

        .desktop_nav__links {
            @include flex-center($spacer-12);

            @include breakpoint(lg) {
                gap: $spacer-16;
            }

            & > a,
            & > .desktop_nav__about {
                padding: $spacer-2 $spacer-12;
                font-size: 15px;
                line-height: 20px;
                font-weight: 400;
                color: var(--desktop-nav-link-color);
                transition: background-color 0.3s;
                text-decoration: none;
                border-radius: $border-radius-8;

                &:hover {
                    background-color: var(--desktop-nav-link-hover-background-color);
                    text-decoration: none;
                }
            }

            .desktop_nav__about {
                display: inline-flex;
                padding: 0;

                .popover__canvas {
                    background-color: var(--desktop-nav-popover-canvas);
                    top: 100%;
                    left: 0;
                    box-shadow: 0 10px 30px var(--desktop-nav-popover-shadow);
                }

                .desktop_nav__about_header {
                    @include flex-center($spacer-3);

                    padding: $spacer-2 $spacer-8;
                    border-radius: $border-radius-8;
                    // transition: 0.3s;

                    &.desktop_nav__about_header__open:not(.desktop_nav__about_header__active) {
                        background-color: var(--desktop-nav-about-header-background);
                        border-radius: $border-radius-8;
                    }
                }

                .desktop_nav__about_body {
                    @include flex-column;

                    padding: $spacer-4;

                    .desktop_nav__about_item {
                        @include flex-start($spacer-10);

                        color: var(--desktop-nav-about-item-color);
                        font-size: $font-size-15;
                        font-weight: $font-weight-normal;
                        text-decoration: none;
                        padding: $spacer-12 $spacer-36 $spacer-12 $spacer-12;
                        border-radius: $border-radius-6;
                        // transition: background-color 0.3s ease-in;

                        &:hover {
                            background-color: var(--desktop-nav-about-item-hover-background);
                        }

                        .icon {
                            color: var(--desktop-nav-about-item-icon-color);
                            margin-top: $spacer-3;

                            &.fa-user-check {
                                .fa-primary {
                                    opacity: 0.4;
                                }

                                .fa-secondary {
                                    opacity: 1;
                                }
                            }
                        }

                        .desktop_nav__about_item_label {
                            @include flex-column;

                            & > span:first-of-type {
                                color: var(--desktop-nav-about-item-color);
                                font-size: $font-size-16;
                            }

                            & > span:last-of-type {
                                color: var(--desktop-nav-about-item-description-color);
                                margin-top: -$spacer-2;
                            }
                        }
                    }
                }
            }

            .desktop_nav__about_header__active,
            & > a.active {
                outline: none;
                color: var(--desktop-nav-link-active);
                font-weight: 600;
            }
        }
    }

    .desktop_nav__buttons {
        display: flex;
        gap: $spacer-16;
        z-index: 1;
        align-items: center;

        .button {
            font-size: $font-size-15;

            &.button__primary {
                background-color: var(--desktop-nav-primary-button-color);
                border-color: var(--desktop-nav-primary-button-color);
                border-radius: $border-radius-6;
            }

            &.button__outline {
                border-color: var(--desktop-nav-primary-button-color);
                color: var(--desktop-nav-primary-button-color);
                border-radius: $border-radius-6;

                &:hover {
                    background: transparent;
                }
            }
        }

        .landing_button {
            font-size: $font-size-14;
            padding: $spacer-10 $spacer-12;
            border-radius: $border-radius-6;
        }

        .desktop_nav__about_item_label {
            background: none;
            border: none;
            color: var(--desktop-nav-about-item-color) !important;
            font-size: $font-size-15;
            line-height: $line-height-20;
            font-weight: $font-weight-normal;
            padding: $spacer-2 $spacer-12;

            &:hover {
                background-color: var(--desktop-nav-about-item-hover-background) !important;
                text-decoration: none;
            }
        }
    }
}
