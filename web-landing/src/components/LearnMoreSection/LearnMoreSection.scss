@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'colors' as *;

.learn_more_section {
    @include flex-column-center;
    flex-direction: column-reverse;
    text-align: center;
    gap: $spacer-12;

    .learn_more_section__body {
        h2 {
            color: $white-80;
        }

        p {
            color: rgba(218 214 253 / 80%);
        }

        a {
            color: $pale-violet-alternate;
            text-decoration: none;
            &:hover {
                color: $pale-violet-alternate;
                text-decoration: underline;
            }

            font-style: normal;
            font-weight: 400;

            font-size: 16px;
            line-height: 20px; /* 126.316% */
            letter-spacing: -0.232px;
        }
    }

    .learn_more__icon {
        width: 120px;
        height: 126px;
    }

    @include breakpoint(md) {
        @include flex-center-between;
        flex-direction: row;
        text-align: start;
        .learn_more__icon {
            width: 235px;
            height: 250px;
        }

        .learn_more_section__body {
            a {
                font-size: 19px;
                line-height: 24px;
                letter-spacing: -0.276px;
            }
        }
    }
}
