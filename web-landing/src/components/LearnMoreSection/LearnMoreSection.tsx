import { ReactNode } from 'react';

import { Icon, IconSrc } from '@shared/webComponents/Icon/Icon';

import './LearnMoreSection.scss';

interface Props {
    title: string;
    description: string;
    icon: IconSrc;
    learnMoreLink: ReactNode;
}

export const LearnMoreSection = ({ title, description, icon, learnMoreLink }: Props) => {
    return (
        <div className="learn_more_section">
            <div className="learn_more_section__body">
                <h2>{title}</h2>
                <p>{description}</p>
                {learnMoreLink}
            </div>
            <Icon className="learn_more__icon" icon={icon} />
        </div>
    );
};
