import { RefObject, useEffect } from 'react';

import { useIntersectionContext } from '@shared/webComponents/IntersectionContext/IntersectionContext';

export const useRegisterThemeElement = (ref: RefObject<Element>, key: string) => {
    const { registerElement } = useIntersectionContext();

    useEffect(() => {
        const unregister = registerElement(key, ref);
        return () => unregister();
    }, [ref, key, registerElement]);
};

export const useRegisterDarkElement = (ref: RefObject<Element>) => {
    useRegisterThemeElement(ref, 'darkHeader');
};
