@use 'theme' as *;
@use 'colors' as *;

@use '../app/landing-colors.scss' as *;

.desktop_nav {
    &.desktop_nav__light {
        --desktop-nav-link-color: #{$dark-purple-100};
        --desktop-nav-link-active: #{themed($link)};
        --desktop-nav-link-hover-background-color: #{$light-purple-50};
        --desktop-nav-popover-canvas: #{$white-100};
        --desktop-nav-popover-shadow: rgb(31 26 36 / 25%);
        --desktop-nav-about-header-background: #{$light-purple-50};
        --desktop-nav-about-item-color: #{themed($text)};
        --desktop-nav-about-item-description-color: #{themed($text-tertiary)};
        --desktop-nav-about-item-icon-color: #{$landing-magenta-100};
        --desktop-nav-about-item-hover-background: #{$indigo-08a};
        --desktop-nav-primary-button-color: #{$indigo-100};
    }
    &.desktop_nav__dark {
        --desktop-nav-link-color: #{$white-100};
        --desktop-nav-link-active: #{$pale-violet-alternate};
        --desktop-nav-link-hover-background-color: #{$white-16};
        --desktop-nav-popover-canvas: #{$white-16};
        --desktop-nav-popover-shadow: #{$white-16};
        --desktop-nav-about-header-background: #{$white-16};
        --desktop-nav-about-item-color: #{$white-100};
        --desktop-nav-about-item-icon-color: #{$pale-violet-alternate};
        --desktop-nav-about-item-description-color: #{$white-40};
        --desktop-nav-about-item-hover-background: #{$white-16};
        --desktop-nav-primary-button-color: #{$pale-violet-alternate};
    }
}
