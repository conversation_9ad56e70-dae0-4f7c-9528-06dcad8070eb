import { useEffect, useRef, useState } from 'react';

import { useIntersectionObserver } from '@shared/hooks/useIntersectionObserver';

interface Props extends React.VideoHTMLAttributes<HTMLVideoElement>, IntersectionObserverInit {}

export function AutoPlayVideo({ autoPlay, poster, delay = 0, ...props }: Props & { delay?: number }) {
    const videoRef = useRef<HTMLVideoElement>(null);
    const [isVisible, ref] = useIntersectionObserver(props);
    const [displayPoster, setDisplayPoster] = useState(true);

    useEffect(() => {
        if (autoPlay && videoRef.current) {
            if (videoRef.current.ended) {
                videoRef.current.play();
            } else {
                videoRef.current.currentTime = 0;
            }
        }
    }, [autoPlay]);

    useEffect(() => {
        if (isVisible && autoPlay) {
            if (videoRef.current?.paused) {
                window.setTimeout(() => {
                    videoRef.current?.play();
                }, delay);
            }
        } else {
            if (!videoRef.current?.paused) {
                videoRef.current?.pause();
            }
        }
    }, [isVisible, autoPlay, delay]);

    return (
        <div ref={ref} style={{ position: 'relative' }}>
            {/* We display a poster with a separate img element instead of using the
            video poster property, as the video poster tends to flicker in safari.  We pin the image
            behind the video, and only display it until the video has started playback */}
            {poster && displayPoster && (
                <img
                    src={poster}
                    style={{
                        position: 'absolute',
                        inset: '0',
                        width: '100%',
                        height: 'auto',
                    }}
                />
            )}
            <video
                ref={videoRef}
                preload="none"
                playsInline
                muted
                {...props}
                onPlaying={() => setDisplayPoster(false)}
                style={{ position: 'relative', width: '100%', height: 'auto' }}
            />
        </div>
    );
}
