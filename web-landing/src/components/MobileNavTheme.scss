@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'colors' as *;

@use './Header.scss' as *;

.mobile_nav.landing_mobile_nav {
    &.mobile_nav__light {
        --mobile-nav-background: #{$nav-background};
        --mobile-nav-link-active: #{themed($link)};
        --mobile-nav-link-active-bg: #{themed($nav-button-hover-bg)};
        --mobile-nav-link: #{themed($text)};
        --mobile-nav-border: #{themed($border)};
        --mobile-nav-popover-header: #{themed($link-quarternary)};
        --mobile-nav-link-hover-background-color: #{$light-purple-50};
        --mobile-nav-primary-button-color: #{$indigo-100};
        --mobile-nav-border-bottom: #{themed($header-border)};
    }
    &.mobile_nav__dark {
        --mobile-nav-background: #{$dark-purple-100};
        --mobile-nav-link-active-bg: #{$white-08};
        --mobile-nav-border: #{$white-16};
        --mobile-nav-link: #{$white-80};
        --mobile-nav-link-active: #{$pale-violet-alternate};
        --mobile-nav-popover-header: #{$white-16};
        --mobile-nav-link-hover-background-color: #{$white-16};
        --mobile-nav-primary-button-color: #{$pale-violet-alternate};
        --mobile-nav-border-bottom: #{$dark-purple-100};
    }
}
