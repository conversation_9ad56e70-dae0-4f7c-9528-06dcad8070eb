import classNames from 'classnames';
import { useMemo } from 'react';

import './NavigationDots.scss';

interface Props {
    count: number;
    index: number;
    setIndex: (index: number) => void;
}

export const NavigationDots = ({ count, index, setIndex }: Props) => {
    const items = useMemo(() => Array.from({ length: count }, (_, i) => i), [count]);

    return (
        <div className="navigation_dots">
            {items.map((idx) => {
                const classnames = classNames({
                    navigation_dot: true,
                    [`navigation_dot--selected`]: idx === index,
                });

                return <div key={idx} className={classnames} onClick={() => setIndex(idx)} />;
            })}
        </div>
    );
};
