'use client';
import { DependencyList, useEffect } from 'react';

const BASE_TITLE = 'Unblocked · The best way to talk to your codebase';

export const useLandingDocumentTitle = (title: () => string | boolean, dependencies: DependencyList) => {
    useEffect(() => {
        const titleValue = title();
        if (!!titleValue) {
            if (titleValue === true) {
                document.title = BASE_TITLE;
            } else {
                document.title = `${title()} · ${BASE_TITLE}`;
            }
        }

        return () => {
            document.title = BASE_TITLE;
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [title, ...dependencies]);
};
