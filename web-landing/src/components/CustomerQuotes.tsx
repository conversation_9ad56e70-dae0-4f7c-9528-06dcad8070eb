import { LinkedInQuote, Quote } from '@landing/components/QuoteSection/QuoteTypes';

import avatarAdrianBilauca from '@clientAssets/landing/avatar-adrian-bilauca-totalsoft.jpeg';
import avatarAkashSagar from '@clientAssets/landing/avatar-akash-sagar-user-testing.jpeg';
import avatarAlexMallet from '@clientAssets/landing/avatar-alex-mallet-forto.jpg';
import avatarBrentVatne from '@clientAssets/landing/avatar-brent-vatne-expo.jpeg';
import avatarDavidKnell from '@clientAssets/landing/avatar-david-knell-drata.jpeg';
import avatarJoshuaAustill from '@clientAssets/landing/avatar-joshua-austill.jpg';
import avatarKevinDeggelman from '@clientAssets/landing/avatar-kevin-deggelman-sfgiants.jpeg';
import avatarMatthewSpolin from '@clientAssets/landing/avatar-mathew-spolin-appdirect.jpeg';
import avatarMichaelHall from '@clientAssets/landing/avatar-michael-hall-cribl.jpeg';
import avatarNirajJayant from '@clientAssets/landing/avatar-niraj-jayant-human -interest.jpeg';
import avatarSteveToutonghi from '@clientAssets/landing/avatar-steve-toutonghi-measurabl.jpeg';
import avatarTrevinChow2 from '@clientAssets/landing/avatar-trevin-chow.jpg';
import avatarTrevinChow from '@clientAssets/landing/avatar-trevin-chow-bigcartel.jpeg';
import bigCartel from '@clientAssets/landing/logo-big-cartel-2-mono.svg';
import logoDrata from '@clientAssets/landing/logo-drata.svg';
import navaColor from '@clientAssets/landing/logo-nava-color.svg';
import nava from '@clientAssets/landing/logo-nava-mono.svg';
import unblockedPreview from '@clientAssets/public/unblocked-thumbnail.jpg';

export const customerQuotes: Quote[] = [
    {
        userIcon: avatarAlexMallet.src,
        imgTitle: 'Alex Mallet - Forto',
        main: (
            <>
                <p>
                    &ldquo;Every developer now has the ability to tap into past discussions and decisions to fill their
                    knowledge gaps, regardless of their tenure. We are{' '}
                    <b>moving faster and making more accurate decisions as a team</b> as a result.&rdquo;
                </p>
            </>
        ),
        name: 'Alex Mallet',
        role: 'EVP Engineering at Forto',
    },
    {
        userIcon: avatarNirajJayant.src,
        imgTitle: 'Niraj Jayant - Human Interest',
        main: (
            <>
                <p>
                    &ldquo;Unblocked has been instrumental in allowing developers to quickly understand our codebase and
                    has allowed our cross-team initiatives to deliver with high quality on time.
                </p>

                <p>
                    The team is not only getting more done, but they&apos;re{' '}
                    <b>
                        spending less time redoing work because they had the right information to make decisions the
                        first time around.
                    </b>
                    &rdquo;
                </p>
            </>
        ),
        name: 'Niraj Jayant',
        role: 'Director of Product & Eng at Human Interest',
    },
    {
        userIcon: avatarSteveToutonghi.src,
        imgTitle: 'Steve Toutonghi - Measurabl',
        main: (
            <>
                <p>
                    &ldquo;It saves time by putting documentation at our engineers&apos; fingertips, and has been{' '}
                    <b>particularly effective at speeding up engineers who are working in unfamiliar files.</b>
                    &rdquo;
                </p>
            </>
        ),
        name: 'Steve Toutonghi',
        role: 'Director of Engineering at Measurabl',
    },
    {
        userIcon: avatarDavidKnell.src,
        imgTitle: 'David Knell - Drata',
        main: (
            <>
                <p>
                    &ldquo;Unblocked has really impressed the team at Drata. It&apos;s like adding an&nbsp;
                    <b>extra team member who&apos;s always there to help, without the extra overhead.</b>
                    &rdquo;
                </p>
            </>
        ),
        name: 'David Knell',
        role: 'Distinguished Platform Architect at Drata',
    },
    {
        userIcon: avatarMichaelHall.src,
        imgTitle: 'Michael Hall - Cribl',
        main: (
            <>
                <p>
                    &ldquo;With Unblocked&nbsp;
                    <b>I&apos;m free to ask lots of questions I would not be willing to interrupt someone else with.</b>
                    &rdquo;
                </p>
            </>
        ),
        name: 'Michael Hall',
        role: 'Sr Staff Software Engineer at Cribl',
    },
    {
        userIcon: avatarAdrianBilauca.src,
        imgTitle: 'Adrian Bilauca - TotalSoft',
        main: (
            <>
                <p>
                    &ldquo;<b>It only took 5 minutes</b> for anyone on my immediate team to start using it
                    effectively.&rdquo;
                </p>
            </>
        ),
        name: 'Adrian Bilauca',
        role: 'Director of R&D at TotalSoft',
    },
    {
        userIcon: avatarMatthewSpolin.src,
        imgTitle: 'Matthew Spolin - AppDirect',
        main: (
            <>
                <p>
                    &ldquo;
                    <b>Unblocked empowers every developer with the insight once reserved for the seasoned few.</b>
                </p>
                <p>
                    This is the best application of AI&apos;s potential in enhancing developer proficiency, making
                    developers themselves more effective — rather than typing for them.&rdquo;
                </p>
            </>
        ),
        name: 'Mathew Spolin',
        role: 'SVP of Engineering at AppDirect',
    },
    {
        userIcon: avatarTrevinChow.src,
        imgTitle: 'Trevin Chow - Big Cartel',
        main: (
            <>
                <p>
                    &ldquo;Unblocked is a game changer in how easy it is to use and the quality of the results shows how
                    much context it carries automatically.
                </p>

                <p>
                    Using it for the past few weeks is literally&nbsp;
                    <b>10x&apos;ing my productivity.</b>&rdquo;
                </p>
            </>
        ),
        name: 'Trevin Chow',
        role: 'Chief Product Officer at Big Cartel',
    },
    {
        userIcon: avatarAkashSagar.src,
        imgTitle: 'Akash Sagar - UserTesting',
        main: (
            <p>
                &ldquo;Unblocked makes our developers more <b>effective and independent</b>. The sharing and
                collaborative features help us build a knowledge base and also initiate interesting discussions.&rdquo;
            </p>
        ),
        name: 'Akash Sagar',
        role: 'VP of Engineering at UserTesting',
    },
    {
        userIcon: avatarBrentVatne.src,
        imgTitle: 'Brent Vatne - Expo',
        main: (
            <>
                <p>
                    &ldquo;Unblocked is the first app that can actually bring together knowledge from the tools that I
                    use and make it available to me through natural conversation.
                </p>
                <p>
                    <b>I can&apos;t imagine a future without a tool like this.</b>&rdquo;
                </p>
            </>
        ),
        name: 'Brent Vatne',
        role: 'Engineering Manager at Expo',
    },

    {
        userIcon: avatarKevinDeggelman.src,
        imgTitle: 'Kevin Deggelman - San Francisco Giants',
        main: (
            <>
                <p>
                    &ldquo;Unblocked seamlessly integrates with our team&apos;s tools and workflows to provide{' '}
                    <b>accurate insights and answers</b> to questions about our codebase.&rdquo;
                </p>
            </>
        ),
        name: 'Kevin Deggelman',
        role: 'Staff Software Engineer at San Francisco Giants',
    },
];

export const daveKnellQuote = {
    userIcon: avatarDavidKnell.src,
    imgTitle: 'David Knell - Drata',
    main: (
        <>
            <p>
                &ldquo;Unblocked has cut down distractions and <b>increased our team&apos;s focus and velocity.</b>
                &nbsp;My team loves that they can get answers and solutions immediately.&rdquo;
            </p>
        </>
    ),
    name: 'David Knell',
    role: 'Distinguished Platform Architect at Drata',
};

export const daveKnellQuote2 = {
    userIcon: avatarDavidKnell.src,
    imgTitle: 'David Knell - Drata',
    main: (
        <>
            <p>
                &ldquo;I&rsquo;ve seen plenty of tools come and go, but Unblocked has really impressed the team at Drata
                &mdash; we love it.{' '}
                <b>
                    It&rsquo;s like adding an extra team member who&rsquo;s always there to help, without the extra
                    overhead.
                </b>{' '}
                Unblocked has cut down distractions and increased our team&rsquo;s focus and efficiency.&rdquo;
            </p>
        </>
    ),
    name: 'David Knell',
    role: 'Distinguished Platform Architect at Drata',
};

export const daveKnellQuote3: Quote = {
    userIcon: avatarDavidKnell.src,
    imgTitle: 'David Knell - Drata',
    companyName: 'Drata',
    customerLogo: logoDrata.src,
    main: (
        <>
            <p>
                &ldquo;Unblocked has really impressed the team at Drata. It&rsquo;s like adding an{' '}
                <b>extra team member who&rsquo;s always there to help, without the extra overhead.</b>
                &rdquo;
            </p>
        </>
    ),
    name: 'David Knell',
    role: 'Distinguished Platform Architect at Drata',
};

export const matthewSpolinQuote: Quote = {
    userIcon: avatarMatthewSpolin.src,
    imgTitle: 'Matthew Spolin - AppDirect',
    main: (
        <>
            <p>&ldquo;Unblocked empowers every developer with the insight once reserved for the seasoned few.</p>
            <p>
                This is the best application of AI&rsquo;s potential in enhancing developer proficiency,{' '}
                <b>making developers themselves more effective &mdash; rather than typing for them.</b>&rdquo;
            </p>
        </>
    ),
    name: 'Matthew Spolin',
    role: 'SVP of Engineering at AppDirect',
};

export const alexMalletQuote: Quote = {
    userIcon: avatarAlexMallet.src,
    imgTitle: 'Alex Mallet - Forto',
    main: (
        <>
            <p>
                &ldquo;Unblocked is far superior to search, going well beyond string matching to actually answer
                questions within the context of our entire codebase &mdash; something other code-focused AI tools cannot
                do.
            </p>
            <p>
                <b>
                    It saves time by putting documentation at our engineers&rsquo; fingertips, and has been particularly
                    effective at speeding up engineers who are working in unfamiliar files.
                </b>
                &rdquo;
            </p>
        </>
    ),
    name: 'Alex Mallet',
    role: 'EVP Engineering at Forto',
};

export const steveToutonghiQuote: Quote = {
    userIcon: avatarSteveToutonghi.src,
    imgTitle: 'Steve Toutonghi - Measurabl',
    main: (
        <>
            <p>
                &ldquo;Unblocked has made years of scattered documentation and historical context about our codebase
                useful and relevant without any work on our part.
            </p>
            <p>
                <b>We are moving faster and making more accurate decisions as a team as a result.</b>
                &rdquo;
            </p>
        </>
    ),
    name: 'Steve Toutonghi',
    role: 'Director of Engineering at Measurabl',
};

export const steveToutonghiQuote2: Quote = {
    userIcon: avatarSteveToutonghi.src,
    imgTitle: 'Steve Toutonghi - Measurabl',
    main: (
        <>
            <p>
                &ldquo;We are also seeing a benefit beyond just engineering. Other departments can use Unblocked to find
                answers that would have otherwise required engineering input,{' '}
                <b>saving us support bandwidth and reducing their wait time.</b>&rdquo;
            </p>
        </>
    ),
    name: 'Steve Toutonghi',
    role: 'Director of Engineering at Measurabl',
};

export const nirajJayantQuote: Quote = {
    userIcon: avatarNirajJayant.src,
    imgTitle: 'Niraj Jayant - Human Interest',
    main: (
        <>
            <p>
                &ldquo;Developers at Human Interest are now empowered to quickly get the context they need without
                making costly interruptions to others.
            </p>

            <p>
                The team is not only getting more done, but they&rsquo;re{' '}
                <b>
                    spending less time redoing work because they had the right information to make decisions the first
                    time around.
                </b>
                &rdquo;
            </p>
        </>
    ),
    name: 'Niraj Jayant',
    role: 'Director of Product & Engineering at Human Interest',
};

export const BrentVatneQuote: Quote = {
    userIcon: avatarBrentVatne.src,
    imgTitle: 'Brent Vatne - Expo',
    main: (
        <>
            <p>
                &ldquo;Unblocked is the first app that can actually bring together knowledge from the tools that I use
                and make it available to me through natural conversation.
            </p>
            <p>
                <b>I can&apos;t imagine a future without a tool like this.</b>&rdquo;
            </p>
        </>
    ),
    name: 'Brent Vatne',
    role: 'Engineering Manager at Expo',
};

export const McpTestimonialHighlight: Quote = {
    main: (
        <>
            <p>
                With Claude Code + Unblocked, I&apos;ve finally found&nbsp;
                <b>the holy grail of engineering productivity: context-aware coding.</b>
            </p>
            <p>
                It&apos;s not hallucinating &mdash; it&apos;s pulling insight from everything I&apos;ve ever worked on.
            </p>
        </>
    ),
    role: 'Staff Engineer at Nava Benefits',
    userIcon: navaColor.src,
    imgTitle: 'Nava Benefits',
};

export const McpTestimonial1: Quote = {
    main: (
        <>
            <p>
                With Unblocked MCP, Claude can now have real-time access to all our product decisions in Notion, every
                linear ticket, the full codebase, drafts in Google Drive, and Slack conversations.
            </p>
            <p>
                When I ask it to write code, troubleshoot, or scope a project, it pulls insights from everything
                I&rsquo;ve ever worked on. It&rsquo;s a context-native teammate. This workflow fundamentally changed how
                I work.
            </p>
        </>
    ),
    role: 'Staff Engineer at Nava Benefits',
    customerLogo: nava.src,
    imgTitle: 'Nava Benefits',
};

export const McpTestimonial2: Quote = {
    main: (
        <>
            <p>
                Unblocked MCP Server has ridiculously turbocharged my Claude code workflow. I can&rsquo;t underscore how
                valuable it is to have semantic reasoning over GitHub, Notion, Linear, Slack etc. Combining with other
                MCP servers like Ref and Context 7 is ridiculously powerful.
            </p>
        </>
    ),
    role: 'CPO at Big Cartel',
    customerLogo: bigCartel.src,
    imgTitle: 'Big Cartel',
};

export const McpTestimonial3: Quote = {
    main: (
        <>
            <p>
                With Claude Code + Unblocked, I&apos;ve finally found the holy grail of engineering productivity:
                context-aware coding.
            </p>
            <p>
                It&apos;s not hallucinating &mdash; it&apos;s pulling insight from everything I&apos;ve ever worked on.
            </p>
        </>
    ),
    role: 'Staff Engineer at Nava Benefits',
    customerLogo: nava.src,
    imgTitle: 'Nava Benefits',
};

export const mcpCustomerQuotes = [McpTestimonial1, McpTestimonial2, McpTestimonial3];

export const trevinChowMcpQuote: LinkedInQuote = {
    main: (
        <>
            <p>
                Last few days I&rsquo;ve had the opportunity to test the upcoming{' '}
                <span className="quote__link">Unblocked</span> MCP Server and it&rsquo;s ridiculously turbocharged my
                Claude code workflow.
            </p>

            <p>
                I can&rsquo;t underscore how valuable it is to improve development workflow and outcomes having their
                semantic reasoning over over GitHub + Notion + Linear + Slack. Combining it with other MCP servers like
                Ref and Context7 is ridiculously powerful.
            </p>
        </>
    ),
    name: 'Trevin Chow',
    role: 'Chief Product Officer @ Big Cartel',
    link: 'https://www.linkedin.com/posts/trevin_unblocked-activity-7338781042821996545-Rw1O',
    userIcon: avatarTrevinChow2.src,
    attachment: {
        image: unblockedPreview.src,
        header: 'Unblocked',
        subheader: 'getunblocked.com',
    },
};

export const joshuaAustillMcpQuote: LinkedInQuote = {
    main: (
        <>
            <p>This workflow fundamentally changed how I work.</p>

            <p>
                Imagine an AI that doesn&rsquo;t just guess what you mean&mdash;it knows.
                <br />
                With Claude Code + <span className="quote__link">Unblocked</span>&rsquo;s MCP, I&rsquo;ve finally found
                the holy grail of engineering productivity: context-aware coding.
            </p>

            <p>
                Here&rsquo;s what I mean 👇
                <br />
                Claude now has real-time access to:
                <br />
                All our product specs and decisions in Notion
                <br />
                Every Linear ticket, including comments and timelines
                <br />
                The full codebase across all our GitHub repos
                <br />
                My own docs and drafts in Google Drive
                <br />
                Slack conversations&mdash;across DMs, channels, and threads
            </p>
        </>
    ),
    name: 'Joshua Austill',
    role: 'Staff Engineer @ Nava Benefits',
    link: 'https://www.linkedin.com/posts/joshua-austill-7b79168_this-workflow-fundamentally-changed-how-i-activity-7348454500828971008-jY3v',
    userIcon: avatarJoshuaAustill.src,
    showMore: true,
};

export const joshuaAustillMcpQuote_Sm: LinkedInQuote = {
    main: (
        <>
            <p>This workflow fundamentally changed how I work.</p>

            <p>
                Imagine an AI that doesn&rsquo;t just guess what you mean&mdash;it knows.
                <br />
                With Claude Code + <span className="quote__link">Unblocked</span>&rsquo;s MCP, I&rsquo;ve finally found
                the holy grail of engineering productivity: context-aware coding.
            </p>

            <p>
                Here&rsquo;s what I mean 👇
                <br />
                Claude now has real-time access to:
                <br />
                All our product specs and decisions in Notion
                <br />
                Every Linear ticket, including comments and timelines
                <br />
                The full codebase across all our GitHub repos
                <span className="quote__more">...more</span>
            </p>
        </>
    ),
    name: 'Joshua Austill',
    role: 'Staff Engineer @ Nava Benefits',
    link: 'https://www.linkedin.com/posts/joshua-austill-7b79168_this-workflow-fundamentally-changed-how-i-activity-7348454500828971008-jY3v',
    userIcon: avatarJoshuaAustill.src,
};
