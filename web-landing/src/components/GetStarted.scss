@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'colors' as *;

@use '../app/landing-colors.scss' as *;

.get_started {
    background: #8d23ce; /* fallback for old browsers */
    background: radial-gradient(ellipse at bottom, #c862f9 0%, #8d23ce 100%);
    background-position: center bottom;
    padding: 60px 0 0 !important;
    overflow: hidden;

    button {
        margin: 16px auto 36px;
    }

    h2,
    p {
        color: $white-100 !important;
    }

    img {
        width: 2068px;
    }

    .section__content {
        @include flex-column-center;

        text-align: center;
    }
}

.get_answers,
.get_started_steps {
    padding: $spacer-60 $spacer-24;

    .section__content {
        @include flex-column-center;

        text-align: center;

        h2 {
            max-width: 80%;
        }
    }

    .get_answers__buttons {
        @include flex-column;
        @include fill-available;
        flex-direction: column-reverse;
        gap: $spacer-16;
        max-width: 80%;
        margin-top: $spacer-12;

        .button,
        a {
            flex: none;
            align-self: center;
            min-width: 170px;
            border-color: transparent;
            font-size: $size-16;

            border: none;
        }

        @include breakpoint(sm) {
            flex-direction: row;
            width: auto;
            margin-top: $spacer-24;
            .button,
            a {
                height: $size-56;
                min-width: 200px;
                font-size: $size-19;
            }
        }
    }
}

.get_answers {
    background: #280b47;
    color: $white-100;
    padding: $spacer-60 $spacer-24;

    .get_answers__header {
        font-size: 28px;
        line-height: 32px;
        font-weight: $font-weight-bold;
        margin-bottom: $spacer-16;

        @include breakpoint(md) {
            font-size: 40px;
            line-height: 48px;
        }
    }

    .get_answers__subheader {
        color: $white-60;
        font-size: 22px;
        line-height: 28px;
        font-weight: $font-weight-light;
        margin-bottom: $spacer-28;

        @include breakpoint(md) {
            font-size: 26px;
            line-height: 32px;
        }
    }

    .get_answers__buttons {
        .button {
            background: $white-70;
            color: $dark-purple-100;
            font-weight: $font-weight-semibold;

            &:hover {
                background: $white-80;
                color: $dark-purple-100;
            }
        }
    }

    .get_answers__clients {
        @include flex-center($spacer-12);

        justify-content: center;
        flex-wrap: wrap;
        margin-top: $spacer-40;
        gap: $spacer-24;
        padding: 0;

        @include breakpoint(sm) {
            padding: 0 $spacer-48;
        }

        @include breakpoint(md) {
            padding: 0 $spacer-60;
        }

        @include breakpoint(lg) {
            gap: $spacer-48;
        }
    }
}

.client_block {
    padding: $spacer-12;
    cursor: pointer;

    a {
        text-decoration: none;
    }

    @include breakpoint(md) {
        padding: $spacer-24;
    }

    .client_block__logo {
        height: 100px;
        width: 100px;

        @include breakpoint(md) {
            height: 120px;
            width: 120px;
        }
    }

    .client_block__label {
        color: $white-70;
        margin-top: $spacer-16;
        transition: color 0.3s ease-in-out;
    }
    &:hover {
        .client_block__label {
            color: $white-100;
        }
    }
}

.get_started_steps {
    position: relative;
    overflow: hidden;
    background: $landing-magenta-10;
    &::before {
        content: '';
        position: absolute;
        background-color: $landing-indigo-32;
        border-radius: 50%;
        filter: blur(80px);
        height: 140%;
        width: 80%;
        top: 50px;
        left: -20%;
        bottom: -400px;

        @include breakpoint(md) {
            top: unset;
            left: -100px;
            bottom: -400px;
            height: 900px;
            width: 1000px;
        }
    }

    &::after {
        content: '';
        position: absolute;
        background-color: $landing-magenta-15;
        border-radius: 50%;
        filter: blur(80px);
        height: 140%;
        width: 80%;
        top: 0;
        right: -30%;

        @include breakpoint(md) {
            top: -100px;
            right: -300px;
            bottom: unset;
            height: 500px;
            width: 1000px;
        }
    }

    &.home_section {
        @include breakpoint(md) {
            padding: $spacer-80 $spacer-0;
        }

        .section__content {
            z-index: 1;
        }
    }

    .get_answers_steps__blocks {
        display: grid;
        grid-template-columns: 1fr;
        grid-template-rows: max-content 20px 1fr 20px max-content;
        margin-top: $spacer-60;
        gap: $spacer-24;
        padding: 0;

        @include breakpoint(sm) {
            padding: 0 $spacer-48;
        }

        @include breakpoint(md) {
            grid-template-columns: 1fr 1px 1fr 1px 1fr;
            grid-template-rows: 1fr;
            margin-top: $spacer-24;
            padding: 0 $spacer-12;
            gap: $spacer-8;
        }

        @include breakpoint(lg) {
            gap: $spacer-48;
        }

        .get_answers_steps__arrow {
            font-size: $size-32;
            color: $indigo-60a;
            align-self: center;
            letter-spacing: -1px;
            transform: rotate(90deg);

            @include breakpoint(md) {
                transform: rotate(0);
                margin-bottom: $spacer-100;

                &:first-of-type {
                    margin-left: $spacer-8;
                }

                &:last-of-type {
                    margin-left: -$spacer-44;
                }
            }

            @include breakpoint(lg) {
                &:first-of-type {
                    margin-left: $spacer-4;
                }

                &:last-of-type {
                    margin-left: -$spacer-24;
                }
            }
        }
    }
}

.get_started__step_block {
    display: grid;
    grid-template:
        'icon' 1fr
        'label' auto / 1fr;
    place-items: center center;

    @include breakpoint(md) {
        grid-template:
            'icon' minmax(max-content, 180px)
            'label' auto / 1fr;
    }

    .get_started__step_block_img {
        grid-area: icon;
        height: auto;
        width: 180px;

        @include breakpoint(md) {
            width: 180px;
        }
    }

    .get_started__step_block_label {
        grid-area: label;
        color: themed($text);
        margin-top: $spacer-16;
        font-size: 21px;
        line-height: 24px;
        letter-spacing: -0.305px;

        @include breakpoint(md) {
            font-size: 24px;
            line-height: 32px;
            letter-spacing: -0.348px;
        }

        @include breakpoint(lg) {
            font-size: 28px;
            line-height: 34px;
            letter-spacing: -0.406px;
        }
    }

    &.get_answers_steps__process {
        margin-top: -$spacer-20;

        .get_started__step_block_img {
            height: auto;
            width: 300px;

            @include breakpoint(md) {
                width: 300px;
            }
        }

        .get_started__step_block_label {
            margin-top: -4.5px;
        }
    }

    &.get_answers_steps__checkmark {
        .get_started__step_block_img {
            height: auto;
            width: 150px;
            margin-top: $spacer-20;

            @include breakpoint(md) {
                margin-top: 0;
                width: 130px;
            }
        }
    }
}
