@use 'layout' as *;
@use 'colors' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'misc' as *;
@use 'theme' as *;
@use 'layout-mixin' as *;

.blog_landing_preview {
    display: grid;
    grid-template-columns: 1fr;
    row-gap: 24px;
    width: 100%;

    @include breakpoint(md) {
        grid-template-columns: 1fr 36%;
        column-gap: 24px;
    }

    @include breakpoint(lg) {
        column-gap: 32px;
    }

    .blog_landing_preview__info {
        text-align: left;

        @include breakpoint(md) {
            padding: 0 0 4%;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
    }

    .blog_article__title {
        font-size: 24px;
        line-height: 32px;
        font-weight: 500;
        letter-spacing: -0.0245em;
        margin-bottom: 8px;
        @include line-clamp(2);

        @include breakpoint(lg) {
            font-size: 28px;
            line-height: 34px;
            letter-spacing: -0.07rem;
            margin-bottom: 16px;
            @include line-clamp(3);
        }
    }
    a {
        &:not(:hover) {
            color: themed($text);
        }
        text-decoration: none;

        &:hover {
            color: themed($indigo-100);
        }
    }

    .blog_article__description {
        font-size: 19px;
        line-height: 28px;
        font-weight: 300;
        letter-spacing: -0.0245em;
        color: themed($text);
        word-break: break-word;
        margin-bottom: 8px;
        @include line-clamp(3);

        @include breakpoint(lg) {
            margin-bottom: 12px;
        }
    }

    .blog_article__readmore {
        font-size: 19px;
        line-height: 28px;
        font-weight: 300;
        letter-spacing: -0.0245em;
        color: themed($indigo-100);

        svg {
            margin-left: 4px;
        }
    }

    .blog_article__picture {
        width: 100%;
        border-radius: 8px;
        filter: drop-shadow(0 16px 20px rgb(213 210 244 / 20%));
    }
}
