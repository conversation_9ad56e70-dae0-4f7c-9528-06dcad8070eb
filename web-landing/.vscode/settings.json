{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "search.exclude": {"**/.storybook-dist": true, "**/coverage": true, "**/dist": true, "**/.next": true, "**/package-lock.json": true}, "stylelint.validate": ["css", "less", "postcss", "scss"], "jest.runMode": "on-demand", "jest.jestCommandLine": "../node_modules/.bin/jest"}