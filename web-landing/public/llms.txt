# Unblocked

Unblocked is a developer platform that turns scattered knowledge from tools like GitHub, Slack, Confluence, and Jira into helpful context for developers and AI coding assistants.

## Key Benefits

- Reduce time wasted searching across Slack, Confluence, Jira, and GitHub by surfacing context instantly in developer workflow. 
- Improve accuracy of AI code generation tools like Claude Code and Cursor by giving them access to institutional knowledge.
- New hires ramp up on unfamiliar codebases and tools in less time, contributing value sooner.
- Scale internal support and empower cross-functional teams to self-serve technical answers without adding engineering overhead.
- Keep the knowledge base automatically up to date with the latest pull requests, issues, and discussions, without manual upkeep
- Preserve institutional memory during team turnover and growth, ensuring critical context is never lost.


## Features

- Instant, cited Q&A based on code, documentation, issue trackers, and discussions.
- Cross-platform support across web, macOS, and major IDE’s.
- Slack integration that automatically answers support questions in channels.
- Answer customization with preferences for style, depth, length. 
- Data Source Presets to control which knowledge sources are searched.
- MCP Server for AI-powered IDE’s like Cursor, Claude Code, VSCode, Windsurf, and GitHub Copilot.
- PR Failure Agent that posts automated CI failure triage report in the Pull Request (Supporting GitHub Actions, Bitbucket Pipelines, BuildKite, CircleCI, GitLab Pipelines)
- Analytics dashboard to track usage and adoption.
- Public API for connecting custom documents to Unblocked, and for building custom workflows with Unblocked Q&A capabilities.
- Permission-aware answers based on existing user access settings in data sources.


## Supported Integrations

Unblocked supports GitHub, Slack, Confluence, Jira, and more. The complete list includes:

- GitHub
- GitHub Enterprise
- Bitbucket
- Bitbucket Data Center
- GitLab
- GitLab Self Managed
- Azure DevOps
- BuildKite
- CircleCI
- GitHub Actions
- Jenkins
- Coda Enterprise
- Confluence
- Confluence Data Center
- Notion
- Google Drive
- Stack Overflow for Teams
- External websites
- Custom documents via Unblocked API
- Asana
- Jira
- Jira Data Center
- Slack


## Uses Cases

- [Onboarding](https://getunblocked.com/use-cases/onboarding/): Information on how Unblocked helps new team members find the answers they need so they can start contributing right away.
- [Legacy code](https://getunblocked.com/use-cases/legacy-code/): Information on how Unblocked assists in managing and understanding legacy codebases.
- [Internal support](https://getunblocked.com/use-cases/internal-support/): Information on how Unblocked empowers cross-functional teams to answer their own questions.


## Documentation

- [Documentation](https://docs.getunblocked.com/): Comprehensive documentation for using Unblocked effectively.


## Blog

- [Blog](https://getunblocked.com/blog/): Content on product updates, customer stories, and industry insights from the Unblocked team.


## Customer Stories

- [Fetch: “Unblocked transformed our internal support model.”](https://getunblocked.com/blog/customer-story-fetch/): Fetch cut onboarding time and scaled internal support with Unblocked—helping 350+ engineers move faster, stay autonomous, and find answers instantly.
- [Fingerprint: “Unblocked is our single source of truth for code knowledge.”](https://getunblocked.com/blog/customer-story-fingerprint/): Fingerprint scaled internal support with Unblocked, turning scattered knowledge into instant answers and saving 60–70 hours a week across engineering.
- [How TravelPerk Supercharged Engineering Onboarding](https://getunblocked.com/blog/customer-story-travelperk/): For TravelPerk, Unblocked has been a game changer for engineering onboarding. New hires ramp up faster without interrupting others, allowing the team to stay focused and productive.
- [Big Cartel: “Unblocked is like having a smart colleague available 24/7.”](https://getunblocked.com/blog/customer-story-big-cartel/): Big Cartel’s lean, remote team works across functional boundaries to ship quickly. But before Unblocked, making sure everyone understood how the legacy codebase worked was a challenge.
- [AppDirect: “Unblocked has been a game-changer for efficiency and focus.”](https://getunblocked.com/blog/customer-story-app-direct/): For the team at AppDirect, Unblocked checks all the boxes and more, tying together knowledge dispersed across internal platforms and giving developers context right where they’re working.
- [Cribl: “Unblocked is our number one tool for finding the information we should know but don’t.”](https://getunblocked.com/blog/customer-story-cribl/): Cribl’s remote-first culture empowers its employees with flexibility, but keeping over 100 engineers on the same page across time zones poses a real challenge. That’s why the team turned to Unblocked.
- [Drata: “Our team saves 1 to 2 hours per engineer each day with Unblocked”](https://getunblocked.com/blog/customer-story-drata/): Drata’s distributed engineering team relies on Unblocked to get the answers about their codebase. With Unblocked, issues get resolved faster, tasks are completed sooner, and projects go more smoothly.


## Product Updates

- [Introducing the Unblocked Answers API for Custom Workflows](https://getunblocked.com/blog/answers-api/): Today we are launching our new Answers API, which lets you integrate Unblocked’s Q&A features directly into your internal tools.
- [Introducing Unblocked MCP Server (Beta)](https://getunblocked.com/blog/mcp-beta/): Unblocked is now available as an MCP server, supercharging tools like Cursor, Windsurf and VSCode.
- [Introducing PR Failure Agent](https://getunblocked.com/blog/pr-failure-agent/): We're releasing our PR Failure Agent (previously known as Autonomous CI Triage) to general availability.
- [New in Unblocked: PR Failure Agent, Unblocked MCP Server (beta) & Asana Integration](https://getunblocked.com/blog/unblocked-updates-2025-jun-25/):
- [Get Answers From Your Asana Projects](https://getunblocked.com/blog/asana/): Unblocked now integrates with Asana. Ask questions and find answers from your Asana tasks, comments, and project updates.
- [Automate Internal Support With Customized Slack Answers](https://getunblocked.com/blog/customized-slack-answers/): Many customers want to use Unblocked to help automate their internal support in Slack. You can now have per-channel settings over how answers are generated.
- [New in Unblocked: Data Source Presets, Automated CI Triage, Public API & More](https://getunblocked.com/blog/unblocked-updates-2025-apr-2/): You can now select specific data sources to answer questions, get automatic CI triage directly in your pull request, add custom documents via Unblocked API, and more.
- [Introducing Automated CI Triage: Your New Build Detective (Beta)](https://getunblocked.com/blog/ci/): We're excited to announce the upcoming beta release of Unblocked's Automated CI Triage feature, designed to save developers time by automatically detecting and suggesting fixes for CI failures.
- [New in Unblocked: Visualizing Your Code with Diagrams + Dark Mode](https://getunblocked.com/blog/diagrams-and-dark-mode/): Unblocked now supports Mermaid diagrams for visualizing code and system architectures, plus dark mode for a personalized dashboard experience.
- [Permission-aware Answers: Keep Sensitive Information Secure With Data Shield](https://getunblocked.com/blog/data-shield/): Unblocked's Data Shield gives you granular access controls to protect your team’s sensitive data while getting answers from Unblocked.
- [Introducing Analytics: track Unblocked usage, question ratings, top users, and more](https://getunblocked.com/blog/analytics-dashboard/): Our new Analytics Dashboard helps you track adoption, spot patterns, and evaluate how Unblocked works for your team—all in one place.
- [Customizing Unblocked's answers](https://getunblocked.com/blog/answer-preferences/): With Answer Preferences, you now get more control over how Unblocked answers your questions - from setting the length, research depth, and more.
- [Asking questions in private with Unblocked's Incognito Mode](https://getunblocked.com/blog/incognito-mode/): Incognito mode provides complete privacy for your code questions. Learn best practices to leverage this feature across all platforms.
- [Introducing new features that make Unblocked even more helpful in Slack](https://getunblocked.com/blog/slack-dms-and-private-channels/): We’re expanding the ways teams can get fast, accurate, and relevant answers to their questions in Slack with auto responses, support for private channels, bot DMs, and channel summarizations.
- [Connecting your code no matter where it lives](https://getunblocked.com/blog/unblocked-updates-2024-sep-20/): Unblocked now supports connecting to multiple source code systems at the same time. You and your team can get answers from any repository no matter where it lives (GitHub, Bitbucket, or GitLab).
- [Answers, right at your fingertips, with the new Unblocked macOS app](https://getunblocked.com/blog/unblocked-mac-app-2024-jul/): The new Unblocked mac app provides a centralized place to ask questions, get answers, and view your past queries.
- [Faster responses, better answers about projects and issues, role-based permissions, & more](https://getunblocked.com/blog/unblocked-updates-2024-may-22/): A number of new features are now available in Unblocked, helping you and your team get the answers you need about your codebase.
- [Unblocked IDE plugins now in the marketplace](https://getunblocked.com/blog/unblocked-updates-2024-apr-02/): Download the Unblocked plugin for Visual Studio Code and JetBrains IDE’s such as IntelliJ, WebStorm, PyCharm and more.
- [Support for Google Drive, better answers for PRs and more](https://getunblocked.com/blog/unblocked-updates-2024-feb-28/): Import Google Drive documents, improved Pull Request Questions and Answers, Team member overview, Support for Open Source Projects,Suggested Follow-ups Stick Around and more.
- [IDE improvements and support for all sized teams](https://getunblocked.com/blog/unblocked-updates-2024-feb/): We’re improving how you ask questions, discover relevant documents / threads / bugs, and making this available to everyone.


## Shop Talk

- [HTTPS Reverse Tunnel, now without the pain and cost](https://getunblocked.com/blog/chissl/): chiSSL is a new, lightweight version of chisel that allows you to expose any local server running on your development machine to the internet with a valid SSL certificate, all via a single command.
- [Saving time and money with self-hosted runners on EC2 on-demand / spot instances](https://getunblocked.com/blog/ec2-self-hosted-runners/): We created a custom GitHub Action that automatically deploys self-hosted ephemeral runners to EC2 using Spot or On-Demand instances.


## Perspectives

- [Developers Don’t Need More Docs](https://getunblocked.com/blog/documentation/): When developers start a new project or get stuck on a task, they don’t just need pages of structured documentation; they need answers.
- [The 12-Line Pull Request That Took 5 Days: A Context Problem](https://getunblocked.com/blog/developer-productivity-is-still-broken/): We have more AI developer tools than ever, yet many of us still feel unproductive. Why? Maybe it's because we're not using AI to tackle the biggest challenge in software development yet.
- [Your new developer feels like a fraud. It's your onboarding process.](https://getunblocked.com/blog/no-imposter-onboarding/): New engineers face a tough learning curve, navigating a new role and a complex codebase. It’s no wonder they feel like imposters. We need to reimagine onboarding to better support them.


## Company Updates

- [Bridging the Code Knowledge Gap: Unblocked's Series A and the Future of Contextual Code Intelligence](https://getunblocked.com/blog/series-a/): At Unblocked, we’re building a contextual code intelligence platform to give developers the why behind their code, not just the what. We’ve raised $20M to help scale that vision.
- [Unblocked + DX - a new partnership for impact](https://getunblocked.com/blog/unblocked-dx-partnership/): Unblocked is embarking on a new partnership with DX - the developer intelligence platform designed by leading researchers to give true insight into the productivity of software development teams.
- [A Year of Unblocking Engineering Teams](https://getunblocked.com/blog/beta-learnings/): Since our launch a year ago, we’ve added many new features, saved our customers hours every day, and reaffirmed our conviction that we’re solving an expensive problem for software development teams.
- [“Get Unblocked”](https://getunblocked.com/blog/get-unblocked/): Today we're publicly launching Unblocked, a new developer tool that increases your productivity by providing helpful and accurate answers to questions about your codebase.


## General Information

- [Security](https://getunblocked.com/security/): Details on the security features of Unblocked, ensuring that sensitive information is protected.
- [About Us](https://getunblocked.com/about/): Learn more about Unblocked's mission, vision, and the team behind the product.
- [Careers](https://getunblocked.com/careers/): Explore job opportunities at Unblocked and join a team dedicated to enhancing developer productivity.
- [Pricing](https://getunblocked.com/pricing/): Information on the pricing plans available for Unblocked.
- [Download Unblocked](https://getunblocked.com/download/): Download the Unblocked Mac App or install the Unblocked IDE extensions.
- [Get Started](https://getunblocked.com/dashboard/get-started): Start your 21-day free trial with Unblocked.
- [Log In](https://getunblocked.com/dashboard/login): Log in to your existing Unblocked account.
- [Book a Demo](https://getunblocked.com/demo/): Schedule a demo to see Unblocked in action.
