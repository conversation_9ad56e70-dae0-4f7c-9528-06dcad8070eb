{"extends": "../sharedConfigs/tsconfig.json", "compilerOptions": {"baseUrl": "./", "outDir": "dist", "lib": ["ES2022", "DOM", "DOM.Iterable", "WebWorker"], "paths": {"@web/*": ["../web/src/*"], "@shared/*": ["../shared/*"], "@clientAssets/*": ["../shared/clientAssets/*"], "@landing/*": ["./src/*"]}, "allowJs": true, "skipLibCheck": true, "noEmit": true, "jsx": "preserve", "plugins": [{"name": "next"}]}, "include": ["src", "src/types/*.d.ts", "src/**/*.json", "src/**/*.d.ts", "../shared/types/video.d.ts", "../shared/types/Config.d.ts", "next-env.d.ts", ".next/types/**/*.ts", "dist/types/**/*.ts", "../web/src/components/TeamBlock"], "exclude": ["node_modules", ".vscode-test"]}