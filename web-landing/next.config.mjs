import * as path from 'path';
import { fileURLToPath } from 'url';
import webpack from 'webpack';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default (phase, { defaultConfig }) => {
    /**
     * @type {import('next').NextConfig}
     */
    const config = {
        // Landing pages generate static exported pages by default
        output: 'export',
        distDir: phase === 'phase-development-server' ? defaultConfig.distDir : 'dist',

        // Export pages like `/path/to/my/page/index.html`, instead of `/path/to/my/page.html'
        trailingSlash: true,

        sassOptions: {
            includePaths: [path.join(__dirname, '../web/src/styles')],
        },

        webpack: (config) => {
            config.resolve.alias['@shared/webComponents/styles'] = path.resolve(
                __dirname,
                '../shared/webComponents/styles'
            );
            config.resolve.fallback = {
                fs: false,
            };
            config.module.rules.push({
                test: /redocly\/.*/,
                loader: 'ignore-loader',
            });
            config.module.rules.push({
                test: /\.(webm|mp4|mov|woff|woff2)$/i,
                type: 'asset/resource',
            });
            config.plugins.push(
                new webpack.ProvidePlugin({
                    setImmediate: ['timers-browserify', 'setImmediate'],
                })
            );

            config.plugins.push(
                new webpack.DefinePlugin({
                    APP_TYPE: JSON.stringify('landing'),
                })
            );

            if (phase === 'phase-development-server') {
                config.plugins.push(
                    new webpack.DefinePlugin({
                        ENVIRONMENT: JSON.stringify('dev'),
                    })
                );
            }

            return config;
        },
    };
    return config;
};
