name: Infra K8S Dependencies Deploy
on:
  push:
    branches:
      - main
    paths:
      - '.github/workflows/ci-infra-k8s-deps-deploy.yaml'
      - 'infrastructure/kubernetes/**'
  merge_group:
    branches: [main]
  workflow_dispatch:
  workflow_run:
    workflows: [ "CI Infra ML Images" ]
    branches: [main]
    types:
      - completed
concurrency:
  group: infra-k8s-deps-${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: false
jobs:
  deploy-aws-dev:
    if: github.ref == 'refs/heads/main'
    environment: development
    timeout-minutes: 25
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
      actions: read
    strategy:
      matrix:
        include:
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: temporal-umbrella
            helm-release-name: "temporal"
            helm-chart-version: "0.1.2"
            helm-values-file-path: infrastructure/kubernetes/aws-dev/us-west-2/dev-1/temporal/values.yaml
            helm-flags: "--create-namespace"
            kube-namespace: "temporal"
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: prefect-server-umbrella
            helm-release-name: "prefect-server-umbrella"
            helm-chart-version: "0.1.7"
            helm-values-file-path: infrastructure/kubernetes/aws-dev/us-west-2/dev-1/prefect-server/values.yaml
            helm-flags: "--create-namespace"
            kube-namespace: "prefect"
          - helm-repo-url: https://prefecthq.github.io/prefect-helm
            helm-repo-name: prefect
            helm-chart-name: prefect-worker
            helm-release-name: "prefect-worker"
            helm-chart-version: "2025.5.22215901"
            helm-values-file-path: infrastructure/kubernetes/aws-dev/us-west-2/dev-1/prefect-workers/values.yaml
            helm-flags: "--create-namespace"
            kube-namespace: "prefect"
      fail-fast: false
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Deploy ${{ matrix.helm-release-name }} to Dev cluster (dev-1)
        uses: ./.github/actions/ci-k8s-eks-helm-deploy
        with:
          helm-repo-url: ${{ matrix.helm-repo-url }}
          helm-repo-name: ${{ matrix.helm-repo-name }}
          helm-chart-name: ${{ matrix.helm-chart-name }}
          helm-release-name: ${{ matrix.helm-release-name }}
          helm-chart-version: ${{ matrix.helm-chart-version }}
          helm-values-file-path: ${{ matrix.helm-values-file-path }}
          helm-values-cli-args: "--set-string WebAuthSecrets.TEMPORAL_AUTH_CLIENT_ID=${{ secrets.TEMPORAL_AUTH_CLIENT_ID }} --set-string WebAuthSecrets.TEMPORAL_AUTH_CLIENT_SECRET=${{ secrets.TEMPORAL_AUTH_CLIENT_SECRET }} --set-string secret.basicAuth.authString=${{ secrets.PREFECT_SERVER_AUTH_STRING }} ${{ matrix.helm-flags }}"
          kube-namespace: ${{ matrix.kube-namespace }}
          aws-access-key-id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
          aws-region: "us-west-2"
          kubeconfig: ${{ secrets.DEPLOY_KUBECONFIG_US_WEST_2_DEV1 }}
          kube-api-host: ${{ vars.KUBE_API_URL_US_WEST_2_DEV1 }}
          bastion-host: ${{ vars.BASTION_HOST_NAME }}
          bastion-private-ssh-key: ${{ secrets.BASTION_DEPLOY_SSH_KEY }}

  deploy-aws-prod:
    if: github.ref == 'refs/heads/main'
    environment: production
    needs: deploy-aws-dev
    timeout-minutes: 25
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
      actions: read
    strategy:
      matrix:
        include:
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: temporal-umbrella
            helm-release-name: "temporal"
            helm-chart-version: "0.1.2"
            helm-values-file-path: infrastructure/kubernetes/aws-prod/us-west-2/prod-1/temporal/values.yaml
            helm-flags: "--create-namespace"
            kube-namespace: "temporal"
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: prefect-server-umbrella
            helm-release-name: "prefect-server-umbrella"
            helm-chart-version: "0.1.7"
            helm-values-file-path: infrastructure/kubernetes/aws-prod/us-west-2/prod-1/prefect-server/values.yaml
            helm-flags: "--create-namespace"
            kube-namespace: "prefect"
          - helm-repo-url: https://prefecthq.github.io/prefect-helm
            helm-repo-name: prefect
            helm-chart-name: prefect-worker
            helm-release-name: "prefect-worker"
            helm-chart-version: "2025.5.22215901"
            helm-values-file-path: infrastructure/kubernetes/aws-prod/us-west-2/prod-1/prefect-workers/values.yaml
            helm-flags: "--create-namespace"
            kube-namespace: "prefect"
      fail-fast: false
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Deploy ${{ matrix.helm-release-name }} to Prod cluster (prod-1)
        uses: ./.github/actions/ci-k8s-eks-helm-deploy
        with:
          helm-repo-url: ${{ matrix.helm-repo-url }}
          helm-repo-name: ${{ matrix.helm-repo-name }}
          helm-chart-name: ${{ matrix.helm-chart-name }}
          helm-release-name: ${{ matrix.helm-release-name }}
          helm-chart-version: ${{ matrix.helm-chart-version }}
          helm-values-file-path: ${{ matrix.helm-values-file-path }}
          helm-values-cli-args: "--set-string WebAuthSecrets.TEMPORAL_AUTH_CLIENT_ID=${{ secrets.TEMPORAL_AUTH_CLIENT_ID }} --set-string WebAuthSecrets.TEMPORAL_AUTH_CLIENT_SECRET=${{ secrets.TEMPORAL_AUTH_CLIENT_SECRET }} --set-string secret.basicAuth.authString=${{ secrets.PREFECT_SERVER_AUTH_STRING }} ${{ matrix.helm-flags }}"
          kube-namespace: ${{ matrix.kube-namespace }}
          aws-access-key-id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
          aws-region: "us-west-2"
          kubeconfig: ${{ secrets.DEPLOY_KUBECONFIG_US_WEST_2_PROD1 }}
          kube-api-host: ${{ vars.KUBE_API_URL_US_WEST_2_PROD1 }}
          bastion-host: ${{ vars.BASTION_HOST_NAME }}
          bastion-private-ssh-key: ${{ secrets.BASTION_DEPLOY_SSH_KEY }}

  deploy-aws-secops:
    if: github.ref == 'refs/heads/main'
    environment: secops
    timeout-minutes: 60
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
      actions: read
    strategy:
      matrix:
        include:
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: llm-tgi
            helm-release-name: "docmap"
            helm-chart-version: "1.0.13"
            helm-values-file-path: infrastructure/kubernetes/aws-secops/us-west-2/docmap/values.yaml
            helm-timeout: "20m"
            kube-namespace: "default"
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: llm-tgi
            helm-release-name: "e5-mistral"
            helm-chart-version: "1.0.13"
            helm-values-file-path: infrastructure/kubernetes/aws-secops/us-west-2/e5-mistral/values.yaml
            helm-timeout: "30m"
            kube-namespace: "default"
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: llm-tgi
            helm-release-name: "e5-mistral-spot"
            helm-chart-version: "1.0.13"
            helm-values-file-path: infrastructure/kubernetes/aws-secops/us-west-2/e5-mistral-spot/values.yaml
            helm-timeout: "30m"
            kube-namespace: "default"
            helm-flags: ""
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: llm-tgi
            helm-release-name: "llama318b"
            helm-chart-version: "1.0.13"
            helm-values-file-path: infrastructure/kubernetes/aws-secops/us-west-2/llama318b/values.yaml
            helm-timeout: "30m"
            kube-namespace: "default"
      fail-fast: false
      #max-parallel: 2
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Deploy ${{ matrix.helm-release-name }} to Secops cluster
        uses: ./.github/actions/ci-k8s-eks-helm-deploy
        with:
          helm-repo-url: ${{ matrix.helm-repo-url }}
          helm-repo-name: ${{ matrix.helm-repo-name }}
          helm-chart-name: ${{ matrix.helm-chart-name }}
          helm-release-name: ${{ matrix.helm-release-name }}
          helm-chart-version: ${{ matrix.helm-chart-version }}
          helm-timeout: ${{ matrix.helm-timeout }}
          helm-values-file-path: ${{ matrix.helm-values-file-path }}
          helm-values-cli-args: >-
            --set-string global.unblocked.secrets.HUGGING_FACE_TOKEN=${{ secrets.HUGGING_FACE_TOKEN }}
            --set-string global.unblocked.secrets.OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}
            --set-string image.tgi.tag=latest
            --set-string image.tgi.pullPolicy=Always
            ${{ matrix.helm-flags }}
          kube-namespace: ${{ matrix.kube-namespace }}
          aws-access-key-id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
          aws-region: "us-west-2"
          kubeconfig: ${{ secrets.DEPLOY_KUBECONFIG_US_WEST_2 }}
          kube-api-host: ${{ vars.KUBE_API_URL_US_WEST_2 }}
          bastion-host: ${{ vars.BASTION_HOST_NAME }}
          bastion-private-ssh-key: ${{ secrets.BASTION_DEPLOY_SSH_KEY }}

  deploy-gcp-secops:
    if: github.ref == 'refs/heads/main'
    environment: gcp-secops
    timeout-minutes: 30
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
      actions: read
    strategy:
      matrix:
        include:
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: jenkins-stack
            helm-release-name: "jenkins"
            helm-chart-version: "0.1.0"
            helm-values-file-path: infrastructure/kubernetes/gcp-secops/us-west1/jenkins/values.yaml
            kube-namespace: "default"
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: atlassian-stack
            helm-release-name: "atlassian-confluence"
            helm-chart-version: "1.0.1"
            helm-values-file-path: infrastructure/kubernetes/gcp-secops/us-west1/confluence/values.yaml
            kube-namespace: "default"
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: atlassian-stack
            helm-release-name: "atlassian-bitbucket"
            helm-chart-version: "1.0.1"
            helm-values-file-path: infrastructure/kubernetes/gcp-secops/us-west1/bitbucket/values.yaml
            kube-namespace: "default"
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: atlassian-stack
            helm-release-name: "atlassian-jira"
            helm-chart-version: "1.0.1"
            helm-values-file-path: infrastructure/kubernetes/gcp-secops/us-west1/jira/values.yaml
            kube-namespace: "default"
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: gradle-cache
            helm-release-name: "gradle-cache"
            helm-chart-version: "0.1.0"
            helm-values-file-path: infrastructure/kubernetes/gcp-secops/us-west1/gradle-cache/values.yaml
            kube-namespace: "default"
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: arc-wrapper
            helm-release-name: "arc"
            helm-chart-version: "0.1.0"
            helm-values-file-path: infrastructure/kubernetes/gcp-secops/us-west1/actions-runner-controller/values.yaml
            kube-namespace: "default"
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: arc-wrapper
            helm-release-name: "arc-runner-set-2x"
            helm-chart-version: "0.1.0"
            helm-values-file-path: infrastructure/kubernetes/gcp-secops/us-west1/gha-runner-scale-set/values-2x.yaml
            kube-namespace: "default"
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: arc-wrapper
            helm-release-name: "arc-runner-set-4x"
            helm-chart-version: "0.1.0"
            helm-values-file-path: infrastructure/kubernetes/gcp-secops/us-west1/gha-runner-scale-set/values-4x.yaml
            kube-namespace: "default"
          - helm-repo-url: https://unblocked.github.io/unblocked-helm
            helm-repo-name: unblocked
            helm-chart-name: arc-wrapper
            helm-release-name: "arc-runner-set-9x"
            helm-chart-version: "0.1.0"
            helm-values-file-path: infrastructure/kubernetes/gcp-secops/us-west1/gha-runner-scale-set/values-9x.yaml
            kube-namespace: "default"
      fail-fast: false
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Deploy ${{ matrix.helm-release-name }} to Secops cluster
        uses: ./.github/actions/ci-k8s-gke-helm-deploy
        with:
          helm-repo-url: ${{ matrix.helm-repo-url }}
          helm-repo-name: ${{ matrix.helm-repo-name }}
          helm-chart-name: ${{ matrix.helm-chart-name }}
          helm-release-name: ${{ matrix.helm-release-name }}
          helm-chart-version: ${{ matrix.helm-chart-version }}
          helm-values-file-path: ${{ matrix.helm-values-file-path }}
          helm-values-cli-args: "--set config.hashedPassword=${{ secrets.GRADLE_CACHE_PASSWORD_HASH }} --set jenkins.controller.admin.password=${{ secrets.JENKINS_CONTROLLER_ADMIN_PASSWORD }} ${{ matrix.helm-flags }}"
          helm-flags: ${{ matrix.helm-flags }}
          kube-namespace: ${{ matrix.kube-namespace }}
          gcp-auth-json: ${{ secrets.DEPLOY_USER_AUTH_JSON }}
          gcp-region: "us-west1"
          gcp-project: "secops-467621"
          kubeconfig: ${{ secrets.DEPLOY_KUBECONFIG_US_WEST1 }}
          tailscale-auth-token: ${{ secrets.TAILSCALE_AUTH_TOKEN }}


