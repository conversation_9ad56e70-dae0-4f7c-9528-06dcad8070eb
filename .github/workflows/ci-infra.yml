name: Infra

concurrency:
  group: '${{ github.workflow }}@${{ github.event.pull_request.head.label || github.head_ref || github.ref }}-infra'
  cancel-in-progress: false
on:
  push:
    branches:
      - main
    paths:
      - '.github/workflows/ci-infra.yml'
      - 'infrastructure/cdk/core/**'
  pull_request:
    paths:
      - '.github/workflows/ci-infra.yml'
      - 'infrastructure/cdk/core/**'
  workflow_dispatch:

jobs:
  check-for-services-changes:
    timeout-minutes: 5
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
    outputs:
      files: ${{ steps.filter.outputs.files }}
    steps:

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
          token: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}

      - name: Paths Changes Filter
        uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            files:
              - '.github/workflows/ci-infra*'
              - 'infrastructure/cdk/core/**'

  build:
    timeout-minutes: 15
    needs:
      - check-for-services-changes
    if: ${{ needs.check-for-services-changes.outputs.files == 'true' }}
    name: cdk-build-diff
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./
    env:
      CI: true
    strategy:
      fail-fast: true
      matrix:
        include:
          - cdk-environment: dev-us-west-2
            role-arn: arn:aws:iam::************:role/OrganizationAccountAccessRole
            region: us-west-2
            gh-environment: development
            ansible-environment: dev
          - cdk-environment: dev-standard-us-east-1
            role-arn: arn:aws:iam::************:role/OrganizationAccountAccessRole
            region: us-east-1
            gh-environment: development
            ansible-environment: dev
          - cdk-environment: prod-us-west-2
            role-arn: arn:aws:iam::************:role/OrganizationAccountAccessRole
            region: us-west-2
            gh-environment: production
            ansible-environment: prod
          - cdk-environment: prod-standard-us-east-1
            role-arn: arn:aws:iam::************:role/OrganizationAccountAccessRole
            region: us-east-1
            gh-environment: production
            ansible-environment: prod
          - cdk-environment: sec-ops-us-west-2
            role-arn: arn:aws:iam::************:role/OrganizationAccountAccessRole
            region: us-west-2
            gh-environment: production
            ansible-environment: sec-ops
    environment: ${{ matrix.gh-environment }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
          token: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}

      - name: Decrypt CDK Environment Secrets
        uses: dawidd6/action-ansible-playbook@v2
        with:
          playbook: cdk-env-decrypt-playbook.yml
          directory: ./secrets/cdk
          vault_password: ${{ secrets.ANSIBLE_VAULT_PASSWORD }}
          options: |
            --extra-vars "env=${{ matrix.ansible-environment }}"
            --verbose

      - name: Assume Role
        id: aws-setup-creds-and-role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.DEPLOY_CDK_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_CDK_SECRET_ACCESS_KEY }}
          aws-region: ${{ matrix.region }}
          role-to-assume: ${{ matrix.role-arn }}
          role-skip-session-tagging: true
          role-duration-seconds: 1200

      - uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: './infrastructure/cdk/core/package-lock.json'

      - name: build lambda functions (install npm deps)
        shell: bash
        run: chmod +x ./infrastructure/cdk/core/assets/lambda/build.sh && ./infrastructure/cdk/core/assets/lambda/build.sh

      - name: Install dependencies
        shell: bash
        run: npm ci
        working-directory: './infrastructure/cdk/core'

      - name: CDK Diff ${{ matrix.cdk-environment }}
        shell: bash
        run: npx cdk diff -c config=${{ matrix.cdk-environment }} --all
        working-directory: './infrastructure/cdk/core'

      - name: CDK Synth ${{ matrix.cdk-environment }}
        if: ${{ github.event_name == 'pull_request' }}
        shell: bash
        run: npx cdk synth -c config=${{ matrix.cdk-environment }} --all -o "$GITHUB_WORKSPACE/cdk.out"
        working-directory: './infrastructure/cdk/core'

      - name: CDK Diff & PR Comment ${{ matrix.cdk-environment }}
        if: ${{ github.event_name == 'pull_request' }}
        uses: corymhall/cdk-diff-action@v2
        with:
          githubToken: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}

  deploy-dev:
    timeout-minutes: 120
    runs-on: ubuntu-22.04-4core
    environment: development
    needs: build
    if: github.ref == 'refs/heads/main'
    permissions:
      contents: read   # Needed for Slack action
      actions: read    # Needed for Slack action
    strategy:
      fail-fast: false
      matrix:
        include:
          - cdk-environment: dev-us-west-2
            region: us-west-2
          - cdk-environment: dev-standard-us-east-1
            region: us-east-1
    steps:
      - name: Checkout
        uses: actions/checkout@v4


      - name: Deploy ${{ matrix.cdk-environment }}
        uses: ./.github/actions/ci-cdk-deploy
        with:
          cdk-environment: ${{ matrix.cdk-environment }}
          ansible-environment: "dev"
          region: ${{ matrix.region }}
          role-arn: arn:aws:iam::************:role/OrganizationAccountAccessRole
          slack-webhook-url: ${{ secrets.SLACK_WEBHOOK_URL_PROD_ALARMS }}
          jfrog-secret-config-token: ${{ secrets.JFROG_SECRET_CONFIG_TOKEN }}
          gh-personal-access-token: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}
          ansible-vault-password: ${{ secrets.ANSIBLE_VAULT_PASSWORD }}
          aws-access-key-id: ${{ secrets.DEPLOY_CDK_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_CDK_SECRET_ACCESS_KEY }}

  deploy-prod:
    timeout-minutes: 120
    runs-on: ubuntu-22.04-4core
    environment: production
    if: ${{ (github.ref == 'refs/heads/main') && !contains(vars.SUSPEND_PROD_DEPLOYMENTS, 'true') }}
    needs: deploy-dev
    permissions:
      contents: read   # Needed for Slack action
      actions: read    # Needed for Slack action
    strategy:
      fail-fast: false
      matrix:
        include:
          - cdk-environment: prod-us-west-2
            region: us-west-2
          - cdk-environment: prod-standard-us-east-1
            region: us-east-1
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Deploy ${{ matrix.cdk-environment }}
        uses: ./.github/actions/ci-cdk-deploy
        with:
          cdk-environment: ${{ matrix.cdk-environment }}
          ansible-environment: "prod"
          region: ${{ matrix.region }}
          role-arn: arn:aws:iam::************:role/OrganizationAccountAccessRole
          slack-webhook-url: ${{ secrets.SLACK_WEBHOOK_URL_PROD_ALARMS }}
          jfrog-secret-config-token: ${{ secrets.JFROG_SECRET_CONFIG_TOKEN }}
          gh-personal-access-token: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}
          ansible-vault-password: ${{ secrets.ANSIBLE_VAULT_PASSWORD }}
          aws-access-key-id: ${{ secrets.DEPLOY_CDK_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_CDK_SECRET_ACCESS_KEY }}

  deploy-secops:
    timeout-minutes: 20
    runs-on: ubuntu-latest
    environment: production
    needs: deploy-dev
    if: github.ref == 'refs/heads/main'
    permissions:
      contents: read   # Needed for Slack action
      actions: read    # Needed for Slack action
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Deploy sec-ops-us-west-2
        uses: ./.github/actions/ci-cdk-deploy
        with:
          cdk-environment: "sec-ops-us-west-2"
          ansible-environment: "sec-ops"
          region: us-west-2
          role-arn: arn:aws:iam::************:role/OrganizationAccountAccessRole
          slack-webhook-url: ${{ secrets.SLACK_WEBHOOK_URL_PROD_ALARMS }}
          jfrog-secret-config-token: ${{ secrets.JFROG_SECRET_CONFIG_TOKEN }}
          gh-personal-access-token: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}
          ansible-vault-password: ${{ secrets.ANSIBLE_VAULT_PASSWORD }}
          aws-access-key-id: ${{ secrets.DEPLOY_CDK_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_CDK_SECRET_ACCESS_KEY }}
