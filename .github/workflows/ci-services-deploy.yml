# This is a reusable workflow meant to be invoked from other workflows
# https://docs.github.com/en/actions/using-workflows/reusing-workflows
name: Services Deploy Workflow

concurrency:
  group: '${{ github.workflow }}@${{ github.event.pull_request.head.label || github.head_ref || github.ref }}'

on:
  workflow_call:
    inputs:
      deploy-env:
        description: "The name of deployment target environment"
        required: true
        type: string
      gh-environment:
        description: "The full environment matching what's defined in GitHub environments"
        required: true
        type: string
      image-tag:
        description: "The docker image tag"
        required: true
        type: string
      annotate-deployment:
        description: Annotate grafana charts for this deployment
        required: true
        type: string
      kube-cluster-name:
        description: Name of target Kube cluster to avoid alb name clashes
        required: true
        type: string
      kube-api-host:
        description: API endpoint for the target cluster
        required: false
        type: string
      kubeconfig:
        description: Kubeconfig for target cluster
        required: false
        type: string
      max-parallel-deploys:
        description: Maximum number of services to be deployed in parallel
        required: false
        type: string
        default: 20

jobs:
  deploy-services-dev-1:
    if: ${{ inputs.kube-cluster-name == 'dev-1' }}  # run only for dev1
    timeout-minutes: 22
    continue-on-error: false
    name: ${{ matrix.service-name }}-${{ inputs.deploy-env }}
    runs-on: ubuntu-latest
    permissions:
      contents: read   # Needed for Slack action
      actions: read    # Needed for Slack action
    environment: ${{ inputs.gh-environment }}
    strategy:
      # Use this if you want to limit kube deployment concurrency
      max-parallel: ${{ fromJSON(inputs.max-parallel-deploys) }}
      matrix:
        include:
          - repo: adminwebservice
            service-name: adminwebservice
          - repo: apiservice
            service-name: apiservice
          - repo: assetservice
            service-name: assetservice
          - repo: authservice
            service-name: authservice
          - repo: billingservice
            service-name: billingservice
          - repo: ciservice
            service-name: ciservice
          - repo: dataservice
            service-name: dataservice
          - repo: embeddingservice
            service-name: embeddingservice
          - repo: indexservice
            service-name: indexservice
          - repo: ingest-asana
            service-name: ingest-asana
          - repo: ingest-coda
            service-name: ingest-coda
          # TODO MSTEAMS: Enable when ready to deploy
          #- repo: ingest-microsoft-teams
          #  service-name: ingest-microsoft-teams
          - repo: ingest-confluence
            service-name: ingest-confluence
          - repo: ingest-google
            service-name: ingest-google
          - repo: ingest-jira
            service-name: ingest-jira
          - repo: ingest-linear
            service-name: ingest-linear
          - repo: ingest-notion
            service-name: ingest-notion
          - repo: ingest-slack
            service-name: ingest-slack
          - repo: ingest-stackoverflow
            service-name: ingest-stackoverflow
          - repo: ingest-web
            service-name: ingest-web
          - repo: maintenanceservice
            service-name: maintenanceservice
          - repo: mcpservice
            service-name: mcpservice
          - repo: mermaidservice
            service-name: mermaidservice
          #- repo: mlrouterservice
          #  service-name: mlrouterservice
          - repo: notificationservice
            service-name: notificationservice
          - repo: proxy-provider
            service-name: proxy-provider
          - repo: publicapiservice
            service-name: publicapiservice
          - repo: pusherservice
            service-name: pusherservice
          - repo: queueservice
            service-name: queueservice
          - repo: review
            service-name: review
          - repo: scmservice
            service-name: scmservice
          - repo: searchservice
            service-name: searchservice
          - repo: slackservice
            service-name: slackservice
          - repo: sourcecodeservice
            service-name: sourcecodeservice
          - repo: summarizationservice
            service-name: summarizationservice
          - repo: telemetryservice
            service-name: telemetryservice
          - repo: topicservice
            service-name: topicservice
          - repo: webhookservice
            service-name: webhookservice
      fail-fast: false
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Deploy ${{ matrix.repo }} to ${{ inputs.deploy-env }}
        uses: ./.github/actions/ci-service-k8s-deploy
        with:
          deploy-env: ${{ inputs.deploy-env }}
          service-name: ${{ matrix.service-name }}
          service-release-name: ${{ matrix.service-name }}
          service-release-version: ${{ github.sha }}
          service-release-build-number: ${{ github.run_number }}
          service-helm-dir: ./projects/services/${{ matrix.service-name }}/.helm/${{ matrix.service-name }}
          image-repository: ${{ vars.ECR_REGISTRY_URL_WEST_2 }}/${{ matrix.repo }}
          image-tag: ${{ inputs.image-tag }}
          aws-access-key-id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
          aws-region: "us-west-2"
          ansible-vault-password: ${{ secrets.ANSIBLE_VAULT_PASSWORD }}
          kube-api-host: ${{ inputs.kube-api-host || vars.KUBE_API_URL_US_WEST_2_DEV1 }}
          kubeconfig: ${{ inputs.kubeconfig || secrets.DEPLOY_KUBECONFIG_US_WEST_2_DEV1 }}
          kube-cluster: ${{ inputs.kube-cluster-name }}
          bastion-host: "bhost.secops.getunblocked.com"
          bastion-private-ssh-key: ${{ secrets.BASTION_DEPLOY_SSH_KEY }}
          grafana-api-key: ${{ secrets.GRAFANA_API_KEY }}
          annotate-deployment: ${{ inputs.annotate-deployment }}

      - uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,message,commit,author,action,eventName,ref,workflow,job,took,pullRequest
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_INFRA_ALARMS }}
        if: failure()

  deploy-services-prod-1:
    if: ${{ inputs.kube-cluster-name == 'prod-1' }}  # run only for prod-1
    timeout-minutes: 22
    continue-on-error: false
    name: ${{ matrix.service-name }}-${{ inputs.deploy-env }}
    runs-on: ubuntu-latest
    permissions:
      contents: read   # Needed for Slack action
      actions: read    # Needed for Slack action
    environment: ${{ inputs.gh-environment }}
    strategy:
      # Use this if you want to limit kube deployment concurrency
      max-parallel: ${{ fromJSON(inputs.max-parallel-deploys) }}
      matrix:
        include:
          - repo: adminwebservice
            service-name: adminwebservice
          - repo: apiservice
            service-name: apiservice
          - repo: assetservice
            service-name: assetservice
          - repo: authservice
            service-name: authservice
          - repo: billingservice
            service-name: billingservice
          - repo: ciservice
            service-name: ciservice
          - repo: dataservice
            service-name: dataservice
          - repo: embeddingservice
            service-name: embeddingservice
          - repo: indexservice
            service-name: indexservice
          - repo: ingest-asana
            service-name: ingest-asana
          - repo: ingest-coda
            service-name: ingest-coda
          # TODO MSTEAMS: Enable when ready to deploy
          #- repo: ingest-microsoft-teams
          #  service-name: ingest-microsoft-teams
          - repo: ingest-confluence
            service-name: ingest-confluence
          - repo: ingest-google
            service-name: ingest-google
          - repo: ingest-jira
            service-name: ingest-jira
          - repo: ingest-linear
            service-name: ingest-linear
          - repo: ingest-notion
            service-name: ingest-notion
          - repo: ingest-slack
            service-name: ingest-slack
          - repo: ingest-stackoverflow
            service-name: ingest-stackoverflow
          - repo: ingest-web
            service-name: ingest-web
          - repo: maintenanceservice
            service-name: maintenanceservice
          - repo: mcpservice
            service-name: mcpservice
          - repo: mermaidservice
            service-name: mermaidservice
          #- repo: mlrouterservice
          #  service-name: mlrouterservice
          - repo: notificationservice
            service-name: notificationservice
          - repo: proxy-provider
            service-name: proxy-provider
          - repo: publicapiservice
            service-name: publicapiservice
          - repo: pusherservice
            service-name: pusherservice
          - repo: queueservice
            service-name: queueservice
          - repo: review
            service-name: review
          - repo: scmservice
            service-name: scmservice
          - repo: searchservice
            service-name: searchservice
          - repo: slackservice
            service-name: slackservice
          - repo: sourcecodeservice
            service-name: sourcecodeservice
          - repo: summarizationservice
            service-name: summarizationservice
          - repo: telemetryservice
            service-name: telemetryservice
          - repo: topicservice
            service-name: topicservice
          - repo: webhookservice
            service-name: webhookservice
      fail-fast: false
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Deploy ${{ matrix.repo }} to ${{ inputs.deploy-env }}
        uses: ./.github/actions/ci-service-k8s-deploy
        with:
          deploy-env: ${{ inputs.deploy-env }}
          service-name: ${{ matrix.service-name }}
          service-release-name: ${{ matrix.service-name }}
          service-release-version: ${{ github.sha }}
          service-release-build-number: ${{ github.run_number }}
          service-helm-dir: ./projects/services/${{ matrix.service-name }}/.helm/${{ matrix.service-name }}
          image-repository: ${{ vars.ECR_REGISTRY_URL_WEST_2 }}/${{ matrix.repo }}
          image-tag: ${{ inputs.image-tag }}
          aws-access-key-id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
          aws-region: "us-west-2"
          ansible-vault-password: ${{ secrets.ANSIBLE_VAULT_PASSWORD }}
          kube-api-host: ${{ inputs.kube-api-host || vars.KUBE_API_URL_US_WEST_2_PROD1 }}
          kubeconfig: ${{ inputs.kubeconfig || secrets.DEPLOY_KUBECONFIG_US_WEST_2_PROD1 }}
          kube-cluster: ${{ inputs.kube-cluster-name }}
          bastion-host: "bhost.secops.getunblocked.com"
          bastion-private-ssh-key: ${{ secrets.BASTION_DEPLOY_SSH_KEY }}
          grafana-api-key: ${{ secrets.GRAFANA_API_KEY }}
          annotate-deployment: ${{ inputs.annotate-deployment }}

      - uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,message,commit,author,action,eventName,ref,workflow,job,took,pullRequest
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_INFRA_ALARMS }}
        if: failure()