name: Services

on:
  push:
    branches:
      - main
    paths:
      - '.github/actions/ci-service-k8s-deploy/*'
      - '.github/workflows/ci-service*'
      - '.gitmodules'
      - 'api/**'
      - 'build.gradle.kts'
      - 'buildResources/**'
      - 'buildSrc/**'
      - 'common/src/**'
      - 'common/src/*.kts'
      - 'custom-ktlint-rules/**'
      - 'docker/**'
      - 'gradle.*'
      - 'gradle/**'
      - 'openapi/**'
      - 'projects/**'
      - 'secrets/**'
      - 'settings.gradle.kts'
  pull_request:
  merge_group:
    branches: [main]
  workflow_dispatch:
  workflow_call:
    inputs:
      caller:
        description: "Who triggered this"
        required: true
        type: string
        default: "unknown"

concurrency:
  group: services-${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  AWS_REGION: us-west-2 # set this to your preferred AWS region, e.g. us-west-1
  OUTPUT_ARTIFACT_NAME: built-artifacts-${{ github.run_number }}
  BUILD_ARTIFACTS_DIR: ./build/libs
  TOUCH: "Dummy value to force workflow rerun"
  # hack for https://github.com/actions/cache/issues/810#issuecomment-1222550359
  SEGMENT_DOWNLOAD_TIMEOUT_MINS: 3
  GH_CACHE_BUCKET: unblocked-gh-actions-s3-cache-sec-ops-us-west-2

jobs:
  check-for-services-changes:
    timeout-minutes: 5
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
      actions: read    # Needed for Slack action
    outputs:
      files: ${{ steps.filter.outputs.files }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Paths Changes Filter
        uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            files:
              - '.github/actions/ci-service-k8s-deploy/*'
              - '.github/workflows/ci-service*'
              - '.gitmodules'
              - 'api/**'
              - 'build.gradle.kts'
              - 'buildResources/**'
              - 'buildSrc/**'
              - 'common/src/**'
              - 'common/src/*.kts'
              - 'custom-ktlint-rules/**'
              - 'docker/**'
              - 'gradle.*'
              - 'gradle/**'
              - 'openapi/**'
              - 'projects/**'
              - 'secrets/**'
              - 'settings.gradle.kts'

  build:
    needs:
      - check-for-services-changes
    if: ${{ needs.check-for-services-changes.outputs.files == 'true' || (inputs.caller || '') == 'onprem-trigger' }}
    name: services-build
    timeout-minutes: 60
    runs-on: gcp-arc-runner-set-9x
    permissions:
      contents: write # Needed for Slack action
      actions: read  # Needed for Slack action
      checks: write # Needed for JUnit Report action
      pull-requests: write # Needed for JUnit Report action
    defaults:
      run:
        working-directory: ./
    env:
      CI: true
      CI_PR_TITLE: ${{ github.event.pull_request.title }}
      CI_TITLE: ${{ github.event.head_commit.message }}
      LANG: en_US.UTF-8
      LC_ALL: en_US.UTF-8
      JAVA_TOOL_OPTIONS: -Dfile.encoding=UTF-8


    steps:
      - name: Set HOME environment variable
        run: |
          export GH_HOME=~
          echo "GH_HOME=$GH_HOME" >> $GITHUB_ENV

      - run: env

      - name: Configure S3 AWS credentials
        id: aws-s3-creds
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          role-to-assume: 'arn:aws:iam::877923746456:role/IAMS3AccessRole-unblocked-gh-actions-s3-cache'
          role-skip-session-tagging: true
          role-duration-seconds: 2400

      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive
          token: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Ensure UTF-8 locale present
        run: |
          if command -v locale-gen >/dev/null 2>&1; then
            sudo apt-get update
            sudo apt-get install -y locales
            sudo locale-gen en_US.UTF-8
            sudo update-locale LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8
          fi

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y docker.io make ansible tar zstd

      - name: Run Ansible Playbook
        uses: dawidd6/action-ansible-playbook@v2
        with:
          playbook: local-env-decrypt-playbook.yml
          directory: ./secrets/local
          vault_password: ${{ secrets.VAULT_PASSWORD }}
          options: |
            --verbose

      - name: Set up Docker Compose
        uses: docker/setup-compose-action@v1

      - name: Start Docker Services
        run: make start-ci-services

      - name: Set up JDK
        uses: actions/setup-java@v4.7.1
        with:
          java-version: ${{ vars.DOCKER_JAVA_VERSION }}
          distribution: ${{ vars.DOCKER_JAVA_DISTRO }}
        timeout-minutes: 10

      - name: Install dependencies for public API
        run: npm ci
        working-directory: './projects/libs/lib-public-api'

      - name: Configure Gradle Arguments
        env:
          FAIL_SLOW: ${{ contains(env.CI_TITLE, '[FAIL SLOW]') && github.ref == 'refs/heads/main' }}
          SKIP_TESTS: ${{ contains(env.CI_TITLE, '[SKIP TESTS]') && github.ref == 'refs/heads/main' }}
        run: |
          export GRADLE_ARGS="--no-daemon build -x :common:generateProto -x :npmInstall"
          echo $SKIP_TESTS
          if [ ${SKIP_TESTS} == 'true' ]; then
            GRADLE_ARGS="$GRADLE_ARGS -x test"
          fi
          echo "GRADLE_ARGS=$(echo $GRADLE_ARGS)" >> $GITHUB_ENV

      - name: Register Gradle Java/Kotlin matchers
        run: echo "::add-matcher::.github/matchers/gradle-java-kotlin.json"

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
        with:
          dependency-graph: generate-and-submit
          # Non-main branches: restore-only; Main: can write/save cache
          cache-read-only: ${{ github.ref != 'refs/heads/main' }}
          cache-encryption-key: ${{ secrets.GRADLE_CACHE_ENCRYPTION_KEY }}

      - name: Build with Gradle
        run: ./gradlew ${{ env.GRADLE_ARGS }}
        env:
          GRADLE_CACHE_USERNAME: ${{ secrets.GRADLE_CACHE_USERNAME }}
          GRADLE_CACHE_PASSWORD: ${{ secrets.GRADLE_CACHE_PASSWORD }}
        timeout-minutes: 25

      - name: Publish Test Report
        uses: mikepenz/action-junit-report@v5.2.0
        if: success() || failure() # always run even if the previous step fails
        with:
          check_retries: true
          detailed_summary: true
          flaky_summary: true
          report_paths: '**/build/test-results/test/TEST-*.xml'
          job_name: 'services-build'
          update_check: true # need to use alternative mode to push in write check sub-job

      - name: Set Build Artifacts Cache Key
        env:
          cache-key: ${{ runner.os }}-build-artifacts-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }}
        shell: bash
        run: |
          echo "BUILD_ARTIFACTS_CACHE_KEY=$(echo ${{ env.cache-key }})" >> $GITHUB_ENV


      # Avoid upload-artifact action as it is 4x slower than using cache.
      # https://github.com/actions/upload-artifact/issues/199#issuecomment-1196557045
      - name: Cache Build Artifacts
        if: github.ref == 'refs/heads/main'
        uses: actions/cache@v4.2.3
        with:
          path: ${{ env.BUILD_ARTIFACTS_DIR }}
          key: ${{ env.BUILD_ARTIFACTS_CACHE_KEY }}
        env:
          ACTIONS_CACHE_SERVICE_V2: true

      - uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          fields: repo,message,commit,author,action,eventName,ref,workflow,job,took,pullRequest
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_DEV_ALARMS }}
        if: failure() && github.ref == 'refs/heads/main'



    # HACK due to bug with environment variables via reusable workflows
    # https://github.community/t/reusable-workflow-env-context-not-available-in-jobs-job-id-with/206111
    outputs:
      artifacts-cache-bucket: ${{ env.GH_CACHE_BUCKET }}
      artifacts-cache-key: ${{ env.BUILD_ARTIFACTS_CACHE_KEY }}
      artifacts-cache-path: ${{ env.BUILD_ARTIFACTS_DIR }}
      kube-api-host-dev: ${{ env.KUBE_API_HOST_DEV }}
      kube-api-host-prod: ${{ env.KUBE_API_HOST_PROD }}

  package:
    needs: build
    name: building docker images
    permissions:
      contents: read   # Needed for Slack action
      actions: read    # Needed for Slack action
    if: (inputs.caller || '') != 'onprem-trigger' && github.ref == 'refs/heads/main'
    uses: ./.github/workflows/ci-service-package.yml
    strategy:
      matrix:
        include:
          - repo: adminwebservice
            service-name: adminwebservice
          - repo: apiservice
            service-name: apiservice
          - repo: assetservice
            service-name: assetservice
          - repo: authservice
            service-name: authservice
          - repo: billingservice
            service-name: billingservice
          - repo: ciservice
            service-name: ciservice
          - repo: dataservice
            service-name: dataservice
          - repo: embeddingservice
            service-name: embeddingservice
          - repo: indexservice
            service-name: indexservice
          - repo: ingest-asana
            service-name: ingest-asana
          - repo: ingest-coda
            service-name: ingest-coda
          # TODO MSTEAMS: Enable when ready to deploy
          #- repo: ingest-microsoft-teams
          #  service-name: ingest-microsoft-teams
          - repo: ingest-confluence
            service-name: ingest-confluence
          - repo: ingest-google
            service-name: ingest-google
          - repo: ingest-jira
            service-name: ingest-jira
          - repo: ingest-linear
            service-name: ingest-linear
          - repo: ingest-notion
            service-name: ingest-notion
          - repo: ingest-slack
            service-name: ingest-slack
          - repo: ingest-stackoverflow
            service-name: ingest-stackoverflow
          - repo: ingest-web
            service-name: ingest-web
          - repo: maintenanceservice
            service-name: maintenanceservice
          - repo: mcpservice
            service-name: mcpservice
          - repo: mermaidservice
            service-name: mermaidservice
          #- repo: mlrouterservice
          #  service-name: mlrouterservice
          - repo: notificationservice
            service-name: notificationservice
          - repo: proxy-provider
            service-name: proxy-provider
          - repo: publicapiservice
            service-name: publicapiservice
          - repo: pusherservice
            service-name: pusherservice
          - repo: queueservice
            service-name: queueservice
          - repo: review
            service-name: review
          - repo: scmservice
            service-name: scmservice
          - repo: searchservice
            service-name: searchservice
          - repo: slackservice
            service-name: slackservice
          - repo: sourcecodeservice
            service-name: sourcecodeservice
          - repo: summarizationservice
            service-name: summarizationservice
          - repo: telemetryservice
            service-name: telemetryservice
          - repo: topicservice
            service-name: topicservice
          - repo: webhookservice
            service-name: webhookservice
    with:
      artifacts-cache-key: ${{ needs.build.outputs.artifacts-cache-key }}
      artifacts-cache-path: ${{ needs.build.outputs.artifacts-cache-path }}
      artifacts-cache-bucket: ${{ needs.build.outputs.artifacts-cache-bucket }}
      ecr-repo-name: ${{ matrix.repo }}
      service-name: ${{ matrix.service-name }}
      service-dir: ./projects/services/${{ matrix.service-name }}
      service-build-artifacts-dir: ./projects/services/${{ matrix.service-name }}/build/libs
      aws-region: us-west-2
    secrets:
      aws-access-key-id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
      aws-secret-access-key: ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
      slack-webhook-url-dev-alarms: ${{ secrets.SLACK_WEBHOOK_URL_DEV_ALARMS }}

  package-universal:
    needs: build
    name: building universal docker images
    permissions:
      contents: read   # Needed for Slack action
      actions: read    # Needed for Slack action
    if: (inputs.caller || '') == 'onprem-trigger' && github.ref == 'refs/heads/main'
    uses: ./.github/workflows/ci-service-package.yml
    with:
      artifacts-cache-key: ${{ needs.build.outputs.artifacts-cache-key }}
      artifacts-cache-path: ${{ needs.build.outputs.artifacts-cache-path }}
      artifacts-cache-bucket: ${{ needs.build.outputs.artifacts-cache-bucket }}
      ecr-repo-name: universal
      service-name: universal
      service-dir: ${{ needs.build.outputs.artifacts-cache-path }}
      service-build-artifacts-dir: ${{ needs.build.outputs.artifacts-cache-path }}
      aws-region: us-west-2
      service-dockerfile: universal.Dockerfile
      runs-on: ubuntu-22.04-4core
    secrets:
      aws-access-key-id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
      aws-secret-access-key: ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
      slack-webhook-url-dev-alarms: ${{ secrets.SLACK_WEBHOOK_URL_DEV_ALARMS }}

  cleanup:
    needs: [ build, package ]
    name: cleanup
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Configure S3 AWS credentials
        id: aws-s3-creds
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          role-to-assume: 'arn:aws:iam::877923746456:role/IAMS3AccessRole-unblocked-gh-actions-s3-cache'
          role-skip-session-tagging: true
          role-duration-seconds: 2400
  #
  # Disabled because we had to disable s3 caching of build artifacts due to s3 costs
  #      - name: Delete Build Artifacts
  #        uses: invitetest1/action-s3-cache@v1.0.4
  #        timeout-minutes: 4
  #        continue-on-error: true
  #        with:
  #          action: delete
  #          aws-access-key-id: ${{ env.AWS_ACCESS_KEY_ID }}
  #          aws-secret-access-key: ${{ env.AWS_SECRET_ACCESS_KEY }}
  #          aws-session-token: ${{ env.AWS_SESSION_TOKEN }}
  #          aws-region: ${{ env.AWS_REGION }}
  #          bucket: ${{ needs.build.outputs.artifacts-cache-bucket }}
  #          key: ${{ needs.build.outputs.artifacts-cache-key }}

  deploy-dev:
    needs: package
    if: (inputs.caller || '') != 'onprem-trigger' && github.ref == 'refs/heads/main'
    uses: ./.github/workflows/ci-services-deploy.yml
    permissions:
      contents: read   # Needed for Slack action
      actions: read    # Needed for Slack action
    with:
      deploy-env: "dev"
      gh-environment: "development"
      image-tag: 1-${{ github.sha }}
      annotate-deployment: 'false'
      kube-cluster-name: "dev-1"
      max-parallel-deploys: ${{ vars.MAX_PARALLEL_DEPLOYMENTS || 40 }}
    secrets: inherit

  deploy-prod:
    needs: [ package, deploy-dev ]
    if: ${{ (github.ref == 'refs/heads/main') && !contains(vars.SUSPEND_PROD_DEPLOYMENTS, 'true') }}
    uses: ./.github/workflows/ci-services-deploy.yml
    permissions:
      contents: read   # Needed for Slack action
      actions: read    # Needed for Slack action
    with:
      deploy-env: "prod"
      gh-environment: "production"
      image-tag: 1-${{ github.sha }}
      annotate-deployment: 'true'
      kube-cluster-name: "prod-1"
      max-parallel-deploys: ${{ vars.MAX_PARALLEL_DEPLOYMENTS || 20 }}
    secrets: inherit