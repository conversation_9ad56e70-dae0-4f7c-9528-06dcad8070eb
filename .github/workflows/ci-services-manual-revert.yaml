# ==========================================================
# Emergency Revert Workflow
# ==========================================================
#
# Deploys all services to a specific Git SHA in production.
#
# Key checks:
# - Fails if DB migrations differ between main and provided SHA.
# - Verifies that Docker images for all services exist in ECR (tag: 1-<SHA>).
# - Only proceeds if both repo variables ENABLE_EMERGENCY_REVERT and
#   SUSPEND_PROD_DEPLOYMENTS are set to 'true' (admins-only).
#
# Usage:
# - Trigger manually via "Run workflow".
# - Provide target Git SHA as input.
# - Ensure both repo variables are true before running.
#
# WARNING: Critical workflow. Only use for emergency rollbacks.
# ==========================================================
name: Emergency Rollback
on:
  workflow_dispatch:
    inputs:
      sha:
        description: "The Git SHA to revert to"
        required: true
        type: string

env:
  AWS_REGION: us-west-2
  ECR_REPOS: |
    adminwebservice
    apiservice
    assetservice
    authservice
    billingservice
    ciservice
    dataservice
    embeddingservice
    indexservice
    ingest-asana
    ingest-coda
    ingest-confluence
    ingest-google
    ingest-jira
    ingest-linear
    ingest-notion
    ingest-slack
    ingest-stackoverflow
    ingest-web
    maintenanceservice
    mcpservice
    mermaidservice
    notificationservice
    proxy-provider
    publicapiservice
    pusherservice
    queueservice
    review
    scmservice
    searchservice
    slackservice
    sourcecodeservice
    summarizationservice
    telemetryservice
    topicservice
    webhookservice

jobs:
  validate-sha:
    runs-on: ubuntu-latest
    if: ${{ (github.ref == 'refs/heads/main') && contains(vars.SUSPEND_PROD_DEPLOYMENTS, 'true') && contains(vars.ENABLE_EMERGENCY_REVERT, 'true') }}
    steps:
      - name: Checkout main
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Validate DB migrations
        run: |
          TARGET_SHA="${{ github.event.inputs.sha }}"
          echo "Checking DB schema diffs between main and $TARGET_SHA..."
          git fetch origin main
          if git diff --exit-code origin/main $TARGET_SHA -- projects/models/src/main/resources/db/migration; then
            echo "✅ No DB migration differences found."
          else
            echo "❌ DB schema differences detected. Aborting."
            exit 1
          fi

  check-images:
    needs: validate-sha
    runs-on: ubuntu-latest
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
          role-to-assume: arn:aws:iam::877923746456:role/EcrDeployerRole
          role-skip-session-tagging: true

      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v2

      - name: Verify ECR images exist
        run: |
          TARGET_SHA="${{ github.event.inputs.sha }}"
          IMAGE_TAG="1-${TARGET_SHA}"
          MISSING=0
          for REPO in $ECR_REPOS; do
            echo "Checking $REPO:$IMAGE_TAG ..."
            if aws ecr describe-images \
              --repository-name "$REPO" \
              --image-ids imageTag="$IMAGE_TAG" \
              --region "${AWS_REGION}" > /dev/null 2>&1; then
              echo "✅ Found $REPO:$IMAGE_TAG"
            else
              echo "❌ Missing $REPO:$IMAGE_TAG"
              MISSING=1
            fi
          done
          if [ $MISSING -ne 0 ]; then
            echo "One or more required images are missing in ECR. Aborting."
            exit 1
          fi

  #deploy-dev:
   # needs: check-images
   # uses: ./.github/workflows/ci-services-deploy.yml
   # with:
   #   deploy-env: "dev"
   #   gh-environment: "development"
   #   image-tag: 1-${{ github.event.inputs.sha }}
   #   annotate-deployment: 'false'
   #   kube-cluster-name: "dev-1"
  #    max-parallel-deploys: ${{ vars.MAX_PARALLEL_DEPLOYMENTS || 40 }}
  #  secrets: inherit

  deploy-prod:
    needs: [check-images]
    if: github.ref == 'refs/heads/main'
    uses: ./.github/workflows/ci-services-deploy.yml
    with:
      deploy-env: "prod"
      gh-environment: "production"
      image-tag: 1-${{ github.event.inputs.sha }}
      annotate-deployment: 'true'
      kube-cluster-name: "prod-2"
      max-parallel-deploys: ${{ vars.MAX_PARALLEL_DEPLOYMENTS || 30 }}
    secrets: inherit
