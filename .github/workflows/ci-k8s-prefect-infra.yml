name: Kubernetes Prefect Infrastructure

concurrency:
  group: '${{ github.workflow }}@${{ github.event.pull_request.head.label || github.head_ref || github.ref }}-prefect-infra'
  cancel-in-progress: false
on:
  push:
    branches:
      - main
    paths:
      - 'infrastructure/prefect/prefect-deployer/**'
      - '.github/actions/ci-k8s-prefect*'
      - '.github/workflows/ci-k8s-prefect*'
  workflow_dispatch:

env:
  image-tag: ${{ github.run_attempt }}-${{ github.sha }}

jobs:
  check-for-services-changes:
    timeout-minutes: 5
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: read
    outputs:
      files: ${{ steps.filter.outputs.files }}
    steps:

      - name: Checkout
        uses: actions/checkout@v4
        with:
          submodules: recursive
          token: ${{ secrets.GH_PERSONAL_ACCESS_TOKEN }}

      - name: Paths Changes Filter
        uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            files:
              - 'infrastructure/prefect/prefect-deployer/**'
              - '.github/actions/ci-k8s-prefect*'
              - '.github/workflows/ci-k8s-prefect*'

  deploy-prefect-dev:
    timeout-minutes: 120
    runs-on: ubuntu-22.04-4core
    environment: development
    needs:
      - check-for-services-changes
    if: ${{ needs.check-for-services-changes.outputs.files == 'true' && github.ref == 'refs/heads/main' }}
    permissions:
      contents: read   # Needed for Slack action
      actions: read    # Needed for Slack action
    env:
      deploy-env: dev
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Deploy dev-1
        uses: ./.github/actions/ci-k8s-prefect-deploy
        with:
          deploy-env: ${{ env.deploy-env }}
          ecr-registry: ${{ vars.ECR_REGISTRY_URL_WEST_2 }}
          image-tag: ${{ env.image-tag }}
          aws-access-key-id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
          aws-region: "us-west-2"
          kube-cluster-name: "dev-1"
          kubeconfig: ${{ secrets.DEPLOY_KUBECONFIG_US_WEST_2_DEV1 }}
          kube-api-host: ${{ vars.KUBE_API_URL_US_WEST_2_DEV1 }}
          bastion-host: "bhost.secops.getunblocked.com"
          bastion-private-ssh-key: ${{ secrets.BASTION_DEPLOY_SSH_KEY }}
          ansible-vault-password: ${{ secrets.ANSIBLE_VAULT_PASSWORD }}
          jfrog-password: ${{ secrets.JFROG_PASSWORD }}

  deploy-prefect-prod:
    timeout-minutes: 120
    runs-on: ubuntu-22.04-4core
    environment: production
    needs:
      - check-for-services-changes
    if: ${{ needs.check-for-services-changes.outputs.files == 'true' && github.ref == 'refs/heads/main'  && !contains(vars.SUSPEND_PROD_DEPLOYMENTS, 'true') }}
    permissions:
      contents: read   # Needed for Slack action
      actions: read    # Needed for Slack action
    env:
      deploy-env: prod
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Deploy prod-1
        uses: ./.github/actions/ci-k8s-prefect-deploy
        with:
          deploy-env: ${{ env.deploy-env }}
          ecr-registry: ${{ vars.ECR_REGISTRY_URL_WEST_2 }}
          image-tag: ${{ env.image-tag }}
          aws-access-key-id: ${{ secrets.DEPLOY_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.DEPLOY_AWS_SECRET_ACCESS_KEY }}
          aws-region: "us-west-2"
          kube-cluster-name: "prod-1"
          kubeconfig: ${{ secrets.DEPLOY_KUBECONFIG_US_WEST_2_PROD1 }}
          kube-api-host: ${{ vars.KUBE_API_URL_US_WEST_2_PROD1 }}
          bastion-host: "bhost.secops.getunblocked.com"
          bastion-private-ssh-key: ${{ secrets.BASTION_DEPLOY_SSH_KEY }}
          ansible-vault-password: ${{ secrets.ANSIBLE_VAULT_PASSWORD }}
          jfrog-password: ${{ secrets.JFROG_PASSWORD }}