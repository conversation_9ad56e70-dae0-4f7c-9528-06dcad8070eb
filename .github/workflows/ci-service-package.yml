# This is a reusable workflow meant to be invoked from other workflows
# https://docs.github.com/en/actions/using-workflows/reusing-workflows
name: Service Package Workflow

on:
  workflow_call:
    outputs:
      image-tag:
        description: "The docker tag for current image"
        value: ${{ jobs.package-image.outputs.image-tag }}
    inputs:
      artifacts-cache-bucket:
        description: "The cache bucket for artifacts"
        required: true
        type: string
      artifacts-cache-key:
        description: "The cache key for the artifacts that holds service jars and other dependencies"
        required: true
        type: string
      artifacts-cache-path:
        description: "The original path that was used to cache artifacts that holds service jars and other dependencies"
        required: true
        type: string
      ecr-repo-name:
        description: "The name of ecr repo which holds images for this service"
        required: true
        type: string
      service-name:
        description: "The name of the service to be deployed"
        required: true
        type: string
      service-dir:
        description: "The service directory"
        required: true
        type: string
      aws-region:
        description: "The AWS region"
        required: true
        type: string
      runs-on:
        description: "The github builder type"
        required: false
        default: "ubuntu-latest"
        type: string
      service-dockerfile:
        description: "The Dockerfile to use for building the service image"
        required: false
        type: string
        default: "Dockerfile"
      service-build-artifacts-dir:
        description: "The directory for service build artifacts"
        required: true
        type: string

    secrets:
      aws-access-key-id:
        description: "The AWS access key"
        required: true
      aws-secret-access-key:
        description: "The AWS secret access key"
        required: true
      slack-webhook-url-dev-alarms:
        description: Slack webhook url for infra-alarms-dev channel
        required: true

jobs:
  package-image:
    timeout-minutes: 15
    name: build-${{ inputs.service-name }}-image
    runs-on: ${{ inputs.runs-on }}
    outputs:
      image-tag: ${{ steps.set-tag.outputs.image-tag }}
    steps:
      - name: Set fixed image tag
        id: set-tag
        run: echo "image-tag=1-${GITHUB_SHA}" >> $GITHUB_OUTPUT

      - name: Checkout
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.aws-access-key-id }}
          aws-secret-access-key: ${{ secrets.aws-secret-access-key }}
          aws-region: ${{ inputs.aws-region }}
          role-to-assume: arn:aws:iam::877923746456:role/EcrDeployerRole
          role-skip-session-tagging: true

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Check if image already exists in ECR
        id: check-image
        run: |
          IMAGE_TAG="1-${GITHUB_SHA}"
          REPO="${{ inputs.ecr-repo-name }}"
          REGION="${{ inputs.aws-region }}"

          echo "Checking for image ${REPO}:${IMAGE_TAG} in ${REGION}..."

          set +e
          aws ecr describe-images \
            --repository-name "$REPO" \
            --image-ids imageTag="$IMAGE_TAG" \
            --region "$REGION" > /dev/null 2>&1
          EXISTS=$?
          set -e

          if [ $EXISTS -eq 0 ]; then
            echo "Image already exists in ECR. Skipping build."
            echo "skip-build=true" >> $GITHUB_OUTPUT
          else
            echo "Image does not exist. Will build."
            echo "skip-build=false" >> $GITHUB_OUTPUT
          fi

      - name: Download Cached Build Artifacts
        if: steps.check-image.outputs.skip-build == 'false'
        uses: Wandalen/wretry.action@v3.1.0
        with:
          attempt_limit: 10
          attempt_delay: 15000
          action: actions/cache@v4.2.3
          with: |
            path: ${{ inputs.artifacts-cache-path }}
            restore-keys: ${{ inputs.artifacts-cache-key }}
            key: ${{ inputs.artifacts-cache-key }}
            fail-on-cache-miss: true
        env:
          ACTIONS_CACHE_SERVICE_V2: true

      - name: List Artifacts Cache Path
        if: steps.check-image.outputs.skip-build == 'false'
        run: ls -al ${{ inputs.artifacts-cache-path }}

      - name: Move Build Artifacts to Service Destination
        if: steps.check-image.outputs.skip-build == 'false' && inputs.artifacts-cache-path != inputs.service-build-artifacts-dir
        run: |
          mkdir -p ${{ inputs.service-build-artifacts-dir }}
          mv ${{ inputs.artifacts-cache-path }}/* ${{ inputs.service-build-artifacts-dir }}
          ls -al ${{ inputs.service-build-artifacts-dir }}

      - name: Build and publish to ECR
        if: steps.check-image.outputs.skip-build == 'false'
        id: publish-image
        uses: ./.github/actions/ci-docker-ecr-publish
        with:
          image-dir: ${{ inputs.service-dir }}
          image-dockerfile: ${{ inputs.service-dockerfile }}
          ecr-repo-name: ${{ inputs.ecr-repo-name }}
          aws-access-key-id: ${{ secrets.aws-access-key-id }}
          aws-secret-access-key: ${{ secrets.aws-secret-access-key }}
          aws-region: ${{ inputs.aws-region }}
          image-tag: ${{ steps.set-tag.outputs.image-tag }}

      - uses: 8398a7/action-slack@v3
        if: failure()
        with:
          status: ${{ job.status }}
          fields: repo,message,commit,author,action,eventName,ref,workflow,job,took,pullRequest
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.slack-webhook-url-dev-alarms }}

