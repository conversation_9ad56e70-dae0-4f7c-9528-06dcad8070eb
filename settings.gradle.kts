import java.nio.file.Paths

rootProject.name = "unblocked"

include("api")
include("common")
include("custom-ktlint-rules")
include("projects:apps:askapi")
include("projects:apps:dbmigrator")
include("projects:apps:slackautoresponsemetrics")
include("projects:apps:slackextractor")
include("projects:apps:slackincidentanalysis")
include("projects:clients:client-activemq")
include("projects:clients:client-anthropic")
include("projects:clients:client-asana")
include("projects:clients:client-assemblyai")
include("projects:clients:client-atlassian")
include("projects:clients:client-ci")
include("projects:clients:client-coda")
include("projects:clients:client-cohere")
include("projects:clients:client-confluence")
include("projects:clients:client-google")
include("projects:clients:client-google-ai")
include("projects:clients:client-intercom")
include("projects:clients:client-jira")
include("projects:clients:client-linear")
include("projects:clients:client-microsoft-graph")
include("projects:clients:client-ml")
include("projects:clients:client-mongo")
include("projects:clients:client-notion")
include("projects:clients:client-openai")
include("projects:clients:client-opensearch")
include("projects:clients:client-pinecone")
include("projects:clients:client-prefect")
include("projects:clients:client-redis")
include("projects:clients:client-scm")
include("projects:clients:client-sendgrid")
include("projects:clients:client-slack")
include("projects:clients:client-stackoverflowteams")
include("projects:clients:client-stripe")
include("projects:documents:documents-mongo-core")
include("projects:documents:documents-mongo-serialization")
include("projects:documents:documents-mongo-scm")
include("projects:libs:lib-access")
include("projects:libs:lib-activation")
include("projects:libs:lib-analytics")
include("projects:libs:lib-anthropic-api")
include("projects:libs:lib-api")
include("projects:libs:lib-api-auth")
include("projects:libs:lib-api-generated")
include("projects:libs:lib-api-integration")
include("projects:libs:lib-api-integration-extension")
include("projects:libs:lib-api-model")
include("projects:libs:lib-api-test")
include("projects:libs:lib-api-threads")
include("projects:libs:lib-asana")
include("projects:libs:lib-asana-auth")
include("projects:libs:lib-asana-ingestion")
include("projects:libs:lib-asset")
include("projects:libs:lib-atlassian")
include("projects:libs:lib-atlassian-auth")
include("projects:libs:lib-atlassian-forge")
include("projects:libs:lib-audit-log")
include("projects:libs:lib-auth")
include("projects:libs:lib-auth-provider")
include("projects:libs:lib-auth-saml")
include("projects:libs:lib-auth-saml-api")
include("projects:libs:lib-auth-secret")
include("projects:libs:lib-aws")
include("projects:libs:lib-aws-auth")
include("projects:libs:lib-aws-bedrock-anthropic")
include("projects:libs:lib-aws-bedrock-cohere")
include("projects:libs:lib-aws-rpc")
include("projects:libs:lib-billing")
include("projects:libs:lib-billing-events")
include("projects:libs:lib-billing-utils")
include("projects:libs:lib-bot")
include("projects:libs:lib-bot-toolbox")
include("projects:libs:lib-cache")
include("projects:libs:lib-cas")
include("projects:libs:lib-ci")
include("projects:libs:lib-ci-auth")
include("projects:libs:lib-ci-config")
include("projects:libs:lib-ci-controller")
include("projects:libs:lib-ci-feedback")
include("projects:libs:lib-ci-logs")
include("projects:libs:lib-ci-triage")
include("projects:libs:lib-client-api-generated")
include("projects:libs:lib-client-config")
include("projects:libs:lib-coda")
include("projects:libs:lib-coda-api")
include("projects:libs:lib-coda-data")
include("projects:libs:lib-coda-events")
include("projects:libs:lib-coda-ingestion")
include("projects:libs:lib-cohere-api")
include("projects:libs:lib-common")
include("projects:libs:lib-compress")
include("projects:libs:lib-config")
include("projects:libs:lib-confluence")
include("projects:libs:lib-confluence-auth")
include("projects:libs:lib-confluence-data")
include("projects:libs:lib-confluence-dsac")
include("projects:libs:lib-confluence-events")
include("projects:libs:lib-confluence-ingestion")
include("projects:libs:lib-contracts")
include("projects:libs:lib-conversation-analysis")
include("projects:libs:lib-crypto")
include("projects:libs:lib-data-preset")
include("projects:libs:lib-data-preset-config")
include("projects:libs:lib-data-sources")
include("projects:libs:lib-diff")
include("projects:libs:lib-diff-utils")
include("projects:libs:lib-dns")
include("projects:libs:lib-document-data")
include("projects:libs:lib-document-embedding")
include("projects:libs:lib-document-encryption")
include("projects:libs:lib-document-events")
include("projects:libs:lib-dsac")
include("projects:libs:lib-dsac-provider")
include("projects:libs:lib-embedding")
include("projects:libs:lib-embedding-events")
include("projects:libs:lib-emoji")
include("projects:libs:lib-environment-dsl")
include("projects:libs:lib-dashboard-extensions")
include("projects:libs:lib-event-queue")
include("projects:libs:lib-expert")
include("projects:libs:lib-failsafe")
include("projects:libs:lib-feedback")
include("projects:libs:lib-ghdiscussions-ingestion")
include("projects:libs:lib-ghissues-ingestion")
include("projects:libs:lib-git")
include("projects:libs:lib-google")
include("projects:libs:lib-google-auth")
include("projects:libs:lib-google-events")
include("projects:libs:lib-google-ingestion")
include("projects:libs:lib-graphql")
include("projects:libs:lib-grpc")
include("projects:libs:lib-ingestion")
include("projects:libs:lib-ingestion-common")
include("projects:libs:lib-ingestion-pipeline")
include("projects:libs:lib-insider")
include("projects:libs:lib-insight")
include("projects:libs:lib-installation")
include("projects:libs:lib-installation-bot")
include("projects:libs:lib-integration-data")
include("projects:libs:lib-integration-data-events")
include("projects:libs:lib-integration-events")
include("projects:libs:lib-intercom")
include("projects:libs:lib-intercom-service")
include("projects:libs:lib-jira")
include("projects:libs:lib-jira-auth")
include("projects:libs:lib-jira-data")
include("projects:libs:lib-jira-events")
include("projects:libs:lib-jira-ingestion")
include("projects:libs:lib-job")
include("projects:libs:lib-kotlin")
include("projects:libs:lib-ktor")
include("projects:libs:lib-ktor-client-engine")
include("projects:libs:lib-ktor-client-org")
include("projects:libs:lib-linear")
include("projects:libs:lib-linear-auth")
include("projects:libs:lib-linear-data")
include("projects:libs:lib-linear-events")
include("projects:libs:lib-linear-ingestion")
include("projects:libs:lib-link-hydration")
include("projects:libs:lib-log")
include("projects:libs:lib-log-kotlin")
include("projects:libs:lib-log-sensitive")
include("projects:libs:lib-maintenance")
include("projects:libs:lib-maintenance-events")
include("projects:libs:lib-maintenance-installation")
include("projects:libs:lib-maintenance-migration")
include("projects:libs:lib-maintenance-org")
include("projects:libs:lib-maintenance-scm")
include("projects:libs:lib-markdown")
include("projects:libs:lib-markdown-document")
include("projects:libs:lib-mcp-definitions")
include("projects:libs:lib-mcp-execution")
include("projects:libs:lib-membership")
include("projects:libs:lib-metrics")
include("projects:libs:lib-microsoft-graph-auth")
include("projects:libs:lib-microsoft-bot")
include("projects:libs:lib-microsoft-teams-ingestion")
include("projects:libs:lib-migration-events")
include("projects:libs:lib-ml-completion")
include("projects:libs:lib-ml-completion-prompts")
include("projects:libs:lib-ml-doc-converter")
include("projects:libs:lib-ml-doc-rerank")
include("projects:libs:lib-ml-embedding")
include("projects:libs:lib-ml-embedding-core")
include("projects:libs:lib-ml-embedding-input")
include("projects:libs:lib-ml-embedding-opensearch")
include("projects:libs:lib-ml-embedding-pinecone")
include("projects:libs:lib-ml-embedding-query")
include("projects:libs:lib-ml-functions")
include("projects:libs:lib-ml-functions-core")
include("projects:libs:lib-ml-inference")
include("projects:libs:lib-ml-input")
include("projects:libs:lib-ml-prompt")
include("projects:libs:lib-ml-query-context")
include("projects:libs:lib-mlrouter")
include("projects:libs:lib-mongo")
include("projects:libs:lib-network")
include("projects:libs:lib-notification")
include("projects:libs:lib-notification-events")
include("projects:libs:lib-notion")
include("projects:libs:lib-notion-auth")
include("projects:libs:lib-notion-events")
include("projects:libs:lib-notion-ingestion")
include("projects:libs:lib-oauth")
include("projects:libs:lib-opensearch")
include("projects:libs:lib-pagination")
include("projects:libs:lib-partition")
include("projects:libs:lib-pdf")
include("projects:libs:lib-person-preferences")
include("projects:libs:lib-pinecone")
include("projects:libs:lib-plan-capabilities")
include("projects:libs:lib-pr")
include("projects:libs:lib-pr-ingestion")
include("projects:libs:lib-pr-summary-ingestion")
include("projects:libs:lib-prefect")
include("projects:libs:lib-product-feedback")
include("projects:libs:lib-proto-generated")
include("projects:libs:lib-public-api")
include("projects:libs:lib-public-api-auth")
include("projects:libs:lib-public-api-generated")
include("projects:libs:lib-public-api-key")
include("projects:libs:lib-public-api-model")
include("projects:libs:lib-queue")
include("projects:libs:lib-rapid")
include("projects:libs:lib-ratelimit")
include("projects:libs:lib-recommendation")
include("projects:libs:lib-regression-testing")
include("projects:libs:lib-repo-access")
include("projects:libs:lib-repo-access-rpc")
include("projects:libs:lib-review")
include("projects:libs:lib-review-controller")
include("projects:libs:lib-review-events")
include("projects:libs:lib-rpc")
include("projects:libs:lib-scim")
include("projects:libs:lib-scm")
include("projects:libs:lib-scm-config")
include("projects:libs:lib-scm-data")
include("projects:libs:lib-scm-ingestion")
include("projects:libs:lib-search")
include("projects:libs:lib-search-events")
include("projects:libs:lib-search-indexing-events")
include("projects:libs:lib-search-semantic")
include("projects:libs:lib-security")
include("projects:libs:lib-semantic-bot")
include("projects:libs:lib-sendgrid")
include("projects:libs:lib-serialization")
include("projects:libs:lib-service")
include("projects:libs:lib-service-bootstrap")
include("projects:libs:lib-slack")
include("projects:libs:lib-slack-auth")
include("projects:libs:lib-slack-bot")
include("projects:libs:lib-slack-bot-events")
include("projects:libs:lib-slack-data")
include("projects:libs:lib-slack-events")
include("projects:libs:lib-slack-extractor")
include("projects:libs:lib-slack-ingestion")
include("projects:libs:lib-slack-notify")
include("projects:libs:lib-slack-preferences")
include("projects:libs:lib-slack-webhook")
include("projects:libs:lib-sourcecode")
include("projects:libs:lib-sourcecode-events")
include("projects:libs:lib-sourcecode-ingestion")
include("projects:libs:lib-stackoverflowteams")
include("projects:libs:lib-stackoverflowteams-ingestion")
include("projects:libs:lib-stripe")
include("projects:libs:lib-stripe-events")
include("projects:libs:lib-stripe-webhook")
include("projects:libs:lib-summarization")
include("projects:libs:lib-summarization-events")
include("projects:libs:lib-team-orchestration")
include("projects:libs:lib-temporal")
include("projects:libs:lib-topic")
include("projects:libs:lib-topic-events")
include("projects:libs:lib-topic-ingestion")
include("projects:libs:lib-topic-insight")
include("projects:libs:lib-topic-search")
include("projects:libs:lib-trace")
include("projects:libs:lib-trace-aws")
include("projects:libs:lib-trace-coroutines")
include("projects:libs:lib-trace-grpc")
include("projects:libs:lib-trace-jdbc")
include("projects:libs:lib-trace-jms")
include("projects:libs:lib-trace-jvm")
include("projects:libs:lib-trace-krpc")
include("projects:libs:lib-trace-ktor")
include("projects:libs:lib-trace-redis")
include("projects:libs:lib-trace-service")
include("projects:libs:lib-user-engagement")
include("projects:libs:lib-user-secret")
include("projects:libs:lib-versions")
include("projects:libs:lib-web-events")
include("projects:libs:lib-web-ingestion")
include("projects:libs:lib-xml")
include("projects:models")
include("projects:scripts")
include("projects:services:adminwebservice")
include("projects:services:apiservice")
include("projects:services:assetservice")
include("projects:services:authservice")
include("projects:services:billingservice")
include("projects:services:ciservice")
include("projects:services:dataservice")
include("projects:services:embeddingservice")
include("projects:services:indexservice")
include("projects:services:ingest-asana")
include("projects:services:ingest-coda")
include("projects:services:ingest-confluence")
include("projects:services:ingest-google")
include("projects:services:ingest-jira")
include("projects:services:ingest-linear")
include("projects:services:ingest-microsoft-teams")
include("projects:services:ingest-notion")
include("projects:services:ingest-slack")
include("projects:services:ingest-stackoverflow")
include("projects:services:ingest-web")
include("projects:services:maintenanceservice")
include("projects:services:mcpservice")
include("projects:services:mermaidservice")
include("projects:services:mlrouterservice")
include("projects:services:notificationservice")
include("projects:services:proxy-provider")
include("projects:services:publicapiservice")
include("projects:services:pusherservice")
include("projects:services:queueservice")
include("projects:services:review")
include("projects:services:scmservice")
include("projects:services:searchservice")
include("projects:services:slackservice")
include("projects:services:sourcecodeservice")
include("projects:services:summarizationservice")
include("projects:services:telemetryservice")
include("projects:services:topicservice")
include("projects:services:webhookservice")
include("projects:services:mcpservice")
include("projects:services:msteamsservice")

if (gradle.startParameter.projectProperties["includeJetbrains"] == "true" ||
    providers.gradleProperty("includeJetbrains").orNull == "true"
) {
    include("jetbrains")
}

dependencyResolutionManagement {
    versionCatalogs {
        create("testLibs") {
            from(files("${rootProject.projectDir}/gradle/test.libs.versions.toml"))
        }
    }
}

pluginManagement {
    plugins {
        kotlin("jvm") version "2.2.0" apply false
        kotlin("plugin.serialization") version "2.2.0" apply false

        id("com.expediagroup.graphql") version "7.1.1" apply false
        id("com.gradleup.shadow") version "8.3.9" apply false
        id("com.github.node-gradle.node") version "8.0.0-SNAPSHOT" apply false
        id("com.google.protobuf") version "0.9.4" apply false
        id("io.freefair.github.dependency-submission") version "8.6" apply false
        id("io.gitlab.arturbosch.detekt") version "1.22.0" apply false
        id("org.cyclonedx.bom") version "1.8.2" apply false
        id("org.gradle.test-retry") version "1.6.2" apply false
        id("org.jetbrains.kotlin.plugin.jpa") version "2.2.0" apply false
        id("org.jetbrains.kotlinx.rpc.plugin") version "0.9.1" apply false
        // BE CAREFUL: Version 5.0.1 in particular causes jetbrains build issues with class not defined
        if (gradle.startParameter.projectProperties["includeJetbrains"] == "true" ||
            providers.gradleProperty("includeJetbrains").orNull == "true"
        ) {
            id("org.jmailen.kotlinter") version "4.3.0" apply false
        } else {
            id("org.jmailen.kotlinter") version "5.1.1" apply false
        }
        id("org.openapi.generator") version "6.4.0" apply false
    }

    repositories {
        mavenCentral()
        google()
        gradlePluginPortal()
        maven { url = uri("${rootProject.projectDir.absolutePath}/.maven") }
    }
}

buildCache {
    local {
        directory = Paths.get(rootProject.projectDir.absolutePath, ".build-cache")
    }
    remote(HttpBuildCache::class) {
        url = uri("http://gradle-cache.default.svc.cluster.local:5071/cache/")
        isEnabled = true
        isPush = true
        isAllowUntrustedServer = true
        isAllowInsecureProtocol = true
        credentials {
            val cacheUser = providers.environmentVariable("GRADLE_CACHE_USERNAME").orNull ?: ""
            val cachePass = providers.environmentVariable("GRADLE_CACHE_PASSWORD").orNull ?: ""
            username = cacheUser
            password = cachePass
        }
    }
}
