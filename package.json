{"name": "unblocked", "version": "1.0.0", "description": "Unblocked", "workspaces": ["shared", "vscode", "web"], "scripts": {"fix-lint": "npm run fix-lint --workspaces && npm run fix-lint --prefix=desktop", "fix-vscode-lint": "npm run fix-lint --workspace=vscode", "fix-web-lint": "npm run fix-lint --workspace=web", "fix-desktop-lint": "npm run fix-lint --prefix=desktop", "fix-shared-lint": "npm run fix-lint --workspace=shared"}, "repository": {"type": "git", "url": "git+https://github.com/NextChapterSoftware/unblocked.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/NextChapterSoftware/unblocked/issues"}, "homepage": "https://github.com/NextChapterSoftware/unblocked#readme", "devDependencies": {"@babel/core": "^7.24.4", "@babel/plugin-transform-react-jsx": "^7.23.4", "@babel/preset-env": "^7.24.4", "@esbuild-plugins/node-globals-polyfill": "^0.2.3", "@eslint/compat": "^1.3.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.31.0", "@floating-ui/react": "^0.26.11", "@floating-ui/react-dom": "^2.0.8", "@fortawesome/fontawesome-common-types": "^6.5.2", "@fortawesome/fontawesome-pro": "^6.5.2", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-brands-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/pro-duotone-svg-icons": "^6.5.2", "@fortawesome/pro-light-svg-icons": "^6.5.2", "@fortawesome/pro-regular-svg-icons": "^6.5.2", "@fortawesome/pro-solid-svg-icons": "^6.5.2", "@fortawesome/pro-thin-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.2", "@grpc/grpc-js": "^1.10.6", "@headlessui/react": "^2.2.3", "@isaacs/ttlcache": "^1.4.1", "@js-temporal/polyfill": "^0.4.4", "@modelcontextprotocol/sdk": "^1.11.2", "@prezly/slate-commons": "^0.106.0", "@prezly/slate-lists": "^0.106.0", "@redocly/cli": "^1.16.0", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/nextjs": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/test": "^8.6.14", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^4.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/chrome": "^0.0.266", "@types/classnames": "^2.3.1", "@types/isomorphic-fetch": "^0.0.39", "@types/jest": "^29.5.12", "@types/lru-cache": "^7.10.10", "@types/node": "^22.16.0", "@types/pako": "^2.0.3", "@types/parse-github-url": "^1.0.3", "@types/proper-lockfile": "^4.1.4", "@types/react": "18.3.17", "@types/react-dnd": "^3.0.2", "@types/react-dom": "18.3.5", "@types/recharts": "^1.8.29", "@types/sha1": "^1.1.5", "@types/shelljs": "^0.8.15", "@types/tar": "^6.1.13", "@types/uuid": "^9.0.8", "@types/vscode": "^1.88.0", "@types/vscode-webview": "^1.57.5", "@vercel/webpack-asset-relocator-loader": "^1.7.3", "@vitejs/plugin-react": "^5.0.1", "ajv": "^8.13.0", "autoprefixer": "^10.4.19", "babel-loader": "^9.1.3", "buffer": "^6.0.3", "classnames": "^2.5.1", "copy-webpack-plugin": "^12.0.2", "cross-fetch": "^4.0.0", "css-loader": "^7.1.1", "dayjs": "^1.11.10", "electron-vite": "^4.0.0", "esbuild-loader": "^4.1.0", "eslint": "^9.31.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.32.0", "eslint-plugin-no-barrel-files": "^1.2.2", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "fast-deep-equal": "^3.1.3", "fetch-retry": "^6.0.0", "file-loader": "^6.2.0", "file-type": "^19.0.0", "fork-ts-checker-webpack-plugin": "^9.0.2", "git-revision-webpack-plugin": "^5.0.0", "google-protobuf": "^3.20.0-rc.2", "highlight.js": "^11.9.0", "history": "^5.3.0", "html-webpack-plugin": "^5.6.0", "https-proxy-agent": "^7.0.4", "idb-keyval": "^6.2.1", "input-otp": "^1.4.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-util": "^30.0.5", "jsonc-parser": "^3.3.1", "jwt-decode": "^4.0.0", "mac-system-proxy": "^1.0.4", "memfs": "^4.8.1", "merge-jsons-webpack-plugin": "^2.0.1", "mini-css-extract-plugin": "^2.8.1", "mockdate": "^3.0.5", "next": "^14.2.25", "next-sitemap": "^4.2.3", "node-fetch": "^3.3.2", "node-loader": "^2.0.0", "node-stream-zip": "^1.15.0", "parse-github-url": "^1.0.2", "portfinder": "^1.0.28", "postcss": "^8.4.38", "postcss-cli": "^11.0.0", "postcss-import": "^16.1.0", "postcss-loader": "^8.1.1", "postcss-preset-env": "^9.5.4", "prettier": "^3.2.5", "process": "^0.11.10", "proper-lockfile": "^4.1.2", "react": "18.3.1", "react-day-picker": "^9.8.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "18.3.1", "react-merge-refs": "^2.1.1", "react-refresh": "^0.14.0", "react-router-dom": "^7.6.0", "recharts": "^2.13.3", "sass": "^1.74.1", "sass-loader": "^14.1.1", "selector-observer": "^2.1.6", "semver": "^7.7.1", "sha1": "^1.1.1", "shelljs": "^0.8.5", "shiki": "github:invitetest1/ShikiBundle", "slate": "^0.101.5", "slate-react": "^0.101.6", "source-map-loader": "^5.0.0", "style-loader": "^4.0.0", "stylelint": "^16.3.1", "stylelint-config-standard": "^36.0.0", "stylelint-config-standard-scss": "^13.1.0", "svg-url-loader": "^8.0.0", "tar": "^7.4.3", "terser-webpack-plugin": "^5.3.10", "timers-browserify": "^2.0.12", "tmp-promise": "^3.0.3", "ts-jest": "^29.4.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "type-fest": "^4.15.0", "typescript": "^5.4.4", "typescript-eslint": "^8.37.0", "util": "^0.12.5", "uuid": "^9.0.1", "vite-bundle-analyzer": "^1.2.1", "vite-plugin-checker": "^0.10.2", "vite-plugin-html": "^3.2.2", "vite-plugin-node-polyfills": "^0.24.0", "vite-plugin-pwa": "^1.0.3", "vite-plugin-static-copy": "^3.1.2", "vsce": "^2.15.0", "webextension-polyfill": "^0.10.0", "webpack": "^5.91.0", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4", "webpack-manifest-plugin": "^5.0.0", "webpack-merge": "^5.10.0", "webpack-pwa-manifest": "^4.3.0", "winston": "^3.13.0", "xstream": "npm:@nextchaptersoftware/xstream@^1.0.2", "yargs": "^17.7.2", "zip-webpack-plugin": "^4.0.1", "zod": "^3.24.3"}, "dependencies": {"@types/webextension-polyfill": "^0.10.7", "base64-js": "^1.5.1", "lottie-react": "^2.4.1", "lru-cache": "^10.2.0", "mermaid": "^11.4.1", "npm-check": "^6.0.1", "pako": "^2.1.0", "ts-proto": "^1.171.0", "type-fest": "^4.11.0", "vite": "npm:rolldown-vite@latest"}}