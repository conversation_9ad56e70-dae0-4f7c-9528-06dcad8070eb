#!/bin/bash

usage() {
    echo "usage: runServiceJAR.sh -j <jar>"
    echo "  -j <jar> : Specify the name of the JAR file to execute."
    echo "             If the exact JAR file is not found, the script will"
    echo "             attempt to locate it using a glob pattern matching"
    echo "             '<jar>*.jar' in the current directory."
}

while getopts ":j:" o; do
    case "${o}" in
        j)
            jar=${OPTARG}
            ;;
        *)
            usage
            exit 1
            ;;
    esac
done
shift $((OPTIND-1))

if [ -z "${jar}" ]; then
    usage
    exit 1
fi

# Check if the specified JAR file exists
if [ ! -f "${jar}" ]; then
    # Use shell globbing to find the JAR file
    jars=(./""${jar}""*.jar)

    # Ensure the JAR file exists after searching
    if [ ${#jars[@]} -eq 0 ]; then
        echo "Error: JAR file matching '${jar}' not found!"
        usage
        exit 1
    fi

    # Use the first match
    jar="${jars[0]}"
fi

# https://egahlin.github.io/2022/05/31/improved-ergonomics.html
# exec is required to ensure sigterms are correctly propagated to the java process rather than the parent bash script
exec java \
    "-server" \
    "-XX:+UnlockExperimentalVMOptions" \
    "-XX:MinRAMPercentage=40" \
    "-XX:MaxRAMPercentage=80" \
    "-XX:+UseG1GC" \
    "-XX:MaxGCPauseMillis=100" \
    "-XX:+UseStringDeduplication" \
    "-Dkotlinx.coroutines.io.parallelism=128" \
    "-jar" \
    "${jar}"
