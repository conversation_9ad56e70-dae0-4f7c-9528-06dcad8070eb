root = true

[*]
end_of_line = lf
charset = utf-8
trim_trailing_whitespace = true
insert_final_newline = true


[*.{kt,kts}]
# Comma-separated list of allowed wildcard imports that will override the no-wildcard-imports rule.
# This can be used for allowing wildcard imports from libraries like Ktor where extension functions are used in a way that creates a lot of imports.
# "**" applies to package and all subpackages
ij_kotlin_packages_to_use_import_on_demand = *
ij_kotlin_imports_layout = *

# max-line-length
max_line_length = 150

# Ideally, no experimental rule should be disabled. Ktlint should follow the dogfooding principle. This means that an
# experimental rule should only be added to the master branch no sooner than that this rule has been applied on the
# ktlint code base itself.
# For now, the experimental:argument-list-wrapping still needs to be disabled as it fails the build due to false
# positives. See https://github.com/pinterest/ktlint/pull/1284
ktlint_experimental = enabled
ij_kotlin_allow_trailing_comma = true
ij_kotlin_allow_trailing_comma_on_call_site = true

# Hitting exceptions with this rule
ktlint_standard_function-literal = disabled

# Unnecessary since we are using detekt
ktlint_standard_max-line-length = disabled

# Disable some rules
ktlint_standard_argument-list-wrapping = disabled
ktlint_standard_chain-method-continuation = disabled
ktlint_standard_class-signature = disabled
ktlint_standard_condition-wrapping = disabled
ktlint_standard_function-expression-body = disabled
ktlint_standard_function-signature = disabled
ktlint_standard_indent = disabled
ktlint_standard_kdoc = disabled
ktlint_standard_multiline-expression-wrapping = disabled
ktlint_standard_no-blank-line-in-list = disabled
ktlint_standard_no-consecutive-comments = disabled
ktlint_standard_no-empty-first-line-in-class-body = disabled
ktlint_standard_string-template-indent = disabled
ktlint_standard_trailing-common-on-call-site = disabled
ktlint_standard_trailing-common-on-declaration-site = disabled
ktlint_standard_when-entry-bracing = disabled

# For db models, we do not want to adopt standard property naming
[**/db/models/**.kt]
ktlint_standard_property-naming = disabled

[*.just]
indent_style = space
indent_size = 2
