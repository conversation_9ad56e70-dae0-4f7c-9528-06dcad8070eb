module.exports = {
    theme: {
        // Overriding existing colours
        colors: {
            'space-cadet': {
                100: '#2B345F',
                70: '#6B718F',
                50: '#959AAF',
                30: '#80859F',
                30: '#D5D6DF',
                20: '#D5D6DF',
                10: '#EAEBEF',
                3: '#F9F9FA',
            },
            'sea-foam': {
                100: '#35C7C7',
                hover: '#31B7B7',
                25: '#CCF1F1',
            },
            'blue-crayola': {
                100: '#2874FF',
                30: '#BFD5FF',
                15: '#DFEAFF',
                10: '#E9F1FF',
                6: '#F2F7FF',
                3: '#F9FBFF',
            },
            aquamarine: '#5DD39E',
            cerise: '#E25282',
            mikado: '#DB2763',
            white: {
                100: '#FFFFFF',
                70: 'rgba(255, 255, 255, 0.7)',
                20: 'rgba(255, 255, 255, 0.2)',
                10: 'rgba(255, 255, 255, 0.1)',
            },
            'primary-button-gradient-base': 'rgba(43, 52, 95, 0.3)',
        },
        extend: {
            fontFamily: {
                effra: ['effra', 'sans-serif'],
                source: ['source-sans-3', 'source-code-pro'],
            },
            fontSize: {
                body: '14px',
            },
            padding: {
                '4px': '4px',
                '8px': '8px',
            },
            backgroundImage: {
                'primary-button-disabled': 'linear-gradient(0deg, rgba(43, 52, 95, 0.3), rgba(43, 52, 95, 0.3))',
                'secondary-button-disabled':
                    'linear-gradient(0deg, rgb(249, 249, 250), rgb(249, 249, 250)), linear-gradient(0deg, rgba(43, 52, 95, 0.03), rgba(43, 52, 95, 0.03))',
            },
            lineHeight: {
                body: '17px',
            },
        },
    }
};
