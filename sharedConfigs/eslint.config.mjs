import { globalIgnores } from 'eslint/config';
import simpleImportSort from 'eslint-plugin-simple-import-sort';
import unusedImports from 'eslint-plugin-unused-imports';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import tseslint from 'typescript-eslint';

import { fixupConfigRules } from '@eslint/compat';
import { FlatCompat } from '@eslint/eslintrc';
import js from '@eslint/js';
import tsParser from '@typescript-eslint/parser';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all,
});

export default tseslint.config([
    globalIgnores([
        '**/*.d.ts',
        '**/generated/**/*',
        '**/generatedApi/**/*',
        '**/generatedExtraApi/**/*',
        '.storybook-dist/**/*',
        'build/**/*',
        'dist/**/*',
        'out/**/*',
        '.next/**/*',
        'node_modules/**/*',
        '**/postcss.config.js',
        '**/webpack.*.[t|j]s',
        '**/jest.config.ts',
        '**/prettier.config.js',
        '**/next.config.mjs',
        '**/babel.config.js',
        '**/stylelint.config.js',
        'coverage/**/*',
        '.storybook/**/*',
        'jest.setup.js',
        '.webpack/**/*',
        'forge.config.ts',
        '**/__mocks__/**/*',
        '**/IntercomAPI.js',
        '**/next-sitemap.config.js',
    ]),
    {
        extends: fixupConfigRules(
            compat.extends(
                'prettier',
                'plugin:react-hooks/recommended',
                'plugin:react/recommended',
                'plugin:@typescript-eslint/recommended',
                'plugin:react/jsx-runtime'
            )
        ),

        plugins: {
            // '@typescript-eslint': fixupPluginRules(typescriptEslint),
            'simple-import-sort': simpleImportSort,
            'unused-imports': unusedImports,
        },

        languageOptions: {
            parser: tsParser,
            ecmaVersion: 6,
            sourceType: 'module',

            parserOptions: {
                project: './tsconfig.json',
            },
        },

        settings: {
            react: {
                version: 'detect',
            },
        },

        rules: {
            '@typescript-eslint/no-floating-promises': 'warn',
            'no-unused-vars': 'off',
            '@typescript-eslint/no-unused-vars': 'off',
            'unused-imports/no-unused-imports': 'error',
            'unused-imports/no-unused-vars': 'error',
            '@typescript-eslint/no-explicit-any': 'warn',
            '@typescript-eslint/no-require-imports': 'error',
            '@typescript-eslint/unbound-method': 'error',

            '@typescript-eslint/no-misused-promises': [
                'error',
                {
                    checksVoidReturn: false,
                },
            ],

            '@typescript-eslint/naming-convention': [
                'error',
                {
                    selector: 'function',
                    format: ['PascalCase', 'camelCase'],
                },
            ],

            'react-hooks/exhaustive-deps': [
                'error',
                {
                    additionalHooks:
                        '(useStream|useRightColumnLayout|useRightColumnContent|useDocumentTitle|useApiDataStream|useStickToBottom)',
                },
            ],

            'simple-import-sort/imports': [
                'error',
                {
                    groups: [
                        ['^([a-zA-Z]|@headlessui|@floating|@stripe)'],
                        ['^@(api|models|shared/api)'],
                        ['^@'],
                        ['^@(assets|clientAssets)'],
                        ['^@fortawesome'],
                        ['^\\.'],
                        ['^.s?css$'],
                    ],
                },
            ],

            'no-duplicate-imports': 'error',
            curly: 'error',
            eqeqeq: 'error',
            'no-throw-literal': 'error',
            '@typescript-eslint/no-deprecated': 'error',

            'no-restricted-imports': [
                'error',
                {
                    paths: [
                        '@fortawesome/free-brands-svg-icons',
                        '@fortawesome/free-solid-svg-icons',
                        '@fortawesome/pro-duotone-svg-icons',
                        '@fortawesome/pro-light-svg-icons',
                        '@fortawesome/pro-regular-svg-icons',
                        '@fortawesome/pro-solid-svg-icons',
                        '@fortawesome/pro-thin-svg-icons',
                    ],
                },
            ],

            'react/button-has-type': 'error',

            'no-empty-function': 'off',
            '@typescript-eslint/no-empty-function': 'off',
        },
    },
]);
