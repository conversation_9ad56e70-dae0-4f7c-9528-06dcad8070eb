/**
 * Shared Vite configuration utilities for all client applications
 * This file provides common configuration patterns to maintain consistency
 */

import { execSync } from 'child_process';
import { resolve } from 'path';

export interface EnvironmentVariables {
    APP_TYPE: string;
    ENABLE_STRICT_MODE: string;
    COMMIT_SHA: string;
    PRODUCT_NUMBER: string;
    PRODUCT_VERSION: string;
    'nodeUrl.URL': string;
}

export interface CommonAliases {
    [key: string]: string;
}

/**
 * Creates common path aliases used across all applications
 * @param {string} appDir - The application directory (e.g., '/Users/<USER>/web')
 * @param {string} appName - The application name for app-specific alias (e.g., 'web', 'desktop', 'vscode')
 */
export function createCommonAliases(appDir: string, appName: string): CommonAliases {
    const sharedDir = resolve(appDir, '../shared');

    return {
        // App-specific alias
        [`@${appName}`]: resolve(appDir, 'src'),

        // Shared module aliases
        '@shared': sharedDir,
        '@clientAssets': resolve(sharedDir, 'clientAssets'),
    };
}

/**
 * Environment variable handler for Vite define plugin
 * @param {Object} env - Environment variables from Vite
 * @param {string} appType - Application type ('web', 'desktop', 'vscode', etc.)
 */
export function createEnvironmentVariables(env: Record<string, unknown>, appType: string): EnvironmentVariables {
    return {
        APP_TYPE: JSON.stringify(appType),
        ENABLE_STRICT_MODE: JSON.stringify(env.ENABLE_STRICT_MODE || false),
        COMMIT_SHA: JSON.stringify(process.env.COMMIT_SHA || 'development'),
        PRODUCT_NUMBER: JSON.stringify(env.PRODUCT_NUMBER || process.env.PRODUCT_NUMBER),
        PRODUCT_VERSION: JSON.stringify(env.PRODUCT_VERSION || process.env.PRODUCT_VERSION),
        // @prezly/slate/sdk compatibility fix
        'nodeUrl.URL': 'nodeUrl.Url',
    };
}

/**
 * Git revision utility for getting commit information
 */
export function getGitRevision(): string {
    try {
        return execSync('git rev-parse HEAD').toString().trim();
    } catch (error) {
        console.warn('Could not get git revision:', error);
        return 'development';
    }
}

export default {
    createCommonAliases,
    createEnvironmentVariables,
    getGitRevision,
};
