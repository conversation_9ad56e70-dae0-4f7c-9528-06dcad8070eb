import { webcrypto } from 'node:crypto';
import v8 from 'v8';
import { Stream } from 'xstream';

// Disable debug log output on the commandline when running jest tests
jest.spyOn(console, 'debug').mockImplementation();

const debugLog = <T>() => {
    return (inStream: Stream<T>) => {
        return inStream;
    };
};

// Mock out the Winston logger completely.
jest.mock('../shared/webUtils/log', () => {
    return {
        logger: () => {
            return {
                error: jest.fn(),
                warn: jest.fn(),
                debug: jest.fn(),
                info: jest.fn(),
            };
        },
        debugLog,
    };
});

// Give default crypto impl from node
Object.defineProperty(globalThis, 'crypto', {
    value: webcrypto,
});

// Expose special v8 functions to the JS layer when running tests.
// This lets us manipulate the environment when needed for tests.
// Specifically, this lets us run the v8 GC on demand (see the
// CollectGarbage helper).
v8.setFlagsFromString('--allow-natives-syntax');

jest.useFakeTimers();

export {};
